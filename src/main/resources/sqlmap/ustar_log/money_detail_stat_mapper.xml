<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.slave_mapper.ustar_log.MoneyDetailStatMapper">
    <select id="getTotalCost" resultType="java.lang.Integer">
        select sum(total_cost) from (
        <foreach collection="suffixList" item="tableSuffix" separator="UNION ALL">
            select (-sum(changed)) as total_cost from t_money_detail_${tableSuffix} where uid=#{uid} and changed &lt; 0
        </foreach>) as a
    </select>
</mapper>
