package com.quhong.operation.share.vo;

import com.alibaba.excel.annotation.ExcelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/8/10
 */
public class FridaySendBeansVO implements Serializable {

    private static final long serialVersionUID = -8641776656244797793L;

    @ExcelProperty("bean=2000")
    private String twoThousandBeansRid;

    @ExcelProperty("bean=4000")
    private String fourThousandBeansRid;

    @ExcelProperty("bean=7000")
    private String sevenThousandBeansRid;

    @ExcelProperty("bean=10000")
    private String tenThousandBeansRid;

    public String getTwoThousandBeansRid() {
        return twoThousandBeansRid;
    }

    public void setTwoThousandBeansRid(String twoThousandBeansRid) {
        this.twoThousandBeansRid = twoThousandBeansRid;
    }

    public String getFourThousandBeansRid() {
        return fourThousandBeansRid;
    }

    public void setFourThousandBeansRid(String fourThousandBeansRid) {
        this.fourThousandBeansRid = fourThousandBeansRid;
    }

    public String getSevenThousandBeansRid() {
        return sevenThousandBeansRid;
    }

    public void setSevenThousandBeansRid(String sevenThousandBeansRid) {
        this.sevenThousandBeansRid = sevenThousandBeansRid;
    }

    public String getTenThousandBeansRid() {
        return tenThousandBeansRid;
    }

    public void setTenThousandBeansRid(String tenThousandBeansRid) {
        this.tenThousandBeansRid = tenThousandBeansRid;
    }

    @Override
    public String toString() {
        return "FridaySendBeansVO{" +
                "twoThousandBeansRid=" + twoThousandBeansRid +
                ", fourThousandBeansRid=" + fourThousandBeansRid +
                ", sevenThousandBeansRid=" + sevenThousandBeansRid +
                ", tenThousandBeansRid=" + tenThousandBeansRid +
                '}';
    }
}
