package com.quhong.operation.share.vo;

import com.alibaba.excel.annotation.ExcelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/8/3
 */
public class CompereRoomInfoVO implements Serializable {

    private static final long serialVersionUID = 5072617902694676378L;
    @ExcelProperty("迎新房ID")
    private String id;
    @ExcelProperty("主持人")
    private String compereName;
    @ExcelProperty("在麦时长")
    private Integer micTime = 0;
    @ExcelProperty("新用户进房人数")
    private Integer newActorJoinRoomPerson = 0;
    @ExcelProperty("新用户进房次数")
    private Integer newActorJoinRoomCount = 0;
    @ExcelProperty("文字聊天人数")
    private Integer chatPerson = 0;
    @ExcelProperty("文字聊天次数")
    private Integer chatCount = 0;
    @ExcelProperty("新用户上麦人数")
    private Integer newActorMicPerson = 0;
    @ExcelProperty("新用户上麦次数")
    private Integer newActorMicCount = 0;
    @ExcelProperty("新用户平均上麦时长")
    private Double newActorMicAvg = 0.00;
    @ExcelProperty("新用户上麦5分钟人数")
    private Integer newActorMic5Minute = 0;
    @ExcelProperty("关注房间人数")
    private Integer followRoomPerson = 0;
    @ExcelProperty("关注房间次数")
    private Integer followRoomCount = 0;
    @ExcelProperty("用户平均停留时长")
    private Double avgInRoomTime = 0.00;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCompereName() {
        return compereName;
    }

    public void setCompereName(String compereName) {
        this.compereName = compereName;
    }

    public Integer getMicTime() {
        return micTime;
    }

    public void setMicTime(Integer micTime) {
        this.micTime = micTime;
    }

    public Integer getNewActorJoinRoomPerson() {
        return newActorJoinRoomPerson;
    }

    public void setNewActorJoinRoomPerson(Integer newActorJoinRoomPerson) {
        this.newActorJoinRoomPerson = newActorJoinRoomPerson;
    }

    public Integer getNewActorJoinRoomCount() {
        return newActorJoinRoomCount;
    }

    public void setNewActorJoinRoomCount(Integer newActorJoinRoomCount) {
        this.newActorJoinRoomCount = newActorJoinRoomCount;
    }

    public Integer getChatPerson() {
        return chatPerson;
    }

    public void setChatPerson(Integer chatPerson) {
        this.chatPerson = chatPerson;
    }

    public Integer getChatCount() {
        return chatCount;
    }

    public void setChatCount(Integer chatCount) {
        this.chatCount = chatCount;
    }

    public Integer getNewActorMicPerson() {
        return newActorMicPerson;
    }

    public void setNewActorMicPerson(Integer newActorMicPerson) {
        this.newActorMicPerson = newActorMicPerson;
    }

    public Integer getNewActorMicCount() {
        return newActorMicCount;
    }

    public void setNewActorMicCount(Integer newActorMicCount) {
        this.newActorMicCount = newActorMicCount;
    }

    public Double getNewActorMicAvg() {
        return newActorMicAvg;
    }

    public void setNewActorMicAvg(Double newActorMicAvg) {
        this.newActorMicAvg = newActorMicAvg;
    }

    public Integer getNewActorMic5Minute() {
        return newActorMic5Minute;
    }

    public void setNewActorMic5Minute(Integer newActorMic5Minute) {
        this.newActorMic5Minute = newActorMic5Minute;
    }

    public Integer getFollowRoomPerson() {
        return followRoomPerson;
    }

    public void setFollowRoomPerson(Integer followRoomPerson) {
        this.followRoomPerson = followRoomPerson;
    }

    public Integer getFollowRoomCount() {
        return followRoomCount;
    }

    public void setFollowRoomCount(Integer followRoomCount) {
        this.followRoomCount = followRoomCount;
    }

    public Double getAvgInRoomTime() {
        return avgInRoomTime;
    }

    public void setAvgInRoomTime(Double avgInRoomTime) {
        this.avgInRoomTime = avgInRoomTime;
    }

    @Override
    public String toString() {
        return "CompereRoomInfoVO{" +
                "id='" + id + '\'' +
                ", compereName='" + compereName + '\'' +
                ", micTime=" + micTime +
                ", newActorJoinRoomPerson=" + newActorJoinRoomPerson +
                ", newActorJoinRoomCount=" + newActorJoinRoomCount +
                ", chatPerson=" + chatPerson +
                ", chatCount=" + chatCount +
                ", newActorMicPerson=" + newActorMicPerson +
                ", newActorMicCount=" + newActorMicCount +
                ", newActorMicAvg=" + newActorMicAvg +
                ", newActorMic5Minute=" + newActorMic5Minute +
                ", followRoomPerson=" + followRoomPerson +
                ", followRoomCount=" + followRoomCount +
                ", avgInRoomTime=" + avgInRoomTime +
                '}';
    }
}
