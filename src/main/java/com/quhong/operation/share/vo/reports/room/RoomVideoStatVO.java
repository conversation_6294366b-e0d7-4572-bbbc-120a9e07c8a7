package com.quhong.operation.share.vo.reports.room;

import com.quhong.operation.share.vo.AbstractDateVO;

public class RoomVideoStatVO extends AbstractDateVO {

    /**
     * 开启视频房人数
     */
    private int opVideoUserCount;

    /**
     * 开启视屏房次数
     */
    private int opVideoCount;

    /**
     * 高峰期平均在线视频房数量
     */
    private int peakAvgRoomCount;

    /**
     * 进视频房总人数
     */
    private int enterUserCount;

    /**
     * 进视频房总次数
     */
    private int enterCount;

    /**
     * 平均播放视频个数
     */
    private int avgPlayVideoCount;

    /**
     * 高峰期视频房平均在线时长
     */
    private int peakAvgRoomTime;

    /**
     * 高峰期视屏房平均在线人数
     */
    private int peakAvgUserCount;

    /**
     * 观看一分钟一下人数
     */
    private int ltOneMinUserCount;

    /**
     * 观看1-5分钟人数
     */
    private int ltFiveMinUserCount;

    /**
     * 观看5-10分钟人数
     */
    private int ltTenMinUserCount;

    /**
     * 观看10-20分钟人数
     */
    private int ltTwentyMinUserCount;

    /**
     * 观看20-30分钟人数
     */
    private int ltThirtyMinUserCount;

    /**
     * 观看30分钟以上人数
     */
    private int gtThirtyMinUserCount;

    public int getOpVideoUserCount() {
        return opVideoUserCount;
    }

    public void setOpVideoUserCount(int opVideoUserCount) {
        this.opVideoUserCount = opVideoUserCount;
    }

    public int getOpVideoCount() {
        return opVideoCount;
    }

    public void setOpVideoCount(int opVideoCount) {
        this.opVideoCount = opVideoCount;
    }

    public int getPeakAvgRoomCount() {
        return peakAvgRoomCount;
    }

    public void setPeakAvgRoomCount(int peakAvgRoomCount) {
        this.peakAvgRoomCount = peakAvgRoomCount;
    }

    public int getEnterUserCount() {
        return enterUserCount;
    }

    public void setEnterUserCount(int enterUserCount) {
        this.enterUserCount = enterUserCount;
    }

    public int getEnterCount() {
        return enterCount;
    }

    public void setEnterCount(int enterCount) {
        this.enterCount = enterCount;
    }

    public int getAvgPlayVideoCount() {
        return avgPlayVideoCount;
    }

    public void setAvgPlayVideoCount(int avgPlayVideoCount) {
        this.avgPlayVideoCount = avgPlayVideoCount;
    }

    public int getPeakAvgRoomTime() {
        return peakAvgRoomTime;
    }

    public void setPeakAvgRoomTime(int peakAvgRoomTime) {
        this.peakAvgRoomTime = peakAvgRoomTime;
    }

    public int getPeakAvgUserCount() {
        return peakAvgUserCount;
    }

    public void setPeakAvgUserCount(int peakAvgUserCount) {
        this.peakAvgUserCount = peakAvgUserCount;
    }

    public int getLtOneMinUserCount() {
        return ltOneMinUserCount;
    }

    public void setLtOneMinUserCount(int ltOneMinUserCount) {
        this.ltOneMinUserCount = ltOneMinUserCount;
    }

    public int getLtFiveMinUserCount() {
        return ltFiveMinUserCount;
    }

    public void setLtFiveMinUserCount(int ltFiveMinUserCount) {
        this.ltFiveMinUserCount = ltFiveMinUserCount;
    }

    public int getLtTenMinUserCount() {
        return ltTenMinUserCount;
    }

    public void setLtTenMinUserCount(int ltTenMinUserCount) {
        this.ltTenMinUserCount = ltTenMinUserCount;
    }

    public int getLtTwentyMinUserCount() {
        return ltTwentyMinUserCount;
    }

    public void setLtTwentyMinUserCount(int ltTwentyMinUserCount) {
        this.ltTwentyMinUserCount = ltTwentyMinUserCount;
    }

    public int getLtThirtyMinUserCount() {
        return ltThirtyMinUserCount;
    }

    public void setLtThirtyMinUserCount(int ltThirtyMinUserCount) {
        this.ltThirtyMinUserCount = ltThirtyMinUserCount;
    }

    public int getGtThirtyMinUserCount() {
        return gtThirtyMinUserCount;
    }

    public void setGtThirtyMinUserCount(int gtThirtyMinUserCount) {
        this.gtThirtyMinUserCount = gtThirtyMinUserCount;
    }

    @Override
    public String toString() {
        return "RoomVideoStatVO{" +
                "opVideoUserCount=" + opVideoUserCount +
                ", opVideoCount=" + opVideoCount +
                ", peakAvgRoomCount=" + peakAvgRoomCount +
                ", enterUserCount=" + enterUserCount +
                ", enterCount=" + enterCount +
                ", avgPlayVideoCount=" + avgPlayVideoCount +
                ", peakAvgRoomTime=" + peakAvgRoomTime +
                ", peakAvgUserCount=" + peakAvgUserCount +
                ", ltOneMinUserCount=" + ltOneMinUserCount +
                ", ltFiveMinUserCount=" + ltFiveMinUserCount +
                ", ltTenMinUserCount=" + ltTenMinUserCount +
                ", ltTwentyMinUserCount=" + ltTwentyMinUserCount +
                ", ltThirtyMinUserCount=" + ltThirtyMinUserCount +
                ", gtThirtyMinUserCount=" + gtThirtyMinUserCount +
                ", date='" + date + '\'' +
                '}';
    }
}
