package com.quhong.operation.share.vo.reports.money.detail;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 总明细
 */
public class AllMoneyDetailVO {

    @ExcelProperty("from_rid")
    private String fromRid;
    @ExcelProperty("after_rid")
    private int afterRid;
    @ExcelProperty("总充值")
    private int totalRecharge;
    @ExcelProperty("admin减钻")
    private int adminSubBeans;
    @ExcelProperty("转盘游戏")
    private int dialGame;
    @ExcelProperty("购买消费")
    private int buyConsume;
    @ExcelProperty("签到")
    private int signIn;
    @ExcelProperty("收发礼物")
    private int getSendGift;
    @ExcelProperty("收发红包")
    private int getSendRedPacket;
    @ExcelProperty("pk游戏")
    private int pkGame;
    @ExcelProperty("vip 充值")
    private int vipRecharge;
    @ExcelProperty("房间消费返钻")
    private int roomConsumeFeedback;
    @ExcelProperty("vip奖励送钻")
    private int vipAward;
    @ExcelProperty("个人消费返钻")
    private int consumeFeedback;
    @ExcelProperty("活动奖励收入")
    private int activityAward;
    @ExcelProperty("老虎机总消耗")
    private int tigerMachineGame;
    @ExcelProperty("猜拳游戏总消耗")
    private int fingerGame;
    @ExcelProperty("新色子游戏")
    private int newDiceGame;
    @ExcelProperty("其他")
    private int other;
    @ExcelProperty("总计")
    private int totalConsume;
    @ExcelProperty("is_valid")
    private String isValid;
    @ExcelProperty("ban_reason")
    private String banReason;
    @ExcelProperty("ban_mtime")
    private String banMtime;

    public String getFromRid() {
        return fromRid;
    }

    public void setFromRid(String fromRid) {
        this.fromRid = fromRid;
    }

    public int getAfterRid() {
        return afterRid;
    }

    public void setAfterRid(int afterRid) {
        this.afterRid = afterRid;
    }

    public int getTotalRecharge() {
        return totalRecharge;
    }

    public void setTotalRecharge(int totalRecharge) {
        this.totalRecharge = totalRecharge;
    }

    public int getAdminSubBeans() {
        return adminSubBeans;
    }

    public void setAdminSubBeans(int adminSubBeans) {
        this.adminSubBeans = adminSubBeans;
    }

    public int getDialGame() {
        return dialGame;
    }

    public void setDialGame(int dialGame) {
        this.dialGame = dialGame;
    }

    public int getBuyConsume() {
        return buyConsume;
    }

    public void setBuyConsume(int buyConsume) {
        this.buyConsume = buyConsume;
    }

    public int getSignIn() {
        return signIn;
    }

    public void setSignIn(int signIn) {
        this.signIn = signIn;
    }

    public int getGetSendGift() {
        return getSendGift;
    }

    public void setGetSendGift(int getSendGift) {
        this.getSendGift = getSendGift;
    }

    public int getGetSendRedPacket() {
        return getSendRedPacket;
    }

    public void setGetSendRedPacket(int getSendRedPacket) {
        this.getSendRedPacket = getSendRedPacket;
    }

    public int getPkGame() {
        return pkGame;
    }

    public void setPkGame(int pkGame) {
        this.pkGame = pkGame;
    }

    public int getVipRecharge() {
        return vipRecharge;
    }

    public void setVipRecharge(int vipRecharge) {
        this.vipRecharge = vipRecharge;
    }

    public int getRoomConsumeFeedback() {
        return roomConsumeFeedback;
    }

    public void setRoomConsumeFeedback(int roomConsumeFeedback) {
        this.roomConsumeFeedback = roomConsumeFeedback;
    }

    public int getVipAward() {
        return vipAward;
    }

    public void setVipAward(int vipAward) {
        this.vipAward = vipAward;
    }

    public int getConsumeFeedback() {
        return consumeFeedback;
    }

    public void setConsumeFeedback(int consumeFeedback) {
        this.consumeFeedback = consumeFeedback;
    }

    public int getActivityAward() {
        return activityAward;
    }

    public void setActivityAward(int activityAward) {
        this.activityAward = activityAward;
    }

    public int getTigerMachineGame() {
        return tigerMachineGame;
    }

    public void setTigerMachineGame(int tigerMachineGame) {
        this.tigerMachineGame = tigerMachineGame;
    }

    public int getFingerGame() {
        return fingerGame;
    }

    public void setFingerGame(int fingerGame) {
        this.fingerGame = fingerGame;
    }

    public int getNewDiceGame() {
        return newDiceGame;
    }

    public void setNewDiceGame(int newDiceGame) {
        this.newDiceGame = newDiceGame;
    }

    public int getOther() {
        return other;
    }

    public void setOther(int other) {
        this.other = other;
    }

    public int getTotalConsume() {
        return totalConsume;
    }

    public void setTotalConsume(int totalConsume) {
        this.totalConsume = totalConsume;
    }

    public String getIsValid() {
        return isValid;
    }

    public void setIsValid(String isValid) {
        this.isValid = isValid;
    }

    public String getBanReason() {
        return banReason;
    }

    public void setBanReason(String banReason) {
        this.banReason = banReason;
    }

    public String getBanMtime() {
        return banMtime;
    }

    public void setBanMtime(String banMtime) {
        this.banMtime = banMtime;
    }

    public Integer sum() {
        Integer sum = totalRecharge + dialGame + pkGame + other + tigerMachineGame + roomConsumeFeedback + fingerGame
                + newDiceGame + adminSubBeans + buyConsume + vipRecharge + getSendGift + getSendRedPacket + vipAward
                + signIn + activityAward + consumeFeedback;
        return sum;

    }
}
