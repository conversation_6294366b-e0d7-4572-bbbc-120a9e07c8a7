package com.quhong.operation.share.vo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/3/15
 */
public class CouponUseRecordVO {

    /**
     * 用户uid
     */
    private String aid;
    /**
     *  用户原始id
     */
    private Integer rid;

    /**
     * 用户id
     */
    private String strRid;

    /**
     * 优惠劵ID
     */
    private Integer couponId;

    /**
     * 额外奖励
     */
    private BigDecimal extraProp;

    /**
     * 有效期
     */
    private String strValidDay;

    /**
     * 剩余有效期
     */
    private Integer leftValidDay;

    /**
     * 标题
     */
    private String title;

    /**
     * 来源
     */
    private String source;

    /**
     * 获得时间
     */
    private Integer getTime;

    /**
     * 状态 -1全部 0未使用 1已使用 2已过期
     */
    private Integer status;

    /**
     * 充值数量
     */
    private String rechargeNum;

    /**
     * 订单ID
     */
    private String orderId;

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getStrRid() {
        return strRid;
    }

    public void setStrRid(String strRid) {
        this.strRid = strRid;
    }

    public Integer getCouponId() {
        return couponId;
    }

    public void setCouponId(Integer couponId) {
        this.couponId = couponId;
    }

    public BigDecimal getExtraProp() {
        return extraProp;
    }

    public void setExtraProp(BigDecimal extraProp) {
        this.extraProp = extraProp;
    }

    public String getStrValidDay() {
        return strValidDay;
    }

    public void setStrValidDay(String strValidDay) {
        this.strValidDay = strValidDay;
    }

    public Integer getLeftValidDay() {
        return leftValidDay;
    }

    public void setLeftValidDay(Integer leftValidDay) {
        this.leftValidDay = leftValidDay;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Integer getGetTime() {
        return getTime;
    }

    public void setGetTime(Integer getTime) {
        this.getTime = getTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRechargeNum() {
        return rechargeNum;
    }

    public void setRechargeNum(String rechargeNum) {
        this.rechargeNum = rechargeNum;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }
}
