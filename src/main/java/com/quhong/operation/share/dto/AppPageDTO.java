package com.quhong.operation.share.dto;


public class AppPageDTO {
    private String docId;
    private String name;
    private int status;
    private int startTime;
    private int endTime;
    private String url;
    private String urlAr;
    private String iphoneXUrlEn;
    private String iphoneXUrlAr;
    private String link;
    private int actionType;
    private String targetId;
    private int sortNum;
    private int ctime;
    private int mtime;
    private int filterType;  // 过滤展示条件: 0: 不过滤 1: 性别过滤 2: 付费过滤 3: 注册天数过滤  4: 指定用户展示
    private String filterItem;  // 相对于filterType的过滤具体条件

    public String getDocId() {
        return docId;
    }

    public void setDocId(String docId) {
        this.docId = docId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getStartTime() {
        return startTime;
    }

    public void setStartTime(int startTime) {
        this.startTime = startTime;
    }

    public int getEndTime() {
        return endTime;
    }

    public void setEndTime(int endTime) {
        this.endTime = endTime;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUrlAr() {
        return urlAr;
    }

    public void setUrlAr(String urlAr) {
        this.urlAr = urlAr;
    }

    public String getIphoneXUrlEn() {
        return iphoneXUrlEn;
    }

    public void setIphoneXUrlEn(String iphoneXUrlEn) {
        this.iphoneXUrlEn = iphoneXUrlEn;
    }

    public String getIphoneXUrlAr() {
        return iphoneXUrlAr;
    }

    public void setIphoneXUrlAr(String iphoneXUrlAr) {
        this.iphoneXUrlAr = iphoneXUrlAr;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public int getActionType() {
        return actionType;
    }

    public void setActionType(int actionType) {
        this.actionType = actionType;
    }

    public String getTargetId() {
        return targetId;
    }

    public void setTargetId(String targetId) {
        this.targetId = targetId;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public int getMtime() {
        return mtime;
    }

    public void setMtime(int mtime) {
        this.mtime = mtime;
    }

    public int getFilterType() {
        return filterType;
    }

    public void setFilterType(int filterType) {
        this.filterType = filterType;
    }

    public String getFilterItem() {
        return filterItem;
    }

    public void setFilterItem(String filterItem) {
        this.filterItem = filterItem;
    }
}
