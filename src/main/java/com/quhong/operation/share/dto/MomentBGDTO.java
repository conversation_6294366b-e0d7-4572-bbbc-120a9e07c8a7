package com.quhong.operation.share.dto;

/**
 * 背景的配置
 * @date 2022/10/19
 */
public class MomentBGDTO {

    private Integer id;  // 更新时用到
    private String nameEn;   // 背景英语
    private String nameAr;   // 背景阿语
    private String backgroundType; // 背景类型
    private String fontColor;   // 字体颜色
    private String imageUrl;       // 背景url
    private Integer status;        // 背景状态: 0: 无效  1: 有效  -2 已删除
    private Integer costType;      //  扣费类型 0 免费 1: 钻石  2: 心心
    private Integer price;         //  价格
    private Integer sortNum;         //  排序

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public String getBackgroundType() {
        return backgroundType;
    }

    public void setBackgroundType(String backgroundType) {
        this.backgroundType = backgroundType;
    }

    public String getFontColor() {
        return fontColor;
    }

    public void setFontColor(String fontColor) {
        this.fontColor = fontColor;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCostType() {
        return costType;
    }

    public void setCostType(Integer costType) {
        this.costType = costType;
    }

    public Integer getPrice() {
        return price;
    }

    public void setPrice(Integer price) {
        this.price = price;
    }

    public Integer getSortNum() {
        return sortNum;
    }

    public void setSortNum(Integer sortNum) {
        this.sortNum = sortNum;
    }
}
