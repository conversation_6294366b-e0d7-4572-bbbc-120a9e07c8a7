package com.quhong.operation.share.dto;

public class FriendTopicAddDTO {

    /**
     * 英文话题
     */
    private String topicEnName;

    /**
     * 英语排序号
     */
    private Integer enOrder;

    /**
     * 阿语话题
     */
    private String topicArName;

    /**
     * 阿语排序号
     */
    private Integer arOrder;

    /**
     * 状态: 1 有效 2 无效
     */
    private Integer status;

    public String getTopicEnName() {
        return topicEnName;
    }

    public void setTopicEnName(String topicEnName) {
        this.topicEnName = topicEnName;
    }

    public String getTopicArName() {
        return topicArName;
    }

    public void setTopicArName(String topicArName) {
        this.topicArName = topicArName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getEnOrder() {
        return enOrder;
    }

    public void setEnOrder(Integer enOrder) {
        this.enOrder = enOrder;
    }

    public Integer getArOrder() {
        return arOrder;
    }

    public void setArOrder(Integer arOrder) {
        this.arOrder = arOrder;
    }

    @Override
    public String toString() {
        return "FriendTopicAddDTO{" +
                "topicEnName='" + topicEnName + '\'' +
                ", enOrder=" + enOrder +
                ", topicArName='" + topicArName + '\'' +
                ", arOrder=" + arOrder +
                ", status=" + status +
                '}';
    }
}
