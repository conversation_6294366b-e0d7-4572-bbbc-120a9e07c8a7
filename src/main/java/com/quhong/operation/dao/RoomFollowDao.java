package com.quhong.operation.dao;

import com.quhong.mysql.slave_mapper.ustar_log.RoomFollowMapper;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/8/4
 * 废弃原因，数据源没有数据
 */
@Component
@Deprecated
public class RoomFollowDao {

    private final static Logger logger = LoggerFactory.getLogger(RoomFollowDao.class);
    @Autowired
    private RoomFollowMapper roomFollowMapper;

    /**
     * 统计用户在某段时间内关注房间的次数
     * 没有对同一个房间去重
     * @param startTime 开始时间
     * @param endTime 结尾时间
     * @param userId 用户id
     * @return 次数
     */
    @Deprecated
    public ApiResult<Integer> actorFollowRoomCount(Integer startTime, Integer endTime, String userId) {
        ApiResult<Integer> result = new ApiResult<>();
        List<String> tableSuffixArr = DateHelper.ARABIAN.getTableSuffixArr(startTime, endTime);
        Integer count = 0;
        for (String tableSuffix : tableSuffixArr) {
            Integer num = roomFollowMapper.actorFollowRoomCount(tableSuffix, startTime, endTime, userId);
            if (null != num && num > 0)
                count += num;
        }
        logger.info("param start={} end={} userId={} actorFollowRoomCount count={}",
                startTime, endTime, userId, count);

        return result.ok(count);
    }

    /**
     * 一段时间内关注该房间的人数
     * @param startTime 开始时间
     * @param endTime 结尾时间
     * @param roomId 房间rid
     * @return 人数
     */
    @Deprecated
    public ApiResult<Integer> roomFollowPerson(Integer startTime, Integer endTime, String roomId) {
        ApiResult<Integer> result = new ApiResult<>();
        List<String> tableSuffixArr = DateHelper.ARABIAN.getTableSuffixArr(startTime, endTime);
        Set<String> set = new HashSet<>();
        for (String tableSuffix : tableSuffixArr) {
            List<String> list = roomFollowMapper.roomFollowPerson(tableSuffix, startTime, endTime, roomId);
            if (!CollectionUtils.isEmpty(list)) {
                set.addAll(list);
            }
        }
        logger.info("param start={} end={} userId={} roomFollowPerson count={}",
                startTime, endTime, roomId, set.size());

        return result.ok(set.size());
    }

    /**
     * 一段时间内关注该房间的次数
     * @param startTime 开始时间
     * @param endTime 结尾时间
     * @param roomId 房间rid
     * @return 次数
     */
    @Deprecated
    public ApiResult<Integer> roomFollowCount(Integer startTime, Integer endTime, String roomId) {
        ApiResult<Integer> result = new ApiResult<>();
        List<String> tableSuffixArr = DateHelper.ARABIAN.getTableSuffixArr(startTime, endTime);
        Integer count = 0;
        for (String tableSuffix : tableSuffixArr) {
            Integer num = roomFollowMapper.roomFollowCount(tableSuffix, startTime, endTime, roomId);
            if (null != num && num > 0)
                count += num;
        }
        logger.info("param start={} end={} userId={} roomFollowCount count={}",
                startTime, endTime, roomId, count);

        return result.ok(count);
    }

}
