package com.quhong.operation.dao;

import com.quhong.datas.DayTimeData;
import com.quhong.mysql.slave_mapper.ustar_log.DauMapper;
import com.quhong.operation.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Set;

@Component
public class DauStatDao {

    private static final Logger logger = LoggerFactory.getLogger(DauStatDao.class);

    @Autowired
    private DauMapper dauMapper;

    /**
     * 获取在线用户数
     * @param channel 渠道 ; 安卓、ios
     * @param start 开始时间戳 s/秒
     * @param end 结束时间戳 s/秒
     * @param uidSet 用户数组
     * @return
     */
    public int findOnlineUserCount(String channel, int start, int end, Set<String> uidSet){
        int os = 0;
        switch (channel){
            case "Android":
                os = 0;
                break;
            case "iOS":
                os = 1;
                break;
            default:
                os = -1;
                break;
        }
        String tableSuffix = DateHelper.ARABIAN.getTableSuffixByDate(new Date(start * 1000L));
        return dauMapper.selectOnlineUserCount(start, end, os, uidSet, tableSuffix);
    }

    /**
     * 通过日期获取dau
     */
    public int getDauByDay(int os, DayTimeData dayTimeData, Set<String> uidSet){
        int start = dayTimeData.getTime();
        int end = dayTimeData.getEndTime();
        String tableSuffix = DateHelper.ARABIAN.getTableSuffixByDate(new Date(start * 1000L));
        return dauMapper.selectOnlineUserCount(start, end, os, uidSet, tableSuffix);
    }

}
