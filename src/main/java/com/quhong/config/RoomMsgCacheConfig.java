package com.quhong.config;

import com.quhong.msg.MarsCacheMsg;
import com.quhong.msg.obj.MsgInfoObject;
import com.quhong.msg.room.SendRoomPushMsg;
import com.quhong.net.cache.CacheMsgData;
import com.quhong.net.cache.RoomMsgCache;
import com.quhong.net.sender.RoomMsgSender;
import com.quhong.player.cache.Player;
import com.quhong.room.processors.RoomMsgProcessor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class RoomMsgCacheConfig extends MsgSendConfig {
    @Bean
    public RoomMsgSender getRoomMsgSender(){
        return new RoomMsgSender();
    }

    @Bean
    public RoomMsgCache getRoomMsgCache(){
        return new RoomMsgCache() {
            @Override
            protected void doReSendMsgList(Player player, String roomId, List<CacheMsgData> sendList) {
                SendRoomPushMsg msg = new SendRoomPushMsg();
                msg.getProtoHeader().setRoomID(roomId);
                long lastMsgId = 0;
                for(CacheMsgData msgData : sendList){
                    if(msgData.getMsg() instanceof  SendRoomPushMsg){
                        SendRoomPushMsg src = (SendRoomPushMsg)msgData.getMsg();
                        for(MsgInfoObject infoObject : src.getMsgList()){
                            msg.add(infoObject);
                            lastMsgId = src.getMsgId();
                        }
                    }else{
                        msgSender.sendMsg(player.getUid(), msg);
                    }
                }
                if(lastMsgId > 0){
                    msg.setRoomId(roomId);
                    msg.getHeader().setMsgId(lastMsgId);
                    msg.getProtoHeader().setRoomID(roomId);
                    msg.getProtoHeader().setMsgId(lastMsgId);
                    msgSender.reSendMsg(player.getUid(), msg);
                }
            }
        };
    }
}
