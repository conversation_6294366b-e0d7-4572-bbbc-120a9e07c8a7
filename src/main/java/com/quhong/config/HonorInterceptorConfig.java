package com.quhong.config;

import com.quhong.intercepters.H5InterceptorConfig;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/21
 */
@Configuration
public class HonorInterceptorConfig extends H5InterceptorConfig {

    protected List<String> getExcludePaths() {
        return Arrays.asList(baseUrl + "daily_recharge_ranking", baseUrl + "refund_test",
                baseUrl + "first_recharge/info");
    }

}
