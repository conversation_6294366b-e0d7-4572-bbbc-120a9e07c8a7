package com.quhong.config;

import com.quhong.ludo.data.CurrencyInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.Map;


@Component
@PropertySource(value = "classpath:ludo_config.yml", encoding = "UTF-8", factory = YamlPropertySourceFactory.class)
@ConfigurationProperties(prefix = "data")
public class LudoConfig {
    private static final Logger logger = LoggerFactory.getLogger(LudoConfig.class);

    private Map<Integer, CurrencyInfo> currencyInfoMap;
    private Map<String, String> countryFlagMap;

    public Map<Integer, CurrencyInfo> getCurrencyInfoMap() {
        return currencyInfoMap;
    }

    public void setCurrencyInfoMap(Map<Integer, CurrencyInfo> currencyInfoMap) {
        this.currencyInfoMap = currencyInfoMap;
    }

    public Map<String, String> getCountryFlagMap() {
        return countryFlagMap;
    }

    public void setCountryFlagMap(Map<String, String> countryFlagMap) {
        this.countryFlagMap = countryFlagMap;
    }

    public int getCurrencyById(int id) {
        try {
            return currencyInfoMap.get(id).getCurrencyValue();
        } catch (Exception e) {
            logger.error("get currency by id error id={} error message={}", id, e.getMessage());
        }
        return 0;
    }
}
