package com.quhong.config;

import com.quhong.core.config.ServerConfig;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@Configuration
public class MomentConfig {
    private Set<String> POWER_USER = null;
    private Set<String> OFFICIAL_USER = null;

    @PostConstruct
    private void postInit() {
        if (ServerConfig.isProduct()) {
            // 2996881,1181104,2133501,1950844,2702940,2939241,2940648
            POWER_USER = new HashSet<>(Arrays.asList("5cc2797c66dc630025bf17c2", "5a1b742a1bad482144e8e099",
                    "5b0737441bad485d2389e679", "5ade03ef1bad48aa748aa0ee", "5bbe240a66dc63002e3f1fe0",
                    "5c88a0f166dc630038467c4e", "5c89f2ae66dc63003846826e"));
            OFFICIAL_USER = new HashSet<>(Arrays.asList("5c9c260866dc630043ca9995", "594a9c2c1bad4838a47d9a92",
                    "5cdf784961d047a4adf44064", "635e7fa60adfa9310ea6ddac"));
        } else {
            POWER_USER = new HashSet<>(Arrays.asList("5c2c2dec644f8e002a6aaac4", "5cb7eef8644f8e002a90cfc6",
                    "5cd550f0644f8e0030454ef9", "6006a874644f8e00374fca1d", "5e815ad9644f8e00320bf3f4",
                    "5cc19637644f8e0030454ef6", "6290b9478901aade68f17674", "643530db4fc4f9265e368cdd"));
            OFFICIAL_USER = new HashSet<>(Arrays.asList("5c2c2dec644f8e002a6aaac4", "5cb7eef8644f8e002a90cfc6", "6284b26c089842c3c3879265", "5e815ad9644f8e00320bf3f4",
                    "5cdf784961d047a4adf44064", "6006a874644f8e00374fca1d", "5e815ad9644f8e00320bf3f4",
                    "5d70afc4644f8e001e4e64cb", "6290b9478901aade68f17674", "655c4c24b661b86b85455f3b",
                    "653a3fa5a59a617df5d4148f", "65b71f30ea494915d29c40b9"));
        }
    }

    public Set<String> getPOWER_USER() {
        return POWER_USER;
    }

    public void setPOWER_USER(Set<String> POWER_USER) {
        this.POWER_USER = POWER_USER;
    }

    public Set<String> getOFFICIAL_USER() {
        return OFFICIAL_USER;
    }

    public void setOFFICIAL_USER(Set<String> OFFICIAL_USER) {
        this.OFFICIAL_USER = OFFICIAL_USER;
    }
}
