package com.quhong.config;

import com.quhong.api.ApiMomentService;
import com.quhong.api.MsgService;
import com.quhong.constant.ServiceConstant;
import com.quhong.init.DubboServiceInit;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;


@Component
public class ServiceInit implements DubboServiceInit {

    @DubboReference(lazy = true, providedBy = ServiceConstant.MSG, connections = 2, reconnect = "false", retries = 0)
    private MsgService msgService;

    @DubboReference(lazy = true, providedBy = ServiceConstant.MOMENT, connections = 1, reconnect = "false", retries = 0)
    private ApiMomentService apiMomentService;
}
