package com.quhong.config;

import com.quhong.constant.DataResourcesConstant;
import com.quhong.enums.NoviceTaskMqConstant;
import com.quhong.mq.MqSenderService;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.RabbitListenerContainerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MQConfig {

    @Bean("resourcesListenerFactory")
    public RabbitListenerContainerFactory resourcesListenerFactory(ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory containerFactory = new SimpleRabbitListenerContainerFactory();
        containerFactory.setConnectionFactory(connectionFactory);
        // 消费者需要手动确认消息
        containerFactory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        containerFactory.setMaxConcurrentConsumers(2);
        containerFactory.setPrefetchCount(1);
        return containerFactory;
    }
//
//    @Bean
//    public Queue noviceTaskQueue() {
//        return new Queue(NoviceTaskMqConstant.DATA_RESOURCES_QUEUE, true);
//    }
//
//    @Bean
//    public FanoutExchange noviceTaskExchange() {
//        return new FanoutExchange(NoviceTaskMqConstant.FOUNT_NAME, false, false);
//    }
//
//    @Bean
//    Binding bindingNoviceExchange(Queue noviceTaskQueue, FanoutExchange noviceTaskExchange) {
//        return BindingBuilder.bind(noviceTaskQueue).to(noviceTaskExchange);
//    }


    @Bean
    public Queue resTopicQueue() {
        return new Queue(DataResourcesConstant.RESOURCES_TOPIC_QUEUE, true);
    }


    @Bean
    public TopicExchange topicCommonExchange() {
        return new TopicExchange(MqSenderService.TOPIC_COMMON_EXCHANGE, false, false);
    }

    @Bean
    Binding bindingExchangeUserRoomMsg(Queue resTopicQueue, TopicExchange topicCommonExchange) {
        return BindingBuilder.bind(resTopicQueue).to(topicCommonExchange).with(MqSenderService.ROUTE_KEY_USER_ENTER_ROOM);
    }


}
