package com.quhong.config;

import com.quhong.chain.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

@Configuration
public class GiftBeanConfig {
    public static final String GIFT_CHAIN = "gift_chain";

    @Resource
    private BackpackGiftSend backpackGiftSend;
    @Resource
    private BeansGiftSend beansGiftSend;
    @Resource
    private PostGiftSend postGiftSend;
    @Resource
    private PreGiftSend preGiftSend;
    @Resource
    private AsyncSend asyncSend;
    @Resource
    private VipGiftSend vipGiftSend;
    @Resource
    private RandomGiftSend randomGiftSend;

    @Bean(GIFT_CHAIN)
    public AbstractGiftSend getGiftChain() {
        preGiftSend.setNextHandler(vipGiftSend)
                .setNextHandler(backpackGiftSend)
                .setNextHandler(randomGiftSend)
                .setNextHandler(beansGiftSend)
                .setNextHandler(postGiftSend)
                .setNextHandler(asyncSend);
        return preGiftSend;
    }
}
