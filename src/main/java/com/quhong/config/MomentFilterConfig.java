package com.quhong.config;

import com.quhong.filter.AbstractFilterConfig;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class MomentFilterConfig extends AbstractFilterConfig {

    protected Map<String, Long> getRequestWarnDurationMap() {
        Map<String, Long> map = new HashMap<>();
        map.put("/moment/publish", 5000L);
        map.put("/moment/topic/topicHomeRecommend", 5000L);
        map.put("/moment/noticeList", 5000L);
        map.put("/moment/comment", 5000L);
        map.put("/moment/show/personal", 5000L);
        return map;
    }
}
