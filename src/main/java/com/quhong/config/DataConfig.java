package com.quhong.config;

import com.quhong.player.cache.PlayerCacheMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DataConfig {
    private static final Logger logger = LoggerFactory.getLogger(DataConfig.class);

    @Bean
    public PlayerCacheMap getPlayerCacheMap(){
        return new PlayerCacheMap();
    }
}
