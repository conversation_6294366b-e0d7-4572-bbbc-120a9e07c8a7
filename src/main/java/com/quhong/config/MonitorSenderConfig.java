package com.quhong.config;

import com.quhong.monitor.BaseMonitorSender;
import com.quhong.monitor.MonitorSender;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MonitorSenderConfig {

    @Bean
    public MonitorSender getMonitorSender() {
        return new BaseMonitorSender("01", "ustar", "ustar_java_sundry");
    }

}
