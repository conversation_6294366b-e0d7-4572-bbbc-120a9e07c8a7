package com.quhong.config;

import com.quhong.data.RoomSudGameConfigInfo;
import com.quhong.data.vo.VipRoomConfigVO;
import com.quhong.enums.SLangType;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;


@Component
@PropertySource(value = "classpath:room_config.yml", encoding = "UTF-8", factory = YamlPropertySourceFactory.class)
@ConfigurationProperties(prefix = "config")
public class RoomConfig {
    private int liveRoomRequirement; // 创建直播房间权限，可能的值: 0所有用户均可创建直播房 1VIP2及以上 2用户等级5级及以上
    // 关注房间消息触发时机
    private int followTime;
    // 未送礼过期时间，单位 秒
    private int sendGiftExpireTime;
    // 是否通过http发送心跳
    private int httpHeartSwitch;
    // 表情控制开关 0:关  1: 开
    private int emojiSwitch;
    private List<String> hostWelcomeWordsEn;
    private List<String> hostWelcomeWordsAr;
    private List<String> guideWordEn;
    private List<String> guideWordAr;
    private List<String> followRoomEn;
    private List<String> followRoomAr;
    private VipRoomConfigVO vipRoomConfig;
    private List<String> createRoomNotifyEn;
    private List<String> createRoomNotifyAr;
    private Map<String, Object> memberFeeRange;
    private List<String> likeIconUrlList;
    private List<String> welcomeWordEn;
    private List<String> welcomeWordAr;
    private String defaultRoomAnnounceEn;
    private String defaultRoomAnnounceAr;

    /**
     * 游戏
     */
    private Map<Integer, RoomSudGameConfigInfo> sudGameInfoMap;

    public List<String> getRandomGuideList(int slang) {
        List<String> guideList = slang == SLangType.ENGLISH ? guideWordEn : guideWordAr;
        Collections.shuffle(guideList);
        return guideList.stream().limit(5).collect(Collectors.toCollection(ArrayList::new));
    }

    public List<String> getWelcomeWordList(int slang) {
        return slang == SLangType.ENGLISH ? welcomeWordEn : welcomeWordAr;
    }

    public String getRandomFollowStr(int slang) {
        List<String> followList = slang == SLangType.ENGLISH ? followRoomEn : followRoomAr;
        return followList.get(ThreadLocalRandom.current().nextInt(followList.size()));
    }

    public String getRandomNotifyEnStr() {
        return createRoomNotifyEn.get(ThreadLocalRandom.current().nextInt(createRoomNotifyEn.size()));
    }

    public String getRandomNotifyArStr() {
        return createRoomNotifyAr.get(ThreadLocalRandom.current().nextInt(createRoomNotifyAr.size()));
    }

    public int getFollowTime() {
        return followTime;
    }

    public void setFollowTime(int followTime) {
        this.followTime = followTime;
    }

    public int getSendGiftExpireTime() {
        return sendGiftExpireTime;
    }

    public void setSendGiftExpireTime(int sendGiftExpireTime) {
        this.sendGiftExpireTime = sendGiftExpireTime;
    }

    public List<String> getGuideWordEn() {
        return guideWordEn;
    }

    public void setGuideWordEn(List<String> guideWordEn) {
        this.guideWordEn = guideWordEn;
    }

    public List<String> getGuideWordAr() {
        return guideWordAr;
    }

    public void setGuideWordAr(List<String> guideWordAr) {
        this.guideWordAr = guideWordAr;
    }

    public List<String> getFollowRoomEn() {
        return followRoomEn;
    }

    public void setFollowRoomEn(List<String> followRoomEn) {
        this.followRoomEn = followRoomEn;
    }

    public List<String> getFollowRoomAr() {
        return followRoomAr;
    }

    public void setFollowRoomAr(List<String> followRoomAr) {
        this.followRoomAr = followRoomAr;
    }

    public VipRoomConfigVO getVipRoomConfig() {
        return vipRoomConfig;
    }

    public void setVipRoomConfig(VipRoomConfigVO vipRoomConfig) {
        this.vipRoomConfig = vipRoomConfig;
    }

    public List<String> getCreateRoomNotifyEn() {
        return createRoomNotifyEn;
    }

    public void setCreateRoomNotifyEn(List<String> createRoomNotifyEn) {
        this.createRoomNotifyEn = createRoomNotifyEn;
    }

    public List<String> getCreateRoomNotifyAr() {
        return createRoomNotifyAr;
    }

    public void setCreateRoomNotifyAr(List<String> createRoomNotifyAr) {
        this.createRoomNotifyAr = createRoomNotifyAr;
    }

    public int getHttpHeartSwitch() {
        return httpHeartSwitch;
    }

    public void setHttpHeartSwitch(int httpHeartSwitch) {
        this.httpHeartSwitch = httpHeartSwitch;
    }

    public int getEmojiSwitch() {
        return emojiSwitch;
    }

    public void setEmojiSwitch(int emojiSwitch) {
        this.emojiSwitch = emojiSwitch;
    }

    public Map<String, Object> getMemberFeeRange() {
        return memberFeeRange;
    }

    public void setMemberFeeRange(Map<String, Object> memberFeeRange) {
        this.memberFeeRange = memberFeeRange;
    }

    public List<String> getHostWelcomeWordsEn() {
        return hostWelcomeWordsEn;
    }

    public void setHostWelcomeWordsEn(List<String> hostWelcomeWordsEn) {
        this.hostWelcomeWordsEn = hostWelcomeWordsEn;
    }

    public List<String> getHostWelcomeWordsAr() {
        return hostWelcomeWordsAr;
    }

    public void setHostWelcomeWordsAr(List<String> hostWelcomeWordsAr) {
        this.hostWelcomeWordsAr = hostWelcomeWordsAr;
    }

    public int getLiveRoomRequirement() {
        return liveRoomRequirement;
    }

    public void setLiveRoomRequirement(int liveRoomRequirement) {
        this.liveRoomRequirement = liveRoomRequirement;
    }

    public List<String> getLikeIconUrlList() {
//        return Collections.emptyList();
        return likeIconUrlList;
    }

    public void setLikeIconUrlList(List<String> likeIconUrlList) {
        this.likeIconUrlList = likeIconUrlList;
    }

    public List<String> getWelcomeWordEn() {
        return welcomeWordEn;
    }

    public void setWelcomeWordEn(List<String> welcomeWordEn) {
        this.welcomeWordEn = welcomeWordEn;
    }

    public List<String> getWelcomeWordAr() {
        return welcomeWordAr;
    }

    public void setWelcomeWordAr(List<String> welcomeWordAr) {
        this.welcomeWordAr = welcomeWordAr;
    }

    public Map<Integer, RoomSudGameConfigInfo> getSudGameInfoMap() {
        return sudGameInfoMap;
    }

    public void setSudGameInfoMap(Map<Integer, RoomSudGameConfigInfo> sudGameInfoMap) {
        this.sudGameInfoMap = sudGameInfoMap;
    }

    public String getDefaultRoomAnnounceEn() {
        return defaultRoomAnnounceEn;
    }

    public void setDefaultRoomAnnounceEn(String defaultRoomAnnounceEn) {
        this.defaultRoomAnnounceEn = defaultRoomAnnounceEn;
    }

    public String getDefaultRoomAnnounceAr() {
        return defaultRoomAnnounceAr;
    }

    public void setDefaultRoomAnnounceAr(String defaultRoomAnnounceAr) {
        this.defaultRoomAnnounceAr = defaultRoomAnnounceAr;
    }
}
