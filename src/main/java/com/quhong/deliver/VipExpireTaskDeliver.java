package com.quhong.deliver;

import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.DailyVipUserEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.constant.VipFeatureConstant;
import com.quhong.core.date.DayTimeSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.enums.SLangType;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.NoticeNewDao;
import com.quhong.mongo.dao.OfficialDao;
import com.quhong.mongo.data.NoticeNewData;
import com.quhong.mongo.data.OfficialData;
import com.quhong.msg.chat.OfficialPushMsg;
import com.quhong.mysql.dao.VipConfigDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.data.VipConfigData;
import com.quhong.mysql.data.VipInfoData;
import com.quhong.mysql.data.VipUserInfoData;
import com.quhong.redis.IdentifyRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.service.ActorCommonService;
import com.quhong.service.ResourceKeyHandlerService;
import com.quhong.service.VipV2Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class VipExpireTaskDeliver {

    private final static Logger logger = LoggerFactory.getLogger(VipExpireTaskDeliver.class);

    private static final int QUEEN_VIP_LEVEL = 10;
    private static final String VIP_OFFICIAL_TITLE_EN = "%s membership is about to expire";
    private static final String VIP_OFFICIAL_TITLE_AR = "الانتهاء وشك علي %s عضوية";
    private static final String VIP_OFFICIAL_BODY_DATE_EN = "Your %s membership is about to expire on %s, in order to protect your exclusive membership privileges, please renew as soon as possible!";
    private static final String VIP_OFFICIAL_BODY_DATE_AR = "عضويتك %s على وشك الانتهاء في %s ، من أجل حماية امتيازات عضويتك الحصرية ، يرجى التجديد في أقرب وقت ممكن!";
    private static final String VIP_OFFICIAL_BODY_EN = "Your %s membership will expire today, in order to protect your exclusive membership privileges, please renew as soon as possible!";
    private static final String VIP_OFFICIAL_BODY_AR = "ستنتهي عضويتك %s اليوم ، من أجل حماية امتيازات عضويتك الحصرية ، يرجى التجديد في أقرب وقت ممكن!!";
    private static final String VIP_OFFICIAL_ACTION_EN = "Renewal Now";
    private static final String VIP_OFFICIAL_ACTION_AR = "التجديد الآن";

    private static final String VIP_OFFICIAL_1_EN = "VIP 1";
    private static final String VIP_OFFICIAL_1_AR = "كبار الشخصيات 1";
    private static final String VIP_OFFICIAL_2_EN = "VIP 2";
    private static final String VIP_OFFICIAL_2_AR = "كبار الشخصيات 2";
    private static final String VIP_OFFICIAL_3_EN = "VIP 3";
    private static final String VIP_OFFICIAL_3_AR = "كبار الشخصيات 3";
    private static final String VIP_OFFICIAL_4_EN = "VIP 4";
    private static final String VIP_OFFICIAL_4_AR = "كبار الشخصيات 4";
    private static final String VIP_OFFICIAL_5_EN = "Prince";
    private static final String VIP_OFFICIAL_5_AR = "الأمير";
    private static final String VIP_OFFICIAL_6_EN = "King";
    private static final String VIP_OFFICIAL_6_AR = "الملك";

    private static final String QUEEN_OFFICIAL_TITLE_EN = "Queen membership will expire soon";
    private static final String QUEEN_OFFICIAL_TITLE_AR = "ستنتهي عضوية الملكة قريبًا";
    private static final String QUEEN_OFFICIAL_BODY_DATE_EN = "Your Queen membership will expire on %s. To protect your exclusive membership privileges, please renew as soon as possible!";
    private static final String QUEEN_OFFICIAL_BODY_DATE_AR = " ستنتهي عضوية الماكية الخاصة بك في %s ، لحماية امتيازات العضوية الحصرية الخاصة بك ، يرجى التجديد في أقرب وقت ممكن! ";
    private static final String QUEEN_OFFICIAL_BODY_EN = "Your Queen membership will expire today, to protect your exclusive membership privileges, please Renewal as soon as possible!";
    private static final String QUEEN_OFFICIAL_BODY_AR = "ستنتهي عضوية الماكية الخاصة بك اليوم ، لحماية امتيازات العضوية الحصرية الخاصة بك ، يرجى التجديد في أقرب وقت ممكن!";
    private static final String QUEEN_OFFICIAL_ACTION_EN = "Renewal Now";
    private static final String QUEEN_OFFICIAL_ACTION_AR = "التجديد الآن";

    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private IdentifyRedis identifyRedis;
    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private OfficialDao officialDao;
    @Resource
    private NoticeNewDao noticeNewDao;
    @Resource
    private EventReport eventReport;
    @Resource
    private VipV2Service vipV2Service;
    @Resource
    private VipConfigDao vipConfigDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private ActorCommonService actorCommonService;

    public void vipExpireTask() {

        List<VipUserInfoData> expireVipUserInfoDataList = vipInfoDao.selectExpireVipInfoList();
        // 获取所有VIP配置信息，用于权限回收
        Map<Integer, VipConfigData> vipConfigMap = vipConfigDao.selectList().stream().collect(Collectors.toMap(VipConfigData::getVipLevel, Function.identity()));
        for (VipUserInfoData vipInfo : expireVipUserInfoDataList) {
            String uid = vipInfo.getUid();
            int vipLevel = vipInfo.getVipLevel();
            int currentTime = DateHelper.getNowSeconds();
            int vipEndTime = (int) (vipInfo.getVipEndTime().getTime() / 1000);

            logger.info("vipUserInfo expire {}", JSONObject.toJSONString(vipInfo));
            try {
                // 获取VIP配置信息并移除对应权限
                VipConfigData vipConfig = vipConfigMap.get(vipLevel);
                if (vipConfig != null) {
                    vipV2Service.handleVipChangeRemoveResource(uid, vipLevel, VipFeatureConstant.DESCRIPTION_EXPIRE_VIP, vipEndTime);
                }
                resourceKeyHandlerService.removeResourceData(uid, VipV2Service.OLD_VIP_RESOURCE_KEY, VipFeatureConstant.DESCRIPTION_EXPIRE_VIP, vipEndTime);

                // 更新VIP过期状态
                vipInfo.setVipExpireStatus(1);
                vipInfoDao.update(vipInfo);
                vipInfoDao.clearVipLevelFromRedis(uid);
                vipInfoDao.updateRedisVipGiftRemain(uid, 0);

                // 插入VIP过期记录
                int vipDay = (int) (vipInfo.getVipEndTime().getTime() / 1000 - vipInfo.getVipBuyTime().getTime() / 1000) / 86400;
                vipV2Service.insertVipRecord(uid, vipLevel, vipDay, VipFeatureConstant.RECORD_VIP_EXPIRE, currentTime);
                logger.info("VIP expire task completed for uid: {}, vipLevel: {}", uid, vipLevel);

            } catch (Exception e) {
                logger.error("VIP expire task failed for uid: {}, vipLevel: {}", uid, vipLevel, e);
            }
        }
    }

    // 过期官方消息
    private void officialMsgPush(String uid, String originDate, int vipLevel, int countDay) {

        try {
            String title;
            String body;
            String action;
            ActorData userInfo = actorDao.getActorDataFromCache(uid);
            int slang = userInfo.getSlang();
            int actionType = 31;
            if (vipLevel == QUEEN_VIP_LEVEL) {
                title = slang == SLangType.ARABIC ? QUEEN_OFFICIAL_TITLE_AR : QUEEN_OFFICIAL_TITLE_EN;
                if (countDay > 0) {
                    body = slang == SLangType.ARABIC ? QUEEN_OFFICIAL_BODY_AR : QUEEN_OFFICIAL_BODY_EN;
                } else {
                    body = slang == SLangType.ARABIC ? QUEEN_OFFICIAL_BODY_DATE_AR : QUEEN_OFFICIAL_BODY_DATE_EN;
                    body = String.format(body, originDate);
                }
                action = slang == SLangType.ARABIC ? QUEEN_OFFICIAL_ACTION_AR : QUEEN_OFFICIAL_ACTION_EN;

            } else {
                String vipDesc;
                switch (vipLevel) {
                    case 1:
                        vipDesc = slang == SLangType.ARABIC ? VIP_OFFICIAL_1_AR : VIP_OFFICIAL_1_EN;
                        break;
                    case 2:
                        vipDesc = slang == SLangType.ARABIC ? VIP_OFFICIAL_2_AR : VIP_OFFICIAL_2_EN;
                        break;
                    case 3:
                        vipDesc = slang == SLangType.ARABIC ? VIP_OFFICIAL_3_AR : VIP_OFFICIAL_3_EN;
                        break;
                    case 4:
                        vipDesc = slang == SLangType.ARABIC ? VIP_OFFICIAL_4_AR : VIP_OFFICIAL_4_EN;
                        break;
                    case 5:
                        vipDesc = slang == SLangType.ARABIC ? VIP_OFFICIAL_5_AR : VIP_OFFICIAL_5_EN;
                        break;
                    default:
                        vipDesc = slang == SLangType.ARABIC ? VIP_OFFICIAL_6_AR : VIP_OFFICIAL_6_EN;
                }

                title = slang == SLangType.ARABIC ? VIP_OFFICIAL_TITLE_AR : VIP_OFFICIAL_TITLE_EN;
                title = String.format(title, vipDesc);

                if (countDay > 0) {
                    body = slang == SLangType.ARABIC ? VIP_OFFICIAL_BODY_AR : VIP_OFFICIAL_BODY_EN;
                    body = String.format(body, vipDesc);
                } else {
                    body = slang == SLangType.ARABIC ? VIP_OFFICIAL_BODY_DATE_AR : VIP_OFFICIAL_BODY_DATE_EN;
                    body = String.format(body, vipDesc, originDate);
                }

                action = slang == SLangType.ARABIC ? VIP_OFFICIAL_ACTION_AR : VIP_OFFICIAL_ACTION_EN;
            }

            OfficialData officialData = new OfficialData();
            officialData.setSubTitle("");
            officialData.setTo_uid(uid);
            officialData.setCtime(DateHelper.getNowSeconds());
            officialData.setAtype(actionType);
            officialData.setTitle(title);
            officialData.setBody(body);
            officialData.setAct(action);
            officialData.setPicture("");
            officialData.setValid(1);
            officialDao.save(officialData);
            if (officialData.get_id() != null) {
                noticeNewDao.save(new NoticeNewData(uid, officialData.get_id().toString()));

                OfficialPushMsg msg = new OfficialPushMsg();
                msg.setTitle(officialData.getTitle());
                msg.setBody(officialData.getBody());
                roomWebSender.sendPlayerWebMsg("", uid, uid, msg, false);
            }
        } catch (Exception e) {
            logger.error("officialMsgPush error: {}", e.getMessage());
        }
    }


    private void awareUserVipExpire(int countDay) {
        String originDate = DayTimeSupport.format(LocalDateTime.now().plusMinutes(5 * 60 + 30).plusDays(countDay)).substring(0, 10);
        String startTime = originDate + " 00:00:00";
        String endTime = originDate + "  23:59:59";
        logger.info("awareUserVipExpire startTime: {}, endTime:{}", startTime, endTime);
        List<VipInfoData> awareVipInfos = vipInfoDao.selectAwareExpireVip(startTime, endTime);
        for (VipInfoData vipInfo : awareVipInfos) {
            String uid = vipInfo.getUid();
            int vipLevel = vipInfo.getVipLevel();
            logger.info("UserVipExpire uid: {}, vipLevel:{}", uid, vipLevel);
            officialMsgPush(uid, originDate, vipLevel, countDay);
        }
    }


    public void deliver() {
        String pushDate = identifyRedis.getVipExpirePushDate();
        String checkDate = DateHelper.ARABIAN.getDayTableSuffix(new Date());
        logger.info("wareUserVipExpire pushDate: {}, checkDate: {}", pushDate, checkDate);
        if (!Objects.equals(pushDate, checkDate)) {
            awareUserVipExpire(0);
            awareUserVipExpire(3);
            identifyRedis.setVipExpirePushDate(checkDate);
        }
    }

    /**
     * 每日VIP用户信息上报
     */
    public void dailyVipUserInfoReport() {
        logger.info("dailyVipUserInfoReport start");
        List<VipUserInfoData> activeVipUsers = vipInfoDao.selectAllActiveVipUsers();
        String today = DateHelper.ARABIAN.getDayTableSuffix(new Date());
        int currentTime = DateHelper.getNowSeconds();
        logger.info("dailyVipUserInfoReport activeVipUsers size: {}", activeVipUsers.size());
        for (VipUserInfoData vipInfo : activeVipUsers) {
            DailyVipUserEvent event = new DailyVipUserEvent();
            event.setUid(vipInfo.getUid());
            event.setVip_level(vipInfo.getVipLevel());
            event.setVip_level_name(VipFeatureConstant.VIP_NAME_MAP.getOrDefault(vipInfo.getVipLevel(), ""));
            event.setActivate_type(vipInfo.getVipSource());
            event.setVip_category(actorCommonService.getVipVersion(vipInfo.getUid()));
            event.setDate(today);
            event.setCtime(currentTime);
            eventReport.track(new EventDTO(event));
        }
        logger.info("dailyVipUserInfoReport completed successfully, reported {} users", activeVipUsers.size());
    }

}
