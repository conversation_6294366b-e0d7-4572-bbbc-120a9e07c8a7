package com.quhong.mongo.data;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;


/**
 * 用户赊账评级额度表
 */
@Document(collection = "credit_quota")
public class CreditQuotaData {
    @Id
    private ObjectId _id;
    // 管理员uid，系统评判值为system
    private String admin_uid;
    // uid
    private String aid;
    // 赊账金额限制
    private Integer limit;
    // 剩余可赊账金额
    private Integer left;
    // 信用等级
    private Integer credit_level;
    // 信用得分
    private Integer credit_score;
    // 实充总金额  money+预充值+还款
    private Float paid_money;
    // 赊账总金额
    private Float credit_money;
    // 欠款总金额
    private Float debt_money;
    // 近7天登入次数
    private Integer last_7_count;
    // 修改日期
    private Integer m_time;
    // 创建日期
    private Integer c_time;

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getAdmin_uid() {
        return admin_uid;
    }

    public void setAdmin_uid(String admin_uid) {
        this.admin_uid = admin_uid;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public Integer getLimit() {
        return limit == null ? 0 : limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLeft() {
        return left == null ? 0 : left;
    }

    public void setLeft(Integer left) {
        this.left = left;
    }

    public Integer getCredit_level() {
        return credit_level;
    }

    public void setCredit_level(Integer credit_level) {
        this.credit_level = credit_level;
    }

    public Integer getCredit_score() {
        return credit_score == null ? 0 : credit_score;
    }

    public void setCredit_score(Integer credit_score) {
        this.credit_score = credit_score;
    }

    public Float getPaid_money() {
        return paid_money == null ? 0 : paid_money;
    }

    public void setPaid_money(Float paid_money) {
        this.paid_money = paid_money;
    }

    public Float getCredit_money() {
        return credit_money == null ? 0 : credit_money;
    }

    public void setCredit_money(Float credit_money) {
        this.credit_money = credit_money;
    }

    public Float getDebt_money() {
        return debt_money == null ? 0 : debt_money;
    }

    public void setDebt_money(Float debt_money) {
        this.debt_money = debt_money;
    }

    public Integer getLast_7_count() {
        return last_7_count == null ? 0 : last_7_count;
    }

    public void setLast_7_count(Integer last_7_count) {
        this.last_7_count = last_7_count;
    }

    public Integer getM_time() {
        return m_time;
    }

    public void setM_time(Integer m_time) {
        this.m_time = m_time;
    }

    public Integer getC_time() {
        return c_time;
    }

    public void setC_time(Integer c_time) {
        this.c_time = c_time;
    }

    @Override
    public String toString() {
        return "CreditQuotaData{" +
                "_id=" + _id +
                ", admin_uid='" + admin_uid + '\'' +
                ", aid='" + aid + '\'' +
                ", limit=" + limit +
                ", left=" + left +
                ", credit_level=" + credit_level +
                ", credit_score=" + credit_score +
                ", paid_money=" + paid_money +
                ", credit_money=" + credit_money +
                ", debt_money=" + debt_money +
                ", last_7_count=" + last_7_count +
                ", m_time=" + m_time +
                ", c_time=" + c_time +
                '}';
    }
}
