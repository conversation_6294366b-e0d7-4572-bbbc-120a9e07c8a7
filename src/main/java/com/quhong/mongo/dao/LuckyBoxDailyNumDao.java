package com.quhong.mongo.dao;

import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.LuckyBoxDailyNumData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/13
 */
@Component
public class LuckyBoxDailyNumDao {

    private static final Logger logger = LoggerFactory.getLogger(LuckyBoxDailyNumDao.class);

    public static final String TABLE_NAME = "lucky_box_daily_num";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public void save(LuckyBoxDailyNumData data) {
        try {
            mongoTemplate.save(data);
        } catch (Exception e) {
            logger.error("save lucky box daily num data error. uid={} {}", data.getUid(), e.getMessage(), e);
        }
    }


    public int getGainedLuckyBoxNum(String uid, int startTime, int endTime) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid).and("action").is(2).and("c_time").gte(startTime).lt(endTime);
            Aggregation agg = Aggregation.newAggregation(Aggregation.match(criteria),
                    Aggregation.group("uid").count().as("count"));
            List<CountData> result = mongoTemplate.aggregate(agg, TABLE_NAME, CountData.class).getMappedResults();
            int count = 0;
            for (CountData countData : result) {
                count += countData.getCount();
            }
            return count;
        } catch (Exception e) {
            logger.error("get follows count error {}", e.getMessage(), e);
            return 0;
        }
    }

    public List<LuckyBoxDailyNumData> findList(String boxId) {
        try {
            Criteria criteria = Criteria.where("box_id").is(boxId);
            return mongoTemplate.find(new Query(criteria), LuckyBoxDailyNumData.class);
        } catch (Exception e) {
            logger.error("find lucky box daily num data list error. boxId={} {}", boxId, e.getMessage(), e);
            return null;
        }
    }

    public List<LuckyBoxDailyNumData> getLuckyGainReward(String boxId) {
        try {
            Criteria criteria = Criteria.where("box_id").is(boxId).and("action").is(2);
            return mongoTemplate.find(new Query(criteria), LuckyBoxDailyNumData.class);
        } catch (Exception e) {
            logger.error("find lucky box daily num data list error. boxId={} {}", boxId, e.getMessage(), e);
            return null;
        }
    }

    public void batchDelete(int timestamp) {
        try {
            Criteria criteria = Criteria.where("c_time").lt(timestamp);
            mongoTemplate.remove(new Query(criteria), TABLE_NAME);
        } catch (Exception e) {
            logger.error("batchDelete error. timestamp={} {}", timestamp, e.getMessage(), e);
        }
    }

    public static class CountData {
        private int count;
        private String key;

        public int getCount() {
            return count;
        }

        public void setCount(int count) {
            this.count = count;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }
    }
}
