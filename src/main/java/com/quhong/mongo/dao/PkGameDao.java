package com.quhong.mongo.dao;

import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.PkGame;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2022/8/3
 */
@Component
public class PkGameDao {

    private static final Logger logger = LoggerFactory.getLogger(PkGameDao.class);

    public static final String TABLE_NAME = "pk_game";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public PkGame findData(String roomId) {
        try {
            Criteria criteria = Criteria.where("room_id").is(roomId).and("status").in(Arrays.asList(0,1));
            return mongoTemplate.findOne(new Query(criteria), PkGame.class);
        } catch (Exception e) {
            logger.error("find pk game data error. roomId={} {}", roomId, e.getMessage(), e);
        }
        return null;
    }
}
