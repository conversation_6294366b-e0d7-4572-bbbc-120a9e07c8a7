package com.quhong.controllers;

import com.quhong.dto.StarBeatGameDTO;
import com.quhong.enums.GameHttpCode;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.handler.H5Controller;
import com.quhong.service.StarBeatGameService;
import com.quhong.utils.RequestUtils;
import com.quhong.vo.StarBeatGameVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


@RestController
@RequestMapping("${baseUrl}")
public class StarBeatH5Controller extends H5Controller {
    private static final Logger logger = LoggerFactory.getLogger(StarBeatH5Controller.class);

    @Autowired
    private StarBeatGameService starBeatGameService;

    /**
     * 抽奖
     */
    @PostMapping("/starBeat/draw")
    public String draw(@RequestBody StarBeatGameDTO dto) {
        logger.info("draw. dto={}", dto);
        long start = System.currentTimeMillis();
        if (dto == null) {
            return createResult(HttpCode.PARAM_ERROR, new Object());
        }
        StarBeatGameVO vo = starBeatGameService.draw(dto);
        logger.info("draw cost={}", System.currentTimeMillis() - start);
        return createResult(HttpCode.SUCCESS, vo);
    }

    /**
     * 基本信息
     */
    @PostMapping("/starBeat/info")
    public String info(@RequestBody StarBeatGameDTO dto) {
        logger.info("info. dto={}", dto);
        long start = System.currentTimeMillis();
        if (dto == null) {
            return createResult(HttpCode.PARAM_ERROR, new Object());
        }
        StarBeatGameVO vo = starBeatGameService.info(dto);
        logger.info("info cost={}", System.currentTimeMillis() - start);
        return createResult(HttpCode.SUCCESS, vo);
    }

    /**
     * 排行
     */
    @PostMapping("/starBeat/rank")
    public String rank(@RequestBody StarBeatGameDTO dto) {
        logger.info("rank. dto={}", dto);
        if (dto == null) {
            return createResult(HttpCode.PARAM_ERROR, new Object());
        }
        StarBeatGameVO vo = starBeatGameService.rank(dto);
        return createResult(HttpCode.SUCCESS, vo);
    }

    /**
     * 中奖记录
     */
    @PostMapping("/starBeat/history")
    public String history(@RequestBody StarBeatGameDTO dto) {
        logger.info("history. dto={}", dto);
        if (dto == null) {
            return createResult(HttpCode.PARAM_ERROR, new Object());
        }
        StarBeatGameVO vo = starBeatGameService.history(dto);
        return createResult(HttpCode.SUCCESS, vo);
    }

    /**
     * 所有礼盒奖励
     */
    @PostMapping("/starBeat/allBox")
    public String allBox(@RequestBody StarBeatGameDTO dto) {
        logger.info("allBox. dto={}", dto);
        if (dto == null) {
            return createResult(HttpCode.PARAM_ERROR, new Object());
        }
        StarBeatGameVO vo = starBeatGameService.allBox(dto);
        return createResult(HttpCode.SUCCESS, vo);
    }

}
