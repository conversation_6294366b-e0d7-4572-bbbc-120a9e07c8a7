package com.quhong.controllers.visitor;

import com.alibaba.fastjson.JSONObject;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.handler.HttpEnvData;
import com.quhong.handler.WebController;
import com.quhong.intercepters.UserInterceptor;
import com.quhong.utils.RequestUtils;
import com.quhong.video.redis.VideoRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 访客模式
 */
@RestController
@RequestMapping("${baseUrl}" + UserInterceptor.VISITOR)
public class VisitorController extends WebController {
    private static final Logger logger = LoggerFactory.getLogger(VisitorController.class);

    @Resource
    private VideoRedis videoRedis;

    /**
     * app首页视频列表
     */
    @RequestMapping("video/navi_video_list")
    public String naviVideoList(HttpServletRequest request) {
        JSONObject paramObj = RequestUtils.getSendDataFromCache(request);
        HttpEnvData envData = HttpEnvData.create(paramObj);
        String uid = envData.getUid();
        try {
            int page = paramObj.getIntValue("page");
            if (page < 1) {
                page = 1;
            }
            logger.info("visitor do navi video list. uid={} page={}", uid, page);
            ApiResult<Object> result = videoRedis.getNaviVideoListFromCache(page, envData);
            return createResult(envData, result.getCode(), result.getData());
        } catch (Exception e) {
            logger.error("visitor do navi video list error. uid={} {}", uid, e.getMessage(), e);
            return createError(envData, HttpCode.SERVER_ERROR);
        }
    }
}
