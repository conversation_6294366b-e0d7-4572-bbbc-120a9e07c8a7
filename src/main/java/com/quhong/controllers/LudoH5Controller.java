package com.quhong.controllers;

import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.LudoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


@RestController
@RequestMapping("${baseUrl}/ludo/")
public class LudoH5Controller extends H5Controller {
    private static final Logger logger = LoggerFactory.getLogger(LudoH5Controller.class);

    @Resource
    private LudoService ludoService;

    /**
     * ludo广场，H5调用
     */
    @GetMapping("square")
    public String ludoSquare(String uid, int page) {
        if (page < 1) {
            page = 1;
        }
        logger.info("get ludo square list. uid={} page={}", uid, page);
        ApiResult<Object> result = ludoService.getLudoSquare(page);
        return createResult(result.getCode(), result.getData());
    }

    /**
     * ludo广场匹配游戏，H5调用
     */
    @GetMapping("match")
    public String match(String uid) {
        logger.info("ludo square match. uid={}", uid);
        ApiResult<String> result = ludoService.match();
        return createResult(result.getCode(), result.getData());
    }

    /**
     * 前端报错信息上报
     */
    @PostMapping("error_report_h5")
    public String errorReportH5(@RequestBody String errorMsg) {
        if (errorMsg.contains("WebGL2RenderingContext")) {
            logger.error("receive ludo game error report errorMsg:{}", "WebGL");
        } else {
            logger.error("receive ludo game error report errorMsg:\n{}", errorMsg);
        }
        return createResult(HttpCode.SUCCESS, null);
    }
}
