package com.quhong.controllers;

import com.quhong.data.dto.SendFcmDTO;
import com.quhong.datas.HttpResult;
import com.quhong.handler.BaseController;
import com.quhong.service.FCMPushService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "inner/fcm/", produces = MediaType.APPLICATION_JSON_VALUE)
public class InnerFcmController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(InnerFcmController.class);

    @Resource
    private FCMPushService fcmPushService;


    /**
     * 通过toUid 或 toUidSet发送fcm消息
     * 会查询actor表
     */
    @RequestMapping("send_fcm_msg")
    public HttpResult<?> sendFcmMsg(@RequestBody SendFcmDTO dto) {
        logger.info("pre inner send fcm msg. {} {}", dto, dto.getRequestId());
        fcmPushService.sendFcmMsgByUidSet(dto);
        return HttpResult.getOk();
    }


    /**
     * 通过token发送fcm消息
     * 调用此接口要在源头进行埋点， 此处不知道用户信息
     */
    @RequestMapping("send_fcm_msg_token")
    public HttpResult<?> sendFcmMsgByToken(@RequestBody SendFcmDTO dto) {
        logger.info("pre inner send fcm msg by token. {} {}", dto, dto.getRequestId());
        fcmPushService.sendFcmMsgByToken(dto);
        return HttpResult.getOk();
    }

    /**
     * 通过userInfoMap发送fcm消息
     */
    @RequestMapping("send_fcm_msg_user")
    public HttpResult<?> sendFcmMsgByUserInfo(@RequestBody SendFcmDTO dto) {
        logger.info("pre inner send fcm msg by userInfo. {} {}", dto, dto.getRequestId());
        fcmPushService.sendFcmMsgByUserInfo(dto);
        return HttpResult.getOk();
    }
}
