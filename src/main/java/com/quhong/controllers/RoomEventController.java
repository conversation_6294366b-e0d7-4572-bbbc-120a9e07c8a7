package com.quhong.controllers;

import com.alibaba.fastjson.JSONObject;
import com.quhong.data.dto.RoomEventDTO;
import com.quhong.data.vo.RoomEventDetailVO;
import com.quhong.data.vo.RoomEventPageVO;
import com.quhong.data.vo.RoomEventRankVO;
import com.quhong.enums.HttpCode;
import com.quhong.service.RoomEventService;
import com.quhong.utils.RequestUtils;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2022/12/6
 */
@RestController
@RequestMapping("${baseUrl}/room_event")
public class RoomEventController extends AbstractRoomMicController{

    private static final Logger logger = LoggerFactory.getLogger(RoomEventController.class);

    @Resource
    private RoomEventService roomEventService;

    /**
     * 获取进入创建页面的信息
     */
    @RequestMapping("create_info")
    private String getCreateInfo(HttpServletRequest request) {
        RoomEventDTO req = RequestUtils.getSendData(request, RoomEventDTO.class);
        logger.info("get create info. roomId={} uid={}", req.getRoomId(), req.getUid());
        return createResult(req, HttpCode.SUCCESS, roomEventService.getCreateInfo(req));
    }

    /**
     * 校验输入用户是否正确
     */
    @RequestMapping("getUserInfo")
    private String getUserInfo(HttpServletRequest request) {
        RoomEventDTO req = RequestUtils.getSendData(request, RoomEventDTO.class);
        logger.info("getUserInfo req={}", JSONObject.toJSONString(req));
        return createResult(req, HttpCode.SUCCESS, roomEventService.getUserInfoByUserId(req));
    }

    /**
     * 保存房间event活动
     */
    @RequestMapping("save")
    private String saveRoomEvent(HttpServletRequest request) {
        RoomEventDTO req = RequestUtils.getSendData(request, RoomEventDTO.class);
        logger.info("save room event. roomId={} uid={}", req.getRoomId(), req.getUid());
        roomEventService.saveRoomEvent(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 增加活动组织者
     */
    @RequestMapping("addHostUser")
    private String addHostUser(HttpServletRequest request) {
        RoomEventDTO req = RequestUtils.getSendData(request, RoomEventDTO.class);
        logger.info("save room event. roomId={} uid={}", req.getRoomId(), req.getUid());
        return createResult(req, HttpCode.SUCCESS, roomEventService.addHostUser(req));
    }

    /**
     * 删除活动组织者
     */
    @RequestMapping("deleteHostUser")
    private String deleteHostUser(HttpServletRequest request) {
        RoomEventDTO req = RequestUtils.getSendData(request, RoomEventDTO.class);
        logger.info("save room event. roomId={} uid={}", req.getRoomId(), req.getUid());
        return createResult(req, HttpCode.SUCCESS, roomEventService.deleteHostUser(req));
    }

    /**
     * 删除房间event活动
     */
    @RequestMapping("remove")
    private String removeRoomEvent(HttpServletRequest request) {
        RoomEventDTO req = RequestUtils.getSendData(request, RoomEventDTO.class);
        logger.info("remove room event. uid={} eventId={}", req.getUid(), req.getEventId());
        roomEventService.removeRoomEvent(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 活动列表
     */
    @RequestMapping("list")
    private String getRoomEventList(HttpServletRequest request) {
        RoomEventDTO req = RequestUtils.getSendData(request, RoomEventDTO.class);
        long timeMillis = System.currentTimeMillis();
        logger.info("get event list. uid={} roomId={} isHomePage={} page={}", req.getUid(), req.getRoomId(), req.getIsHomepage(), req.getPage());
        RoomEventPageVO vo = roomEventService.getRoomEventList(req);
        logger.info("get event list. uid={} roomId={} isHomePage={} page={} cost={}", req.getUid(), req.getRoomId(), req.getIsHomepage(), req.getPage(), System.currentTimeMillis() - timeMillis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 房间event活动列表
     */
    @RequestMapping("room_list")
    private String getEventRoomList(HttpServletRequest request) {
        RoomEventDTO req = RequestUtils.getSendData(request, RoomEventDTO.class);
        long timeMillis = System.currentTimeMillis();
        logger.info("get room event list. uid={} roomId={} page={}", req.getUid(), req.getRoomId(), req.getPage());
        RoomEventPageVO vo = roomEventService.getEventRoomList(req);
        logger.info("get room event list. uid={} roomId={} page={} cost={}", req.getUid(), req.getRoomId(),  req.getPage(), System.currentTimeMillis() - timeMillis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 房间活动历史列表
     */
    @RequestMapping("past_list")
    private String getPastRoomEventList(HttpServletRequest request) {
        RoomEventDTO req = RequestUtils.getSendData(request, RoomEventDTO.class);
        long timeMillis = System.currentTimeMillis();
        logger.info("get past room event list. uid={} roomId={} page={}", req.getUid(), req.getRoomId(), req.getPage());
        RoomEventPageVO vo = roomEventService.getPastRoomEventList(req);
        logger.info("get past room event list. uid={} roomId={} page={} cost={}", req.getUid(), req.getRoomId(), req.getPage(), System.currentTimeMillis() - timeMillis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * Mine房间活动列表
     */
    @RequestMapping("mine")
    private String getMineList(HttpServletRequest request) {
        RoomEventDTO req = RequestUtils.getSendData(request, RoomEventDTO.class);
        long timeMillis = System.currentTimeMillis();
        logger.info("get mine room event list. uid={} roomId={} listType={} page={}", req.getUid(), req.getRoomId(), req.getListType(), req.getPage());
        RoomEventPageVO vo = roomEventService.getMineList(req);
        logger.info("get mine room event list. uid={} roomId={} listType={} page={} cost={}", req.getUid(), req.getRoomId(), req.getListType(), req.getPage(), System.currentTimeMillis() - timeMillis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 获取房间活动详情
     */
    @RequestMapping("detail")
    private String getEventDetail(HttpServletRequest request) {
        RoomEventDTO req = RequestUtils.getSendData(request, RoomEventDTO.class);
        long timeMillis = System.currentTimeMillis();
        logger.info("get mine room event detail. uid={} eventId={}", req.getUid(), req.getEventId());
        RoomEventDetailVO vo = roomEventService.getEventDetail(req);
        logger.info("get mine room event detail. uid={} eventId={} cost={}", req.getUid(), req.getEventId(), System.currentTimeMillis() - timeMillis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 用户订阅房间活动
     */
    @RequestMapping("sub_event")
    private String subEvent(HttpServletRequest request) {
        RoomEventDTO req = RequestUtils.getSendData(request, RoomEventDTO.class);
        logger.info("user subscriber room event. uid={} eventId={} subOpt={}", req.getUid(), req.getEventId(), req.getSubOpt());
        if (req.getEventId() == null || req.getSubOpt() == null) {
            return createResult(req, HttpCode.PARAM_ERROR, null);
        }
        req.setRobot(false);
        roomEventService.subEvent(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 订阅用户列表
     */
    @RequestMapping("sub_list")
    private String getSubList(HttpServletRequest request) {
        RoomEventDTO req = RequestUtils.getSendData(request, RoomEventDTO.class);
        long timeMillis = System.currentTimeMillis();
        logger.info("get room event subscriber list. uid={} eventId={} page={}", req.getUid(), req.getEventId(), req.getPage());
        if (req.getEventId() == null) {
            return createResult(req, HttpCode.PARAM_ERROR, null);
        }
        PageVO vo = roomEventService.getSubList(req);
        logger.info("get room event subscriber list. uid={} eventId={} page={} cost={}", req.getUid(), req.getEventId(), req.getPage(), System.currentTimeMillis() - timeMillis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 活动送礼物的排行榜
     */
    @RequestMapping("rank_list")
    private String getRankList(HttpServletRequest request) {
        RoomEventDTO req = RequestUtils.getSendData(request, RoomEventDTO.class);
        long timeMillis = System.currentTimeMillis();
        logger.info("get room event gifts ranking list. uid={} eventId={} page={}", req.getUid(), req.getEventId(), req.getPage());
        if (req.getEventId() == null) {
            return createResult(req, HttpCode.PARAM_ERROR, null);
        }
        RoomEventRankVO vo = roomEventService.getRankList(req);
        logger.info("get room event gifts ranking list. uid={} eventId={} page={} cost={}", req.getUid(), req.getEventId(), req.getPage(), System.currentTimeMillis() - timeMillis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }
}
