package com.quhong.controllers;

import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.RechargeInfo;
import com.quhong.data.dto.HonorQueryDTO;
import com.quhong.data.vo.FirstRechargeInfoVO;
import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.handler.HttpEnvData;
import com.quhong.mq.MqSenderService;
import com.quhong.service.FirstRechargeService;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 首充礼包
 *
 * <AUTHOR>
 * @date 2022/11/14
 */
@RestController
@RequestMapping(value = "${baseUrl}")
public class FirstRechargeController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(FirstRechargeController.class);

    @Resource
    private FirstRechargeService firstRechargeService;
    @Resource
    private MqSenderService mqSenderService;


    /**
     * h5接口 获取首充礼包信息
     */
    @GetMapping("first_recharge/info")
    public String getFirstRechargeInfo(@RequestParam(required = false) String uid,@RequestParam(defaultValue = "1") int slang) {
        long time = System.currentTimeMillis();
        logger.info("get first recharge info. uid={}", uid);
        if (StringUtils.isEmpty(uid) || uid.length() > 64) {
            FirstRechargeInfoVO vo = new FirstRechargeInfoVO();
            logger.info("uid is empty or length > 64 uid:{}", uid);
            return createResult(HttpCode.AUTH_ERROR, vo);
        }
        FirstRechargeInfoVO vo = firstRechargeService.getFirstRechargeInfo("",slang);
        vo.setRank(firstRechargeService.getMySelfRank(uid));
        logger.info("get first recharge info. uid={} cost={}", uid, time - System.currentTimeMillis());
        return createResult(HttpCode.SUCCESS, vo);
    }

    @GetMapping("refund_test")
    public String refundTest(@RequestParam String uid, @RequestParam String orderId, @RequestParam Integer action, @RequestParam String rechargeChannel) {
        if (ServerConfig.isNotProduct()) {
            if (action == null || action == 0) {
                if (!StringUtils.isEmpty(rechargeChannel)) {
                    // 发送退款mq消息
                    if (rechargeChannel.equals("apple")) {
                        mqSenderService.sendRefundOrderToMq(MqSenderService.REFUND_APPLE_ROUTE_KEY, new RechargeInfo(uid, orderId));
                    } else {
                        mqSenderService.sendRefundOrderToMq(MqSenderService.REFUND_GOOGLE_ROUTE_KEY, new RechargeInfo(uid, orderId));
                    }
                }
            } else {
                RechargeInfo rechargeInfo = new RechargeInfo();
                rechargeInfo.setUid(uid);
                rechargeInfo.setOrderId(orderId);
                rechargeInfo.setRechargeMoney(0.99);
                rechargeInfo.setRechargeDiamond(110);
                rechargeInfo.setRechargeTime(DateHelper.getNowSeconds());
                rechargeInfo.setRechargeType(1);
                rechargeInfo.setSubType("");
                rechargeInfo.setRechargeItem("apple");
                rechargeInfo.setFirstChargedAdd(33);
                mqSenderService.sendUserRechargeToMq("applePayCharge", rechargeInfo);
            }
        }
        return createResult(HttpCode.SUCCESS, null);
    }
}
