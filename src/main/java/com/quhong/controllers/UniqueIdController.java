package com.quhong.controllers;

import com.alibaba.fastjson.JSONObject;
import com.quhong.annotation.RequireRole;
import com.quhong.data.dto.UniqueIdDTO;
import com.quhong.data.vo.UniqueIdVO;
import com.quhong.enums.HttpCode;
import com.quhong.handler.DesController;
import com.quhong.service.UniqueIdService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/11/1
 */
@RestController
@RequestMapping("${baseUrl}")
public class UniqueIdController extends DesController {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private UniqueIdService uniqueIdService;

    /**
     * 靓号信息
     */
    @RequireRole()
    @PostMapping("/unique_id/info")
    public String uniqueIdInfo(@RequestBody UniqueIdDTO dto) {
        logger.info("setUniqueId. dto={}", JSONObject.toJSONString(dto));
        UniqueIdVO vo = uniqueIdService.getUniqueInfo(dto);
        logger.info("setUniqueId. vo={}", JSONObject.toJSONString(vo));
        return createResult(new HttpCode(0, "success"), vo);
    }

    /**
     * 靓号校验
     */
    @RequireRole()
    @PostMapping("/unique_id/check")
    public String uniqueIdCheck(@RequestBody UniqueIdDTO dto) {
        logger.info("checkUniqueId. dto={}", JSONObject.toJSONString(dto));
        uniqueIdService.checkUniqueId(dto.getAid(), dto.getUniqueId());
        return createResult(new HttpCode(0, "success"), null);
    }

    /**
     * 设置靓号
     */
    @RequireRole()
    @PostMapping("/unique_id/set")
    public String setUniqueId(@RequestBody UniqueIdDTO dto) {
        logger.info("setUniqueId. dto={}", JSONObject.toJSONString(dto));
        uniqueIdService.setUniqueId(dto);
        return createResult(new HttpCode(0, "success"), null);
    }

    /**
     * 移除靓号
     */
    @RequireRole()
    @PostMapping("/unique_id/remove")
    public String removeUniqueId(@RequestBody UniqueIdDTO dto) {
        logger.info("setUniqueId. dto={}", JSONObject.toJSONString(dto));
        uniqueIdService.removeUniqueId(dto);
        return createResult(new HttpCode(0, "success"), null);
    }
}
