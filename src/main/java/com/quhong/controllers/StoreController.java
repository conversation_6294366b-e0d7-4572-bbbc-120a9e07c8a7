package com.quhong.controllers;

import com.quhong.constant.UserHttpCode;
import com.quhong.data.dto.SetResourcesDTO;
import com.quhong.data.dto.StoreDTO;
import com.quhong.data.dto.StoreGoodsDTO;
import com.quhong.data.vo.BeautifulIdVO;
import com.quhong.data.vo.GoodsListHomeVO;
import com.quhong.data.vo.RoomLockVO;
import com.quhong.data.vo.SearchBeautifulIdVO;
import com.quhong.enums.HttpCode;
import com.quhong.enums.UserInfoHttpCode;
import com.quhong.handler.WebController;
import com.quhong.service.*;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2022/8/26
 */
@RestController
@RequestMapping(value = "${baseUrl}")
public class StoreController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(StoreController.class);

    @Resource
    private StoreService storeService;
    @Resource
    private RoomLockService roomLockService;
    @Resource
    private MicFrameService micFrameService;
    @Resource
    private FloatScreenService floatScreenService;
    @Resource
    private JoinCartonService joinCartonService;

    /**
     * 获取指定商品的价格、状态、剩余天数等信息
     */
    @RequestMapping("store/check")
    public String storeCheck(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        StoreDTO req = RequestUtils.getSendData(request, StoreDTO.class);
        logger.info("store check. uid={} gid={} requestId={} timeMillis={}", req.getUid(), req.getGid(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 特权商店列表
     */
    @RequestMapping("store/list")
    public String getStoreList(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        StoreDTO req = RequestUtils.getSendData(request, StoreDTO.class);
        logger.info("get store list. uid={} requestId={} timeMillis={}", req.getUid(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, UserInfoHttpCode.PARAMETER_UPDATE_APP, null);
    }

    /**
     * 商店靓号展示
     */
    @RequestMapping("new_store/id/list")
    public String showBeautifulId(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        StoreDTO req = RequestUtils.getSendData(request, StoreDTO.class);
        BeautifulIdVO vo = storeService.showBeautifulId(req);
        logger.info("get beautiful rid list. uid={} requestId={} timeMillis={}", req.getUid(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 商店靓号搜索
     */
    @RequestMapping("search/beautiful_id")
    public String searchBeautifulId(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        StoreDTO req = RequestUtils.getSendData(request, StoreDTO.class);
        SearchBeautifulIdVO vo = storeService.searchBeautifulId(req);
        logger.info("search beautiful rid. uid={}  rid={} requestId={} timeMillis={}", req.getUid(), req.getRid(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }


    /**
     * 购买房间锁
     */
    @RequestMapping("new_store/room/lock/buy")
    public String buyRoomLock(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        StoreGoodsDTO req = RequestUtils.getSendData(request, StoreGoodsDTO.class);
        RoomLockVO vo = roomLockService.buyRoomLock(req);
        logger.info("buy room lock. uid={} pid={} timeMillis={}", req.getUid(), req.getPid(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 购买麦位框
     */
    @RequestMapping("new_store/mic_frame/buy")
    public String buyFrameMic(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        StoreGoodsDTO req = RequestUtils.getSendData(request, StoreGoodsDTO.class);
        micFrameService.buyGoods(req);
        logger.info("buy mic frame. uid={} mic_id={} timeMillis={}", req.getUid(), req.getMic_frame_id(), System.currentTimeMillis() - millis);
        return createResult(req, UserHttpCode.MIC_FRAME_BUY_SUCCESS, null);
    }

    /**
     * 购买靓号
     */
    @RequestMapping("new_store/buy/beautiful_id")
    public String buyBeautifulRid(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        StoreGoodsDTO req = RequestUtils.getSendData(request, StoreGoodsDTO.class);
        int buyRid = storeService.buyBeautifulRid(req);
        logger.info("buy beautiful id. uid={} buy rid={} timeMillis={}", req.getUid(), req.getRid(), System.currentTimeMillis() - millis);
        return createResult(req, UserHttpCode.BEAUTIFUL_RID_BUY_SUCCESS, new HashMap<>(1).put("rid", buyRid));
    }

    /**
     * 购买浮萍
     */
    @RequestMapping("new_store/float_screen/buy")
    public String buyFloatScreen(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        StoreGoodsDTO req = RequestUtils.getSendData(request, StoreGoodsDTO.class);
        floatScreenService.buyGoods(req);
        logger.info("buy FloatScreen uid={} screen_id={} timeMillis={}", req.getUid(), req.getScreen_id(), System.currentTimeMillis() - millis);
        return createResult(req, UserHttpCode.FLOAT_SCREEN_BUY_SUCCESS, null);
    }


    /**
     * 购买坐骑
     */
    @RequestMapping("new_store/ride/buy")
    public String buyRide(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        StoreGoodsDTO req = RequestUtils.getSendData(request, StoreGoodsDTO.class);
        joinCartonService.buyGoods(req);
        logger.info("buy ride uid={} joinId={} timeMillis={}", req.getUid(), req.getJoin_id(), System.currentTimeMillis() - millis);
        return createResult(req, UserHttpCode.RIDE_BUY_SUCCESS, null);
    }

    /**
     * 新版商店首页列表
     */
    @RequestMapping("new_store/goods/list")
    public String goodsList(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        StoreDTO req = RequestUtils.getSendData(request, StoreDTO.class);
        GoodsListHomeVO vo = storeService.getGoodsListHomeVO(req);
        logger.info("goods list uid={}  timeMillis={}", req.getUid(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 新版商店购买
     */
    @RequestMapping("new_store/goods/buy")
    public String goodsBuy(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        StoreGoodsDTO req = RequestUtils.getSendData(request, StoreGoodsDTO.class);
        logger.info("goods buy req={}", req);
        HttpCode code = storeService.goodsBuy(req);
        logger.info("goods buy success. timeMillis={}", System.currentTimeMillis() - millis);
        return createResult(req, code, null);
    }

    /**
     * 新版商店设置
     */
    @RequestMapping("new_store/goods/set")
    public String goodsSet(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        SetResourcesDTO req = RequestUtils.getSendData(request, SetResourcesDTO.class);
        logger.info("goods set req={}", req);
        HttpCode code = storeService.goodsSet(req);
        logger.info("goods set success timeMillis={}", System.currentTimeMillis() - millis);
        return createResult(req, code, null);
    }

    /**
     * 新版商店商品详情
     */
    @RequestMapping("new_store/goods/list/detail")
    public String getStoreGoodsList(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        StoreDTO req = RequestUtils.getSendData(request, StoreDTO.class);
        logger.info("goods detail req={}", req);
        GoodsListHomeVO vo = storeService.getStoreGoodsList(req);
        logger.info("goods detail success timeMillis={}", System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 新版商店我的装扮详情
     */
    @RequestMapping("new_store/goods/list/my_detail")
    public String getMyGoodsList(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        StoreDTO req = RequestUtils.getSendData(request, StoreDTO.class);
        logger.info("goods my_detail req={}", req);
        GoodsListHomeVO vo = storeService.getMyGoodsList(req);
        logger.info("goods my_detail success timeMillis={}", System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }
}
