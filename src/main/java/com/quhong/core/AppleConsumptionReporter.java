package com.quhong.core;

import com.alibaba.fastjson.JSON;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.web.WebClient;
import com.quhong.data.ActorData;
import com.quhong.data.AppleNotificationData;
import com.quhong.data.AppleReceiptInfo;
import com.quhong.data.dto.AppleConsumptionInformationDTO;
import com.quhong.enums.AppleNotificationType;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.monitor.MonitorSender;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.AppleNotificationLogData;
import com.quhong.mysql.data.ApplePayData;
import com.quhong.mysql.data.GoodsData;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 对接苹果后天服务
 *
 * <AUTHOR>
 * @date 2020/10/22
 */
@Component
public class AppleConsumptionReporter {
    private static final Logger logger = LoggerFactory.getLogger(AppleConsumptionReporter.class);
    // 退款时消费信息
    private static final String CONSUMPTION_INFORMATION_URL = "https://api.storekit.itunes.apple.com/inApps/v1/transactions/consumption/";
    private static final Map<String, String> CONSUMPTION_HEARD_MAP = new HashMap<String, String>() {{
        put("alg", "ES256");
        put("kid", "N429J4U4V7");
        put("typ", "JWT");
    }};
    @Autowired
    private WebClient webClient;
    @Autowired
    private ActorDao actorDao;
    @Autowired
    private ActorPayExternalDao actorPayExternalDao;
    @Autowired
    private RoomUserOnlineDao roomUserOnlineDao;
    @Autowired
    private MoneyDetailStatDao moneyDetailStatDao;
    @Autowired
    private GoodsDao goodsDao;
    @Autowired
    private AppleNotificationLogDao appleNotificationLogDao;
    @Autowired
    private MonitorSender monitorSender;

    public AppleConsumptionReporter() {
    }

    /**
     * 给苹果发送消费信息
     *
     * @url https://developer.apple.com/documentation/appstoreserverapi/send_consumption_information
     */
    public void sendConsumptionInformation(AppleNotificationData notificationData, AppleReceiptInfo receiptInfo, ApplePayData payData) {
        String url = CONSUMPTION_INFORMATION_URL + receiptInfo.getOriginal_transaction_id();
        webClient.sendRestfulPut(url, JSON.toJSONString(createConsumptionInformationData(notificationData, receiptInfo, payData)), CONSUMPTION_HEARD_MAP, 3);
    }

    private AppleConsumptionInformationDTO createConsumptionInformationData(AppleNotificationData notificationData, AppleReceiptInfo receiptInfo, ApplePayData payData) {
        AppleConsumptionInformationDTO dto = new AppleConsumptionInformationDTO();
        ActorData actorData = actorDao.getActorData(payData.getUserid());
        dto.setAccountTenure(getAccountAge(actorData));
        if (actorData == null) {
            return dto;
        }
        dto.setAppAccountToken(actorData.getUid());
        dto.setConsumptionStatus(getConsumptionStatus(actorData, payData));
        dto.setCustomerConsented(true);
        dto.setDeliveryStatus(getDeliveryStatus(payData));
        dto.setLifetimeDollarsRefunded(getRefundValue(actorData.getUid()));
        dto.setPlatform(1);
        if (actorData.getValid() > 0) {
            dto.setUserStatus(1);
        } else {
            dto.setUserStatus(2);
        }
        dto.setLifetimeDollarsPurchased(getLifeTimeDollarCharged(payData.getUserid()));
        dto.setPlayTime(getPlayTime(payData.getUserid()));
        dto.setSampleContentProvided(false);
        return dto;
    }

    private int getDeliveryStatus(ApplePayData payData) {
        /**
         * (Required) A value that indicates whether the app successfully delivered an in-app purchase that works properly.
         * Possible Values
         * 0
         * The app delivered the consumable in-app purchase and it’s working properly.
         *
         * 1
         * The app didn’t deliver the consumable in-app purchase due to a quality issue.
         *
         * 2
         * The app delivered the wrong item.
         *
         * 3
         * The app didn’t deliver the consumable in-app purchase due to a server outage.
         *
         * 4
         * The app didn’t deliver the consumable in-app purchase due to an in-game currency change.
         *
         * 5
         * The app didn’t deliver the consumable in-app purchase for other reasons.
         */
        if (payData.getFstatus() != 1) {
            return 5;
        }
        return 0;
    }

    private int getAccountAge(ActorData actorData) {
        /*
         * (Required) The age of the customer’s account.
         * Possible Values
         * 0
         * Account age is undeclared.
         * 1
         * Account age is between 0–3 days.
         * 2
         * Account age is between 3–10 days.
         * 3
         * Account age is between 10–30 days.
         * 4
         * Account age is between 30–90 days.
         * 5
         * Account age is between 90–180 days.
         * 6
         * Account age is between 180–365 days.
         * 7
         * Account age is over 365 days.
         */
        if (actorData == null) {
            return 0;
        }
        long registerTime = new ObjectId(actorData.getUid()).getTimestamp();
        long ageTime = DateHelper.getNowSeconds() - registerTime;
        if (ageTime < TimeUnit.DAYS.toSeconds(3)) {
            return 1;
        }
        if (ageTime < TimeUnit.DAYS.toSeconds(10)) {
            return 2;
        }
        if (ageTime < TimeUnit.DAYS.toSeconds(30)) {
            return 3;
        }
        if (ageTime < TimeUnit.DAYS.toSeconds(30)) {
            return 4;
        }
        if (ageTime < TimeUnit.DAYS.toSeconds(90)) {
            return 5;
        }
        if (ageTime < TimeUnit.DAYS.toSeconds(100)) {
            return 6;
        }
        if (ageTime < TimeUnit.DAYS.toSeconds(365)) {
            return 7;
        }
        return 8;
    }

    private int getConsumptionStatus(ActorData actorData, ApplePayData payData) {
        try {
            /*
             * (Required) A value that indicates the extent to which the customer consumed the in-app purchase.
             * Possible Values
             * 0
             * The consumption status is undeclared.
             *
             * 1
             * The in-app purchase is not consumed.
             *
             * 2
             * The in-app purchase is partially consumed.
             *
             * 3
             * The in-app purchase is fully consumed.
             */
            int beans = actorData.getBeans();
            int chargeDiamonds = 0;
            if (payData.getProductId() != null) {
                // Double value = goodsDao.getPriceDollars(payData.getProductId());
                // if (value != null) {
                //     chargeDiamonds = (int) Math.round(value);
                // }
                List<GoodsData> goodsList = goodsDao.getGoodsList();
                for (GoodsData goodsData : goodsList) {
                    if (goodsData.getProductId().equals(payData.getProductId())) {
                        chargeDiamonds = goodsData.getBeans();
                        break;
                    }
                }

            }
            int costBeans = moneyDetailStatDao.getTotalCost(actorData.getUid());
            if (costBeans >= chargeDiamonds) {
                return 3;
            } else if (costBeans > 0) {
                return 2;
            }
            if (beans > chargeDiamonds) {
                return 1;
            } else if (beans > 0) {
                return 2;
            }
            return 3;
        } catch (Exception e) {
            logger.error("get consumption error. {}", e.getMessage(), e);
            monitorSender.info("ustar_pay", "initial refund error.", "uid=" + actorData.getUid() + ", rid=" + actorData.getRid());
        }
        return 1;
    }

    private int getLifeTimeDollarCharged(String uid) {
        /*
         * 0
         * Lifetime purchase amount is undeclared.
         *
         * 1
         * Lifetime purchase amount is 0 USD.
         *
         * 2
         * Lifetime purchase amount is between 0.01–49.99 USD.
         *
         * 3
         * Lifetime purchase amount is between 50–99.99 USD.
         *
         * 4
         * Lifetime purchase amount is between 100–499.99 USD.
         *
         * 5
         * Lifetime purchase amount is between 500–999.99 USD.
         *
         * 6
         * Lifetime purchase amount is between 1000–1999.99 USD.
         *
         * 7
         * Lifetime purchase amount is over 2000 USD.
         */
        double rechargeValue = actorPayExternalDao.getUserRechargeMoney(uid).doubleValue();
        if (rechargeValue <= 0) {
            return 1;
        } else if (rechargeValue < 50) {
            return 2;
        } else if (rechargeValue < 100) {
            return 3;
        } else if (rechargeValue < 500) {
            return 4;
        } else if (rechargeValue < 1000) {
            return 5;
        } else if (rechargeValue < 2000) {
            return 6;
        }
        return 7;
    }

    private int getPlayTime(String uid) {
        /*
         * 0
         * The engagement time is undeclared.
         *
         * 1
         * The engagement time is between 0–5 minutes.
         *
         * 2
         * The engagement time is between 5–60 minutes.
         *
         * 3
         * The engagement time is between 1–6 hours.
         *
         * 4
         * The engagement time is between 6–24 hours.
         *
         * 5
         * The engagement time is between 1–4 days.
         *
         * 6
         * The engagement time is between 4–16 days.
         *
         * 7
         * The engagement time is over 16 days.
         */
        int roomTime = roomUserOnlineDao.getStayRoomStat(uid);
        if (roomTime < TimeUnit.MINUTES.toSeconds(5)) {
            return 1;
        }
        if (roomTime < TimeUnit.MINUTES.toSeconds(60)) {
            return 2;
        }
        if (roomTime < TimeUnit.HOURS.toSeconds(60)) {
            return 3;
        }
        if (roomTime < TimeUnit.HOURS.toSeconds(26)) {
            return 4;
        }
        if (roomTime < TimeUnit.DAYS.toSeconds(4)) {
            return 5;
        }
        if (roomTime < TimeUnit.DAYS.toSeconds(16)) {
            return 6;
        }
        return 7;
    }

    private int getRefundValue(String uid) {
        /*
         * (Required) A value that indicates the total amount, in USD, of refunds the customer has received, in your app, across all platforms.
         * Possible Values
         * 0
         * Lifetime refund amount is undeclared.
         *
         * 1
         * Lifetime refund amount is 0 USD.
         *
         * 2
         * Lifetime refund amount is between 0.01–49.99 USD.
         *
         * 3
         * Lifetime refund amount is between 50–99.99 USD.
         *
         * 4
         * Lifetime refund amount is between 100–499.99 USD.
         *
         * 5
         * Lifetime refund amount is between 500–999.99 USD.
         *
         * 6
         * Lifetime refund amount is between 1000–1999.99 USD.
         *
         * 7
         * Lifetime refund amount is over 2000 USD.
         */
        try {
            List<AppleNotificationLogData> list = appleNotificationLogDao.getList(uid, AppleNotificationType.REFUND);
            double totalDollars = 0;
            for (AppleNotificationLogData data : list) {
                if (StringUtils.hasLength(data.getProductId())) {
                    Double dollars = goodsDao.getPriceDollars(data.getProductId());
                    if (dollars != null) {
                        totalDollars += dollars;
                    }
                }
            }
            if (totalDollars == 0) {
                return 0;
            } else if (totalDollars < 0.01) {
                return 1;
            } else if (totalDollars < 50) {
                return 2;
            } else if (totalDollars < 100) {
                return 3;
            } else if (totalDollars < 500) {
                return 4;
            } else if (totalDollars < 1000) {
                return 5;
            } else if (totalDollars < 2000) {
                return 6;
            }
            return 7;
        } catch (Exception e) {
            logger.error("get consumption error. {}", e.getMessage(), e);
            monitorSender.info("ustar_pay", "initial refund error.", "uid=" + uid);
        }
        return 0;
    }
}
