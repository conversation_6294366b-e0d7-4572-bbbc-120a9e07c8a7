package com.quhong.managers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.data.MsgLikeData;
import com.quhong.data.OpMsgReq;
import com.quhong.enums.ApiResult;
import com.quhong.mongo.dao.MsgListDao;
import com.quhong.mongo.data.MsgListData;
import com.quhong.msg.chat.LikeChatMsg;
import com.quhong.mysql.dao.MysqlMsgRecordDao;
import com.quhong.mysql.data.MysqlMsgRecordData;
import com.quhong.room.RoomWebSender;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class ChatMsgLikeProcessor {
    private static final Logger logger = LoggerFactory.getLogger(ChatMsgLikeProcessor.class);

    @Autowired
    private MsgListDao msgListDao;
    @Autowired
    private MysqlMsgRecordDao recordDao;
    @Autowired
    private RoomWebSender webSender;

    public ApiResult<Object> like(OpMsgReq reqData){
        String uid = reqData.getUid();
        logger.info("like msg record. emoji={} msgId={} uid={}", reqData.getEmoji(), reqData.getMsgId(), uid);
        MysqlMsgRecordData recordData = recordDao.getData(reqData.getMsgId());
        if(recordData == null){
            logger.error("can not find msg record data. msgId={} uid={}", reqData.getMsgId(), uid);
            return ApiResult.getOk();
        }
        String fromUid;
        String toUid;
        if(recordData.getFromUid().equals(uid)){
            recordData.setFromLike(reqData.getEmoji());
            fromUid = recordData.getFromUid();
            toUid = recordData.getToUid();
        }else{
            recordData.setToLike(reqData.getEmoji());
            fromUid = recordData.getToUid();
            toUid = recordData.getFromUid();
        }
        recordDao.update(recordData);

        MsgListData msgListData = msgListDao.findMsgListData(recordData.getMsgIndex());
        if(msgListData == null){
            logger.error("ca not find msg list data .msgIndex={} uid={}", recordData.getMsgIndex(), uid);
        }else{
            msgListDao.updateLastRecord(msgListData, recordData);
        }
        // 发送给对方消息
        sendLikeMsg(recordData, fromUid, toUid);
        return ApiResult.getOk();
    }

    private void sendLikeMsg(MysqlMsgRecordData recordData, String fromUid, String toUid){
        LikeChatMsg msg = new LikeChatMsg();
        msg.setChatMsgId(recordData.getMsgId());
        msg.setFromUid(fromUid);
        msg.setLike(JSON.toJSONString(recordData.getLikeList()));
        msg.setLikeUid(fromUid);
        webSender.sendPlayerWebMsg("", fromUid, toUid, msg, true);
    }
}
