package com.quhong.elasticsearch.dao;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.data.elasticsearch.core.query.IndexQueryBuilder;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public abstract class EsMonthShardingDao<T> {

    private static final Logger logger = LoggerFactory.getLogger(EsMonthShardingDao.class);

    private Map<String, Boolean> existMap = new ConcurrentHashMap<>();

    /**
     * 表前置
     */
    protected String tablePre;
    private final Class<T> entityClass;

    @Resource(name = "writeEsTemplate")
    protected ElasticsearchTemplate writeEsTemplate;
    @Resource(name = "readEsTemplate")
    protected ElasticsearchTemplate readEsTemplate;

    public EsMonthShardingDao(String tablePre, Class<T> entityClass) {
        this.tablePre = tablePre;
        this.entityClass = entityClass;
    }

    /**
     * 往es插入数据
     *
     * @param obj 数据对象
     */
    public void save(Object obj) {
        if (null == obj) {
            logger.error("es insert object can not be empty");
            return;
        }
        try {
            String indexName = createIndexName(new Date());
            if (checkExist(indexName)) {
                IndexQuery indexQuery = new IndexQueryBuilder().withObject(obj).withIndexName(indexName).build();
                writeEsTemplate.index(indexQuery);
            }
        } catch (Exception e) {
            logger.error("es insert object error. obj={} {}", JSONObject.toJSONString(obj), e.getMessage(), e);
        }
    }

    /**
     * 检查表是否存在,不存在就创建
     */
    public boolean checkExist(String indexName) {
        Boolean result = existMap.get(indexName);
        if (result != null) {
            return result;
        }
        try {
            if (!writeEsTemplate.indexExists(indexName)) {
                writeEsTemplate.createIndex(indexName);
            }
            existMap.put(indexName, true);
            return true;
        } catch (Exception e) {
            logger.error("check table exists error. {}", e.getMessage(), e);
            return false;
        }
    }

    public String createIndexName(Date date) {
        String suffix = DateHelper.ARABIAN.getTableSuffix(date);
        return createIndexName(suffix);
    }

    public String createIndexName(String suffix) {
        return tablePre + "_" + suffix;
    }

    public List<String> getIndexNameList(int monthOffset) {
        List<String> retList = new ArrayList<>();
        if (monthOffset < 0) {
            for (int i = 0; i > monthOffset; i--) {
                long time = DateHelper.ARABIAN.getMonthOffset(i);
                retList.add(createIndexName(DateHelper.ARABIAN.getTableSuffix(new Date(time))));
            }
        }
        return retList;
    }

    public List<String> getExistIndexNameList(List<String> indexNameList) {
        List<String> retList = new ArrayList<>();
        for (String suffix : indexNameList) {
            if (checkExist(suffix)) {
                retList.add(suffix);
            }
        }
        return retList;
    }

    public String[] getIndexNames(int monthOffset) {
        String[] indexNames = new String[Math.abs(monthOffset)];
        if (monthOffset < 0) {
            for (int i = 0; i > monthOffset; i--) {
                long time = DateHelper.ARABIAN.getMonthOffset(i);
                indexNames[Math.abs(i)] = createIndexName(DateHelper.ARABIAN.getTableSuffix(new Date(time)));
            }
        }
        return indexNames;
    }

    public String[] getIndexNames(int startTime, int endTime) {
        List<String> suffixList = DateHelper.ARABIAN.getTableSuffixList(startTime, endTime);
        String[] indexNames = new String[suffixList.size()];
        int index = 0;
        for (String suffix : suffixList) {
            indexNames[index] = createIndexName(suffix);
            index ++;
        }
        return indexNames;
    }
}
