package com.quhong.elasticsearch;

import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;

import java.io.Serializable;

/**
 * elasticsearch访客记录对象
 */
@Document(indexName = "ustar_log", type = "t_home_visitor")
public class HomeVisitorEs implements Serializable {

    @Id
    private String id;
    // 访问者id
    private String uid;
    //  访问次数
    private Integer times;
    // 被访者id
    private String aid;
    // 是否隐身访问
    private Integer invisible;
    // 最近访问时间
    private Integer mtime;
    // 访问日期
    private String date_num;
    // 访问创建时间
    private Integer ctime;
    // 是否被回访， 1：否  0:是  主要用于谁看过我 没有回访时红点展示
    private Integer is_new;
    // 是否新访问者   1是  0否
    private Integer person_new;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getTimes() {
        return times;
    }

    public void setTimes(Integer times) {
        this.times = times;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public Integer getInvisible() {
        return invisible;
    }

    public void setInvisible(Integer invisible) {
        this.invisible = invisible;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public String getDate_num() {
        return date_num;
    }

    public void setDate_num(String date_num) {
        this.date_num = date_num;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getIs_new() {
        return is_new;
    }

    public void setIs_new(Integer is_new) {
        this.is_new = is_new;
    }

    public Integer getPerson_new() {
        return person_new;
    }

    public void setPerson_new(Integer person_new) {
        this.person_new = person_new;
    }

    @Override
    public String toString() {
        return "HomeVisitorEs{" +
                "id='" + id + '\'' +
                ", uid='" + uid + '\'' +
                ", times=" + times +
                ", aid='" + aid + '\'' +
                ", invisible=" + invisible +
                ", mtime=" + mtime +
                ", date_num='" + date_num + '\'' +
                ", ctime=" + ctime +
                ", is_new=" + is_new +
                ", person_new=" + person_new +
                '}';
    }
}
