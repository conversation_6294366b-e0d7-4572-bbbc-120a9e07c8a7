package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.ActivitySpecialItemsChangeEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.config.ActivityCommonConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.Eid2025VO;
import com.quhong.data.vo.FirstKingdomVO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.FriendsDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.utils.ActorUtils;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * 第一王国
 */
@Service
public class FirstKingdomService extends OtherActivityService {


    private static final Logger logger = LoggerFactory.getLogger(FirstKingdomService.class);
    private static final String ACTIVITY_TITLE_EN = "First Kingdom";
    private static final String ACTIVITY_TITLE_AR = "First Kingdom";
    public static String ACTIVITY_ID = "kkk";
    private static String ACTIVITY_URL = String.format("https://static.youstar.live/first_kingdom2025/?activityId=%s", ACTIVITY_ID);

    // 兑换比例：每xxx钻石=1锤子
    private static int DIAMONDS_PER_POINT = 1;
    // 每xxx建造繁荣度=1等级
    private static int POINT_PER_LEVEL = 100;
    // 攻击抽奖key
    private static final String ATTACK_PAY_DRAW_KEY = "FirstKingdompaydraw";
    private static final String ATTACK_NO_PAY_DRAW_KEY = "FirstKingdomnopaydraw";

    // 钢铁王国
    private static int STEEL_KINGDOM = 1;
    // 自然王国
    private static int NATURE_KINGDOM = 2;
    // 自由王国
    private static int FREEDOM_KINGDOM = 3;


    private static final String BUILD_TYPE = "build";

    private static final String ATTACK_TYPE = "attack";

    private static final String LEVEL3_REWARD_KEY = "first_kingdom_level3_reward";

    // 阵营繁荣度升级等级值
    private static final List<Integer> KINGDOM_LEVEL_LIST = Arrays.asList(0, 100000, 400000, 1000000);

    // 我贡献的建造繁荣度升级等级值
    private static final List<Integer> BUILD_LEVEL_LIST = Arrays.asList(0, 1, 3, 10, 50, 200);
    private static final List<String> BULID_KEY_LEVEL_LIST = Arrays.asList("", "FirstKingdomReward1", "FirstKingdomReward2", "FirstKingdomReward3", "FirstKingdomReward4", "FirstKingdomReward5");
    private static final List<String> BULID_EVENT_LEVEL_LIST = Arrays.asList("", "FirstKingdomReward1", "FirstKingdomReward2", "FirstKingdomReward3", "FirstKingdomReward4", "FirstKingdomReward5");

    private static final List<String> TOP1_KEY_LEVEL_LIST = Arrays.asList("FirstKingdomNO1Top1", "FirstKingdomNO1Top2", "FirstKingdomNO1Top3", "FirstKingdomNO1Top4-10", "FirstKingdomNO1Top4-10", "FirstKingdomNO1Top4-10", "FirstKingdomNO1Top4-10", "FirstKingdomNO1Top4-10", "FirstKingdomNO1Top4-10", "FirstKingdomNO1Top4-10");
    private static final List<String> TOP2_KEY_LEVEL_LIST = Arrays.asList("FirstKingdomNO2Top1", "FirstKingdomNO2Top2", "FirstKingdomNO2Top3", "FirstKingdomNO2Top4-5", "FirstKingdomNO2Top4-5");
    private static final List<String> TOP3_KEY_LEVEL_LIST = Arrays.asList("FirstKingdomNO3Top1", "FirstKingdomNO3Top2-3", "FirstKingdomNO3Top2-3");

    private static final String JOIN_STEEL_KEY = "FirstKingdomSteel";
    private static final String JOIN_NATURE_KEY = "FirstKingdomNature";
    private static final String JOIN_FREEDOM_KEY = "FirstKingdomFree";

    private static final Map<Integer, String> JOIN_KEY_MAP = new HashMap<Integer, String>() {
        {
            put(STEEL_KINGDOM, JOIN_STEEL_KEY);
            put(NATURE_KINGDOM, JOIN_NATURE_KEY);
            put(FREEDOM_KINGDOM, JOIN_FREEDOM_KEY);
        }
    };


    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static final String eventDrawTitle = "FirstKingdompaydraw-title";

    private static final int HISTORY_USER_MAX_SIZE = 1000;
    private static final int PAGE_SIZE = 20;

    private static final String SA_COUNTRY_CODE = "sa";

    // 用户配置字段
    private static final String TEAM_FIELD = "team_field"; // 阵营字段
    private static final String TOTAL_DIAMOND_FIELD = "total_d_field"; // 总发送钻石字段
    private static final String LEFT_CHANCE_FIELD = "left_chance_field"; // 剩余锤子数
    private static final String BUILD_SCORE_FIELD = "build_score_field"; // 建造本阵营贡献的繁荣度
    private static final String BUILD_LEVEL_FIELD = "build_level_field"; // 建造等级字段
    private static final String LEVEL3_GET_FIELD = "level3_get_field"; // 我的王国等级3,是否领取奖励字段

    // 活动层面配置字段
    private static final String A_ATTACK_B_FIELD = "%s_attack_%s"; // A阵营进攻B阵营的消耗的锤子数
    private static final String IS_INIT_FIELD = "is_init_field"; // 所有阵营是否初始化,赋值过默认值


    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private GiftDao giftDao;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private FriendsDao friendsDao;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            ACTIVITY_ID = "68d11ab8e58f9ef900fa011a";
            ACTIVITY_URL = String.format("https://test2.qmovies.tv/first_kingdom2025/?activityId=%s", ACTIVITY_ID);
        }
        String activityIdConfigKey = getActivityIdConfigKey(ACTIVITY_ID);
        if (activityCommonRedis.getCommonHashValue(activityIdConfigKey, IS_INIT_FIELD) == 0) {
            String allPointsKey = getAllPointsKey(ACTIVITY_ID);
            if (activityCommonRedis.getCommonZSetRankingScore(allPointsKey, String.valueOf(STEEL_KINGDOM)) == 0) {
                int initScore = KINGDOM_LEVEL_LIST.get(1);
                activityCommonRedis.addCommonZSetRankingScore(allPointsKey, String.valueOf(STEEL_KINGDOM), initScore);
                activityCommonRedis.addCommonZSetRankingScore(allPointsKey, String.valueOf(NATURE_KINGDOM), initScore);
                activityCommonRedis.addCommonZSetRankingScore(allPointsKey, String.valueOf(FREEDOM_KINGDOM), initScore);
                logger.info("init all points success");
            }
            activityCommonRedis.setCommonHashNum(activityIdConfigKey, IS_INIT_FIELD, 1);
        }

    }


    private String getLocalLockKey(String activityId, String uid) {
        return String.format("getLocalLockKey:%s:%s", activityId, uid);
    }

    private String getLocalTeamLockKey(String activityId, Integer teamType) {
        return String.format("getLocalTeamLockKey:%s:%s", activityId, teamType);
    }

    // Redis key 方法
    // zset存储阵营繁荣度 flied为阵营类型 1-3
    private String getAllPointsKey(String activityId) {
        return String.format("firstKingdom:points:%s", activityId);
    }

    // zset用户在某个阵营消耗的锤子数(包括攻击与建造) flied为uid
    private String getTeamPointsKey(String activityId, int teamType) {
        return String.format("firstKingdom:team:points:%s:%s", activityId, teamType);
    }

    // zset用户在整个活动攻击消耗的锤子数 flied为uid
    private String getAttackPointsKey(String activityId) {
        return String.format("firstKingdom:attack:points:%s", activityId);
    }

    // zset用户在整个活动建造消耗的锤子数 flied为uid
    private String getBuildPointsKey(String activityId) {
        return String.format("firstKingdom:build:points:%s", activityId);
    }


    //  hash存储用户相关配置
    private String getUserConfigKey(String activityId, String uid) {
        return String.format("firstKingdom:user:config:%s:%s", activityId, uid);
    }

    //  hash存储活动层面相关配置
    private String getActivityIdConfigKey(String activityId) {
        return String.format("firstKingdom:activity:config:%s", activityId);
    }


    // - set存储加入某个阵营的用户id
    private String getTeamJoinKey(String activityId, int teamType) {
        return String.format("firstKingdom:team:join:%s", activityId, teamType);
    }

    //  滚屏-list存储最近轮播消息
    private String getRollRecordKey(String activityId) {
        return String.format("firstKingdom:all:history:%s", activityId);
    }

    // 某个用户的抽奖历史记录key-list存储中奖数据,
    private String getListHistoryKey(String activityId, String uid) {
        return String.format("firstKingdom:user:history:%s:%s", activityId, uid);
    }


    public FirstKingdomVO config(String activityId, String uid) {
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);

        FirstKingdomVO vo = new FirstKingdomVO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        String userConfigKey = getUserConfigKey(activityId, uid);
        String activityIdConfigKey = getActivityIdConfigKey(activityId);
        String allPointsKey = getAllPointsKey(activityId);
        String attackPointsKey = getAttackPointsKey(activityId);
        String rollRecordKey = getRollRecordKey(activityId);

        Map<String, String> userConfigMap = activityCommonRedis.getCommonHashAllMapStr(userConfigKey);
        vo.setTeamType(Integer.parseInt(userConfigMap.getOrDefault(TEAM_FIELD, "0")));
        if (vo.getTeamType() != 0) {
            int teamScore = activityCommonRedis.getCommonZSetRankingScore(allPointsKey, String.valueOf(vo.getTeamType()));
            vo.setKingLevel(getBaseIndexLevel(teamScore, KINGDOM_LEVEL_LIST));
            vo.setKingMemberCount(activityCommonRedis.getCommonSetNum(getTeamJoinKey(activityId, vo.getTeamType())));

            List<FirstKingdomVO.TeamAttackVO> teamAttackList = new ArrayList<>();
            Map<String, String> activityConfigMap = activityCommonRedis.getCommonHashAllMapStr(activityIdConfigKey);
            for (Integer teamType : JOIN_KEY_MAP.keySet()) {
                if (teamType == vo.getTeamType()) {
                    continue;
                }
                FirstKingdomVO.TeamAttackVO teamAttackVO = new FirstKingdomVO.TeamAttackVO();
                teamAttackVO.setTeamType(teamType);
                teamAttackVO.setScore(activityCommonRedis.getCommonZSetRankingScore(allPointsKey, String.valueOf(teamType)));
                teamAttackVO.setMyAttackCost(Integer.parseInt(activityConfigMap.getOrDefault(String.format(A_ATTACK_B_FIELD, vo.getTeamType(), teamType), "0")));
                teamAttackVO.setMyDefendCost(Integer.parseInt(activityConfigMap.getOrDefault(String.format(A_ATTACK_B_FIELD, teamType, vo.getTeamType()), "0")));
                teamAttackList.add(teamAttackVO);
            }
            vo.setTeamAttackList(teamAttackList);
        } else {
            vo.setKingLevel(0);
            vo.setKingMemberCount(0);
            vo.setTeamAttackList(Collections.emptyList());
        }
        vo.setChanceNum(Integer.parseInt(userConfigMap.getOrDefault(LEFT_CHANCE_FIELD, "0")));
        vo.setIsGetLevel3Reward(Integer.parseInt(userConfigMap.getOrDefault(LEVEL3_GET_FIELD, "0")));
        vo.setMyUpdateLevelNum(Integer.parseInt(userConfigMap.getOrDefault(BUILD_LEVEL_FIELD, "0")));
        vo.setMyAttackNum(activityCommonRedis.getCommonZSetRankingScore(attackPointsKey, uid));

        ActorData actorData = actorDao.getActorDataFromCache(uid);
        vo.setName(actorData.getName());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));

        List<String> jsonList = activityCommonRedis.getCommonListPageRecord(rollRecordKey, 0, 2);
        List<FirstKingdomVO.ResourceMetaTmp> resultList = new ArrayList<>();
        for (String json : jsonList) {
            FirstKingdomVO.ResourceMetaTmp rollRecord = JSON.parseObject(json, FirstKingdomVO.ResourceMetaTmp.class);
            rollRecord.setName(actorDao.getActorDataFromCache(rollRecord.getAid()).getName());
            resultList.add(rollRecord);
        }
        vo.setRollRecordList(resultList);

        return vo;
    }

    public FirstKingdomVO rankInfo(String activityId, String uid) {
        FirstKingdomVO vo = new FirstKingdomVO();
        fillTeamInfoList(activityId, uid, vo);
        vo.setAttackRankingList(getRankingList(getAttackPointsKey(activityId)));
        vo.setBuildRankingList(getRankingList(getBuildPointsKey(activityId)));
        return vo;
    }

    private void fillTeamInfoList(String activityId, String uid, FirstKingdomVO vo) {
        String userConfigKey = getUserConfigKey(activityId, uid);
        String allPointsKey = getAllPointsKey(activityId);
        int myTeamType = activityCommonRedis.getCommonHashValue(userConfigKey, TEAM_FIELD);
        List<FirstKingdomVO.TeamInfoListVO> teamInfoList = new ArrayList<>();
        Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(allPointsKey, 3);
        int index = 0;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            int teamType = Integer.parseInt(entry.getKey());
            if (teamType == myTeamType) {
                vo.setTeamInfoListIndex(index);
            }
            int score = entry.getValue();
            FirstKingdomVO.TeamInfoListVO teamInfo = new FirstKingdomVO.TeamInfoListVO();
            teamInfo.setTeamType(teamType);
            teamInfo.setScore(score);
            List<OtherRankingListVO> rankingList = new ArrayList<>();
            OtherRankingListVO myRank = new OtherRankingListVO();
            makeOtherRankingData(rankingList, myRank, getTeamPointsKey(activityId, teamType), uid, 10, true);
            teamInfo.setRankingList(rankingList);
            teamInfo.setMyRank(myRank);
            teamInfoList.add(teamInfo);
            index++;
        }
        vo.setTeamInfoList(teamInfoList);
    }

    private List<OtherRankingListVO> getRankingList(String rankingKey) {
        List<OtherRankingListVO> rankingList = new ArrayList<>();
        Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(rankingKey, 5);
        int rank = 1;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            OtherRankingListVO rankingVO = new OtherRankingListVO();
            String aid = entry.getKey();
            ActorData rankActor = actorDao.getActorDataFromCache(aid);
            rankingVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            rankingVO.setName(rankActor.getName());
            rankingVO.setUid(aid);
            rankingVO.setScore(entry.getValue());
            rankingVO.setRank(rank);
            rankingVO.setRidData(rankActor.getRidData());
            rankingList.add(rankingVO);
            rank += 1;
        }
        return rankingList;
    }


    /**
     *
     * @param activityId
     * @param uid
     * @param teamType 0-系统分配 1-钢铁王国 2-自然王国 3-自由王国
     * @return
     */
    public FirstKingdomVO joinTeam(String activityId, String uid, int teamType) {
        checkActivityTime(activityId);
        String userConfigKey = getUserConfigKey(activityId, uid);
        int myTeamType = activityCommonRedis.getCommonHashValue(userConfigKey, TEAM_FIELD);
        if (myTeamType != 0) {
            logger.info("already join team. uid={} myTeamType={}", uid, myTeamType);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "already join team");
        }
        if (teamType != 0 && teamType != STEEL_KINGDOM && teamType != NATURE_KINGDOM && teamType != FREEDOM_KINGDOM) {
            logger.info("invalid team type. uid={} teamType={}", uid, teamType);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "invalid team type");
        }
        if (teamType != 0) {
            String teamJoinKey = getTeamJoinKey(activityId, teamType);
            activityCommonRedis.addCommonSetData(teamJoinKey, uid);
            activityCommonRedis.setCommonHashNum(userConfigKey, TEAM_FIELD, teamType);
        }else {
            // 系统分配
            randomJoinTeam(activityId, uid);

        }
        return config(activityId, uid);
    }

    /**
     * 随机分配用户到队伍
     * 1. 自动将用户分配到人数较少的队伍
     * 2. 人数相同时，分配用户给繁荣度较低的队伍
     *
     * @param activityId 活动ID
     * @param uid 用户ID
     */
    private void randomJoinTeam(String activityId, String uid) {
        String userConfigKey = getUserConfigKey(activityId, uid);
        String allPointsKey = getAllPointsKey(activityId);

        // 获取所有队伍的信息
        List<TeamInfo> teamInfoList = new ArrayList<>();

        // 遍历所有队伍类型
        for (Integer teamType : JOIN_KEY_MAP.keySet()) {
            TeamInfo teamInfo = new TeamInfo();
            teamInfo.teamType = teamType;

            // 获取队伍成员数量
            String teamJoinKey = getTeamJoinKey(activityId, teamType);
            teamInfo.memberCount = activityCommonRedis.getCommonSetNum(teamJoinKey);

            // 获取队伍繁荣度
            teamInfo.prosperityScore = activityCommonRedis.getCommonZSetRankingScore(allPointsKey, String.valueOf(teamType));

            teamInfoList.add(teamInfo);
        }

        // 按照规则排序：先按人数升序，人数相同时按繁荣度升序
        teamInfoList.sort((t1, t2) -> {
            if (t1.memberCount != t2.memberCount) {
                return Integer.compare(t1.memberCount, t2.memberCount);
            }
            return Integer.compare(t1.prosperityScore, t2.prosperityScore);
        });

        // 选择排序后的第一个队伍（人数最少，人数相同时繁荣度最低）
        TeamInfo selectedTeam = teamInfoList.get(0);
        int selectedTeamType = selectedTeam.teamType;

        // 将用户加入选中的队伍
        String teamJoinKey = getTeamJoinKey(activityId, selectedTeamType);
        activityCommonRedis.addCommonSetData(teamJoinKey, uid);
        activityCommonRedis.setCommonHashNum(userConfigKey, TEAM_FIELD, selectedTeamType);

        logger.info("randomJoinTeam success. uid={} selectedTeamType={} memberCount={} prosperityScore={}",
                uid, selectedTeamType, selectedTeam.memberCount, selectedTeam.prosperityScore);
    }

    /**
     * 队伍信息内部类
     */
    private static class TeamInfo {
        int teamType;
        int memberCount;
        int prosperityScore;
    }

    public void collectReward(String activityId, String uid) {
        checkActivityTime(activityId);
        String userConfigKey = getUserConfigKey(activityId, uid);
        Map<String, String> userConfigMap = activityCommonRedis.getCommonHashAllMapStr(userConfigKey);
        int myTeamType = Integer.parseInt(userConfigMap.getOrDefault(TEAM_FIELD, "0"));
        if (myTeamType == 0) {
            logger.info("no team. uid={}", uid);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "no team join");
        }
        if (Integer.parseInt(userConfigMap.getOrDefault(LEVEL3_GET_FIELD, "0")) == 1) {
            logger.info("already get reward. uid={}", uid);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "already get reward");
        }
        String allPointsKey = getAllPointsKey(activityId);
        int myTeamScore = activityCommonRedis.getCommonZSetRankingScore(allPointsKey, String.valueOf(myTeamType));
        if (myTeamScore < KINGDOM_LEVEL_LIST.get(3)) {
            logger.info("not enough score. uid={} myTeamScore={} needScore={}", uid, myTeamScore, KINGDOM_LEVEL_LIST.get(3));
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "not enough score");
        }
        handleRes(uid, JOIN_KEY_MAP.get(myTeamType), "first_kingdom_level3_reward");
        activityCommonRedis.setCommonHashNum(userConfigKey, LEVEL3_GET_FIELD, 1);
    }


    public FirstKingdomVO draw(String activityId, String uid, int amount, int drawType, int attackTeamType) {
        if (!inActivityTime(activityId)) {
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }
        if (amount != 1 && amount != 10 && amount != 50 && drawType != 1 && drawType != 2) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        if (drawType == 2 && attackTeamType != STEEL_KINGDOM && attackTeamType != NATURE_KINGDOM && attackTeamType != FREEDOM_KINGDOM) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }


        FirstKingdomVO vo = new FirstKingdomVO();
        int baseDrawNum = 100;
        int drawNum = baseDrawNum * amount;
        String userConfigKey = getUserConfigKey(activityId, uid);
        String rollRecordKey = getRollRecordKey(activityId);
        String allPointsKey = getAllPointsKey(activityId);
        String activityIdConfigKey = getActivityIdConfigKey(activityId);

        // 弹窗用
        List<FirstKingdomVO.ResourceMetaTmp> popList = new ArrayList<>();
        Map<String, FirstKingdomVO.ResourceMetaTmp> countMap = new HashMap<>();
        // 个人历史记录
        List<FirstKingdomVO.ResourceMetaTmp> srcList = new ArrayList<>();
        synchronized (stringPool.intern(getLocalLockKey(activityId, uid))) {
            Map<String, String> userConfigMap = activityCommonRedis.getCommonHashAllMapStr(userConfigKey);
            int myTeamType = Integer.parseInt(userConfigMap.getOrDefault(TEAM_FIELD, "0"));
            if (myTeamType == 0) {
                logger.info("no team. uid={}", uid);
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "no team join");
            }
            int beforeChance = Integer.parseInt(userConfigMap.getOrDefault(LEFT_CHANCE_FIELD, "0"));
            if (beforeChance < drawNum) {
                logger.info("insufficient points. uid={} currentPoints={} drawNum={}", uid, beforeChance, drawNum);
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER_POINT);
            }

            if (drawType == 1) {
                // 建造本国堡垒
                String teamPointsKey = getTeamPointsKey(activityId, myTeamType);
                String buildPointsKey = getBuildPointsKey(activityId);
                // 扣除积分
                int afterChance = activityCommonRedis.incCommonHashNum(userConfigKey, LEFT_CHANCE_FIELD, -drawNum);
                doReportSpecialItemsEvent(activityId, uid, 2, 3, drawNum);

                int score = drawNum;
                int beforeBuildScore = Integer.parseInt(userConfigMap.getOrDefault(BUILD_SCORE_FIELD, "0"));
                int afterBuildScore = activityCommonRedis.incCommonHashNum(userConfigKey, BUILD_SCORE_FIELD, score);

                int oldLevel = getNewBaseIndexLevel(beforeBuildScore / POINT_PER_LEVEL, BUILD_LEVEL_LIST);
                int newLevel = getNewBaseIndexLevel(afterBuildScore / POINT_PER_LEVEL, BUILD_LEVEL_LIST);
                List<String> keyList = new ArrayList<>();
                if (newLevel > oldLevel) {
                    // 跨级的，需要把之前等级的key都下发
                    for (int level = oldLevel + 1; level <= newLevel; level++) {
                        if (level < BULID_KEY_LEVEL_LIST.size() && StringUtils.hasLength(BULID_KEY_LEVEL_LIST.get(level))) {
                            String resKey = BULID_KEY_LEVEL_LIST.get(level);
                            String eventTitle = BULID_EVENT_LEVEL_LIST.get(level);
                            resourceKeyHandlerService.sendResourceData(uid, resKey, eventTitle, eventTitle, eventTitle, ACTIVITY_URL, "");
                            keyList.add(resKey);
                            logger.info("sendGiftHandle reward sent to uid: {}, level: {}, resKey: {}", uid, level, resKey);
                        }
                    }
                    activityCommonRedis.setCommonHashNum(userConfigKey, BUILD_LEVEL_FIELD, newLevel);
                }

                activityCommonRedis.incrCommonZSetRankingScoreSimple(teamPointsKey, uid, drawNum);
                activityCommonRedis.incrCommonZSetRankingScoreSimple(buildPointsKey, uid, drawNum);
                activityCommonRedis.incrCommonZSetRankingScoreSimple(allPointsKey, String.valueOf(myTeamType), score);

                if (!keyList.isEmpty()) {
                    List<ResourceKeyConfigData> resConfigDataList = resourceKeyHandlerService.findListByResourceKeyList(keyList);
                    for (ResourceKeyConfigData item : resConfigDataList) {
                        for (ResourceKeyConfigData.ResourceMeta resourceMeta : item.getResourceMetaList()) {
                            FirstKingdomVO.ResourceMetaTmp historyMeta = new FirstKingdomVO.ResourceMetaTmp();
                            BeanUtils.copyProperties(resourceMeta, historyMeta);
                            historyMeta.setDrawType(1);
                            historyMeta.setCtime(DateHelper.getNowSeconds());
                            historyMeta.setCostNum(drawNum);
                            srcList.add(historyMeta);

                            if (!countMap.containsKey(resourceMeta.getMetaId())) {
                                historyMeta.setAwardNum(1);
                                countMap.put(resourceMeta.getMetaId(), historyMeta);
                            } else {
                                countMap.get(resourceMeta.getMetaId())
                                        .setAwardNum(countMap.get(resourceMeta.getMetaId()).getAwardNum() + 1);
                            }
                            // 历史记录
                            leftPushAllHistoryList(uid, srcList);
                            // 弹窗列表
                            popList.addAll(countMap.values());
                        }
                    }
                } else {
                    FirstKingdomVO.ResourceMetaTmp historyMeta = new FirstKingdomVO.ResourceMetaTmp();
                    historyMeta.setDrawType(1);
                    historyMeta.setCtime(DateHelper.getNowSeconds());
                    historyMeta.setCostNum(drawNum);
                    srcList.add(historyMeta);
                }
                vo.setChanceNum(afterChance);

                FirstKingdomVO.ResourceMetaTmp rollRecord = new FirstKingdomVO.ResourceMetaTmp();
                rollRecord.setAid(uid);
                rollRecord.setDrawType(1);
                rollRecord.setSocre(score);
                activityCommonRedis.addCommonListRecord(rollRecordKey, JSONObject.toJSONString(rollRecord));
                logger.info("build score. uid={} score={} afterChance={} oldLevel={} newLevel={}"
                        , uid, score, afterChance, oldLevel, newLevel);
            } else {
                // 攻击他国堡垒
                if (attackTeamType == myTeamType) {
                    logger.info("attackTeamType == myTeamType. uid={} attackTeamType={} myTeamType={}", uid, attackTeamType, myTeamType);
                    throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "can not attack your own team");
                }
                String teamPointsKey = getTeamPointsKey(activityId, attackTeamType);
                String attackPointsKey = getAttackPointsKey(activityId);
                String aAttackBKey = String.format(A_ATTACK_B_FIELD, myTeamType, attackTeamType);

                int score = drawNum / 2;
                synchronized (getLocalTeamLockKey(activityId, attackTeamType)) {
                    int beforeAttackScore = activityCommonRedis.getCommonZSetRankingScore(allPointsKey, String.valueOf(attackTeamType));
                    if (beforeAttackScore <= 0) {
                        logger.info("beforeAttackScore <= 0. attackTeamType={} beforeAttackScore={}", attackTeamType, beforeAttackScore);
                        throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER_POINT.getCode(), "no points to attack");
                    }
                    int attackScore = Math.min(score, beforeAttackScore);
                    activityCommonRedis.incrCommonZSetRankingScoreSimple(allPointsKey, String.valueOf(attackTeamType), -attackScore);
                }

                // 扣除积分
                int afterChance = activityCommonRedis.incCommonHashNum(userConfigKey, LEFT_CHANCE_FIELD, -drawNum);
                doReportSpecialItemsEvent(ACTIVITY_ID, uid, 2, 3, drawNum);

                activityCommonRedis.incrCommonZSetRankingScoreSimple(teamPointsKey, uid, drawNum);
                activityCommonRedis.incrCommonZSetRankingScoreSimple(attackPointsKey, uid, drawNum);
                activityCommonRedis.incCommonHashNum(activityIdConfigKey, aAttackBKey, drawNum);

                // 最近30天充值金额
                int rechargeMoney = rechargeDailyInfoDao.getUserLastRechargeCache(uid, 30);
                String drawPoolKey = rechargeMoney >= 5 ? ATTACK_PAY_DRAW_KEY : ATTACK_NO_PAY_DRAW_KEY;
                for (int i = 0; i < amount; i++) {
                    ResourceKeyConfigData.ResourceMeta resourceMeta = drawOne(uid, drawPoolKey, eventDrawTitle);
                    FirstKingdomVO.ResourceMetaTmp historyMeta = new FirstKingdomVO.ResourceMetaTmp();
                    BeanUtils.copyProperties(resourceMeta, historyMeta);
                    historyMeta.setDrawType(2);
                    historyMeta.setCostNum(baseDrawNum);
                    historyMeta.setCtime(DateHelper.getNowSeconds());
                    srcList.add(historyMeta);

                    if (!countMap.containsKey(resourceMeta.getMetaId())) {
                        historyMeta.setAwardNum(1);
                        countMap.put(resourceMeta.getMetaId(), historyMeta);
                    } else {
                        countMap.get(resourceMeta.getMetaId())
                                .setAwardNum(countMap.get(resourceMeta.getMetaId()).getAwardNum() + 1);
                    }
                    // 历史记录
                    leftPushAllHistoryList(uid, srcList);
                    // 弹窗列表
                    popList.addAll(countMap.values());
                }
                vo.setChanceNum(afterChance);

                FirstKingdomVO.ResourceMetaTmp rollRecord = new FirstKingdomVO.ResourceMetaTmp();
                BeanUtils.copyProperties(srcList.get(ThreadLocalRandom.current().nextInt(srcList.size())), rollRecord);
                rollRecord.setAid(uid);
                rollRecord.setSocre(score);
                activityCommonRedis.addCommonListRecord(rollRecordKey, JSONObject.toJSONString(rollRecord));

                logger.info("attack success. uid={} sore={} myTeamType={} attackTeamType={} afterChance={}"
                        , uid, score, myTeamType, attackTeamType, afterChance);
            }
            vo.setAwardList(popList);

        }

        return vo;
    }


    public PageVO<FirstKingdomVO.ResourceMetaTmp> getHistoryListPageRecord(String activityId, String uid, int page) {
        PageVO<FirstKingdomVO.ResourceMetaTmp> vo = new PageVO<>();
        int start = (page - 1) * PAGE_SIZE;
        int end = page * PAGE_SIZE;
        String key = getListHistoryKey(activityId, uid);
        List<String> jsonList = activityCommonRedis.getCommonListPageRecord(key, start, end);
        List<FirstKingdomVO.ResourceMetaTmp> resultList = new ArrayList<>();
        for (String json : jsonList) {
            FirstKingdomVO.ResourceMetaTmp historyData = JSON.parseObject(json, FirstKingdomVO.ResourceMetaTmp.class);
            resultList.add(historyData);
        }
        vo.setList(resultList);
        vo.setNextUrl(resultList.size() < PAGE_SIZE ? "" : String.valueOf(page + 1));
        return vo;
    }


    public void sendGiftHandle(SendGiftData data, String activityId) {
        int sendD = data.getNumber() * data.getPrice() * data.getAid_list().size();
        String uid = data.getFrom_uid();
        String userConfigKey = getUserConfigKey(activityId, uid);

        int afterDiamond = activityCommonRedis.incCommonHashNum(userConfigKey, TOTAL_DIAMOND_FIELD, sendD);
        activityCommonRedis.incCommonHashNum(userConfigKey, LEFT_CHANCE_FIELD, sendD);
        doReportSpecialItemsEvent(ACTIVITY_ID, uid, 1, 1, sendD);

        logger.info("sendGiftHandle uid:{} sendD:{}  afterDiamond:{} ", uid, sendD, afterDiamond);
    }


    public void leftPushAllHistoryList(String uid, List<FirstKingdomVO.ResourceMetaTmp> srcList) {
        String key = getListHistoryKey(ACTIVITY_ID, uid);
        List<String> strList = new ArrayList<>();
        for (FirstKingdomVO.ResourceMetaTmp resourceMeta : srcList) {
            strList.add(JSONObject.toJSONString(resourceMeta));
        }
        if (CollectionUtils.isEmpty(strList)) {
            return;
        }
        activityCommonRedis.leftPushAllCommonList(key, strList, HISTORY_USER_MAX_SIZE);
    }

    private boolean checkAc(String uid) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTest = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
            if (!isWhiteTest) {
                // 灰度测试,只统计测试用户的
                return false;
            }
        }
        return true;
    }


    // 下发榜单奖励
    public void distributionTotalRanking(String activityId) {
        try {
            String allPointsKey = getAllPointsKey(activityId);
            Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(allPointsKey, 3);
            int rank = 1;
            for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
                int teamType = Integer.parseInt(entry.getKey());
                String teamPointsKey = getTeamPointsKey(activityId, teamType);
                switch (rank) {
                    case 1:
                        distributionSubRanking(teamPointsKey, TOP1_KEY_LEVEL_LIST, "First Kingdom NO.1");
                        break;
                    case 2:
                        distributionSubRanking(teamPointsKey, TOP2_KEY_LEVEL_LIST, "First Kingdom NO.2");
                        break;
                    case 3:
                        distributionSubRanking(teamPointsKey, TOP3_KEY_LEVEL_LIST, "First Kingdom NO.3");
                        break;
                    default:
                        break;
                }
                rank += 1;
            }
        } catch (Exception e) {
            logger.error("distributionRanking error: {}", e.getMessage(), e);
        }
    }

    private void distributionSubRanking(String teamPointsKey, List<String> resKeyList, String eventTitle) {
        Map<String, Integer> rankingSubMap = activityCommonRedis.getCommonRankingMap(teamPointsKey, resKeyList.size());
        int rank = 1;
        for (Map.Entry<String, Integer> entry : rankingSubMap.entrySet()) {
            String aid = entry.getKey();
            String resKey = resKeyList.get(rank - 1);
            handleRes(aid, resKey, eventTitle);
            rank += 1;
        }
    }

    private void handleRes(String aid, String resKey, String eventTitle) {
        resourceKeyHandlerService.sendResourceData(aid, resKey,
                eventTitle, eventTitle, eventTitle, ACTIVITY_URL, "");
    }

    /**
     * 上报机会获取消耗事件
     */
    private void doReportSpecialItemsEvent(String activityId, String uid, int action, int source, int num) {
        ActivitySpecialItemsChangeEvent event = new ActivitySpecialItemsChangeEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setChange_action(action);
        event.setActivity_special_items_id("0");
        event.setActivity_name(ACTIVITY_TITLE_EN);
        event.setActive_id(activityId);
        event.setActivity_special_items_resource(source);
        event.setResource_desc("");
        event.setChange_nums(num);
        eventReport.track(new EventDTO(event));
    }


}
