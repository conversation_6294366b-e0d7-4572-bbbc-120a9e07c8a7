package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.GetActivitySpecialItemsRecordEvent;
import com.quhong.analysis.StarBeatGameLogEvent;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.dto.SendChatMsgDTO;
import com.quhong.data.vo.LuckyNumberVO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.data.vo.PrizeConfigVO;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.enums.MsgType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IMsgService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Service
public class LuckyNumberService extends OtherActivityService implements DailyTaskHandler {


    private static final Logger logger = LoggerFactory.getLogger(LuckyNumberService.class);
    public static  String ACTIVITY_ID = "68c95d4b0d8daa4cbe4866af";
    private static final String ACTIVITY_TITLE_EN = "Lucky Numbers";
    private static final String ACTIVITY_TITLE_AR = "الرقم المحظوظ";
    private static final String ACTIVITY_TITLE_EN_1_PAY = "Lucky Numbers-draw1 reward1";
    private static final String ACTIVITY_TITLE_EN_9_PAY = "Lucky Numbers-draw9 reward1";
    private static final String ACTIVITY_TITLE_EN_1_NO_PAY = "Lucky Numbers-draw1 reward2";
    private static final String ACTIVITY_TITLE_EN_9_NO_PAY = "Lucky Numbers-draw9 reward2";
    private static final String ACTIVITY_TITLE_EN_1_SPECIAL = "Lucky Numbers-draw1 reward3";
    private static final String ACTIVITY_TITLE_EN_9_SPECIAL = "Lucky Numbers-draw9 reward3";

    private static final String ACTIVITY_LEFT_BEAN = "leftBean";
    private static final String ACTIVITY_LUCKY_TIMES = "luckyTimes";
    private static  Integer SHARE_ID =  74 ;
    // meteId集合
    private static final List<String> LUCKY_NICE_LIST_KEY = Arrays.asList("lucky1", "lucky2", "lucky3", "lucky4", "lucky5", "lucky6");
    // lucky_number
    private static  String ACTIVITY_URL = String.format("https://static.youstar.live/lucky_clover2025/?activityId=%s&shareId=%s", ACTIVITY_ID, SHARE_ID);
    private static final String ACTIVITY_BROADCAST_ICON = "";
    private static final List<Integer> GAME_TYPE_LIST = Arrays.asList(0, 1); // 游戏类型 0: 数字  1: 图案
    private static final List<Integer> DRAW_NUM_LIST = Arrays.asList(1, 10);
    private static final Interner<String> stringPool = Interners.newWeakInterner();
    private static final List<String> DRAW_USER_LIST = Arrays.asList("616f2433541b4e39f7dabca4", "62a45b81c8dad4488d482810", "5c88a0f166dc630038467c4e",
            "5ab6ac5c1bad4814cbd56daa", "62a3fb110460bd3470eb8c76", "5e3521cfb271b6040a95e13a", "5d82dd40abb01a009560e3a4", "6606588cbe90384a14cd5279", "5cc819f966dc630025bf64fa");
    // 1图刮刮乐资源key
    private static final List<String> DRAW_NUMBER_RES_KEY = Arrays.asList("Luckynumber1paydraw", "Luckynumber1nopaydraw", "Luckynumber1specialdraw");
    // 9图刮刮乐资源key
    private static final List<String> DRAW_NICE_RES_KEY = Arrays.asList("Luckynumber9paydraw", "Luckynumber9nopaydraw", "Luckynumber9specialdraw");
    private static final int LIMIT_INIT_POOL = 30;
    private static final Integer RECORD_PAGE_SIZE = 10;
    // 1图刮刮中奖meteId
    private static final Map<String, Integer> LUCKY_NUMBER_MAP = new HashMap<>();
    private static final List<Integer> LUCKY_NUMBER_LIST = Arrays.asList(18, 188, 288, 388, 488, 688, 8, 888);
    private static final List<String> SUPER_LUCKY_NUMBER_LIST = Arrays.asList("LuckyClover8", "LuckyClover888");

    // 9图刮刮中奖meteId
    private static final List<String> LUCKY_NICE_LIST = Arrays.asList("lucky1", "lucky2", "lucky3", "lucky4", "lucky5", "lucky6");
    private static final String LUCKY_BADGE_STATUS = "badgeStatus";

    static {
        LUCKY_NUMBER_MAP.put("LuckyClover18", 18);
        LUCKY_NUMBER_MAP.put("LuckyClover188", 188);
        LUCKY_NUMBER_MAP.put("LuckyClover288", 288);
        LUCKY_NUMBER_MAP.put("LuckyClover388", 388);
        LUCKY_NUMBER_MAP.put("LuckyClover488", 488);
        LUCKY_NUMBER_MAP.put("LuckyClover688", 688);
        LUCKY_NUMBER_MAP.put("LuckyClover8", 8);
        LUCKY_NUMBER_MAP.put("LuckyClover888", 888);
    }

    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private IMsgService iMsgService;
    @Resource
    private EventReport eventReport;


    @PostConstruct
    public void init() {

        if (ServerConfig.isNotProduct()) {
            ACTIVITY_ID = "68c28cb6b3fb2ca181ac84f0";
            SHARE_ID = 31;
            ACTIVITY_URL = String.format("https://test2.qmovies.tv/lucky_clover2025/?activityId=%s&shareId=%s", ACTIVITY_ID, SHARE_ID);
        }
    }

    // 抽奖相关的每日key
    private String getDailyDate(String activityId) {
        return String.format("dailyDate:%s", activityId);
    }

    private String getHashActivityId(String activityId, String uid) {
        return String.format("luckyNumber:%s:%s", activityId, uid);
    }

    private String getHashDailyActivityId(String activityId, String uid, String dateStr) {
        return String.format("luckyNumberDaily:%s:%s:%s", activityId, uid, dateStr);
    }

    // 每日发礼物排行榜
    private String getDailyGiftRankKey(String activityId, String dateStr) {
        return String.format("dailySendRank:%s:%s", activityId, dateStr);
    }

    // 刮到超级幸运数字的用户
    private String getDailyFinishLuckyKey(String activityId, String dateStr) {
        return String.format("dailyFinishLucky:%s:%s", activityId, dateStr);
    }

    // 抽奖品Key
    private String getListDrawPrizeKey(String activityId, String resKey) {
        return String.format("drawPrize:%s:%s", activityId, resKey);
    }

    // 历史记录key
    private String getHistoryRecordListKey(String activityId, String uid, int gameType) {
        return String.format("historyRecord:%s:%s:%s", activityId, uid, gameType);
    }

    // 发送记录
    private String getSendDrawTimeKey(String activityId, String uid) {
        return String.format("sendDrawTime:%s:%s", activityId, uid);
    }

    // 每日对同一个用户只能赠送一次
    private String getSendTimesSetKey(String activityId, String uid, String dateStr) {
        return String.format("sendTimesSet:%s:%s:%s", activityId, uid, dateStr);
    }

    public LuckyNumberVO luckyNumberConfig(String activityId, String uid) {

        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        LuckyNumberVO vo = new LuckyNumberVO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());

        String currentDay = getDayByBase(activityId, uid);
        // String currentDay = activityCommonRedis.getCommonStrValue(this.getDailyDate(activityId));
        String hashDailyActivityId = getHashDailyActivityId(activityId, uid, currentDay);
        Map<String, Integer> userDailyDataMap = activityCommonRedis.getCommonHashAll(hashDailyActivityId);
        vo.setTimes(userDailyDataMap.getOrDefault(ACTIVITY_LUCKY_TIMES, 0));
        vo.setLeftBeans(100 - userDailyDataMap.getOrDefault(ACTIVITY_LEFT_BEAN, 0));

        String hashActivityId = getHashActivityId(activityId, uid);
        Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);


        // 设置每日排行榜
        String dateRankKey = getDailyGiftRankKey(activityId, currentDay);
        List<OtherRankingListVO> rankingList = new ArrayList<>();
        OtherRankingListVO myRank = new OtherRankingListVO();
        makeOtherRankingData(rankingList, myRank, dateRankKey, uid, 3);
        vo.setGiftRankList(rankingList);
        vo.setMyGiftRank(myRank);

        // 超级幸运数字用户list
        List<OtherRankingListVO> finishLuckyRankList = new ArrayList<>();
        List<String> finishLuckyList = activityCommonRedis.getCommonRangeRankingList(getDailyFinishLuckyKey(activityId, currentDay), 15);
        for (String luckyUid : finishLuckyList) {
            OtherRankingListVO luckyRankVO = new OtherRankingListVO();
            ActorData rankActor = actorDao.getActorDataFromCache(luckyUid);
            luckyRankVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            luckyRankVO.setName(rankActor.getName());
            luckyRankVO.setUid(luckyUid);
            luckyRankVO.setRidData(rankActor.getRidData());
            finishLuckyRankList.add(luckyRankVO);
        }
        vo.setLuckyRankList(finishLuckyRankList);

        // 设置9图获取情况
        List<PrizeConfigVO> luckyNiceList = new ArrayList<>();
        for (String luckyKey : LUCKY_NICE_LIST_KEY) {
            PrizeConfigVO luckyVO = new PrizeConfigVO();
            luckyVO.setDrawType(luckyKey);
            luckyVO.setStatus(userDataMap.getOrDefault(luckyKey, 0));
            luckyNiceList.add(luckyVO);
        }
        vo.setLuckyNiceList(luckyNiceList);
        return vo;
    }


    // 发送礼物抽奖
    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        int totalPrice = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size();
        String fromUid = giftData.getFrom_uid();

        String currentDay = getDayByBase(activityId, fromUid);
        // String currentDay = activityCommonRedis.getCommonStrValue(this.getDailyDate(activityId));
        String hashDailyActivityId = getHashDailyActivityId(activityId, fromUid, currentDay);
        Map<String, Integer> userDailyDataMap = activityCommonRedis.getCommonHashAll(hashDailyActivityId);
        int leftBean = userDailyDataMap.getOrDefault(ACTIVITY_LEFT_BEAN, 0);
        int afterBean = leftBean + totalPrice;
        int incNum = afterBean / 100;
        leftBean = afterBean % 100;

        activityCommonRedis.setCommonHashNum(hashDailyActivityId, ACTIVITY_LEFT_BEAN, leftBean);
        if (incNum > 0) {
            activityCommonRedis.incCommonHashNum(hashDailyActivityId, ACTIVITY_LUCKY_TIMES, incNum);
            this.doDrawTimesEvent("", fromUid, incNum, 1);
        }
        activityCommonRedis.incrCommonZSetRankingScore(getDailyGiftRankKey(activityId, currentDay), fromUid, totalPrice);
    }


    // 获取抽奖配置
    private ResourceKeyConfigData getResourceKeyConfig(String uid, int gameType) {
        List<String> resKeyList = gameType == 0 ? DRAW_NUMBER_RES_KEY : DRAW_NICE_RES_KEY;

        // if (DRAW_USER_LIST.contains(uid)) {
        //     return resourceKeyConfigDao.findByKey(resKeyList.get(2));
        // }
        int rechargeMoney = rechargeDailyInfoDao.getUserLastRechargeCache(uid, 30);
        if (rechargeMoney >= 5) {
            return resourceKeyConfigDao.findByKey(resKeyList.get(0));
        } else {
            return resourceKeyConfigDao.findByKey(resKeyList.get(1));
        }
    }

    private String getResTitleByKey(String resKey, int gameType) {
        List<String> resKeyList = gameType == 0 ? DRAW_NUMBER_RES_KEY : DRAW_NICE_RES_KEY;
        if (resKeyList.get(0).equals(resKey)) {
            return gameType == 0 ? ACTIVITY_TITLE_EN_1_PAY : ACTIVITY_TITLE_EN_9_PAY;
        }
        if (resKeyList.get(1).equals(resKey)) {
            return gameType == 0 ? ACTIVITY_TITLE_EN_1_NO_PAY : ACTIVITY_TITLE_EN_9_NO_PAY;
        }
        return gameType == 0 ? ACTIVITY_TITLE_EN_1_SPECIAL : ACTIVITY_TITLE_EN_9_SPECIAL;
    }

    /**
     * 使用卡片抽奖
     */
    private void initDrawPrizePool(Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap, String drawPrizeKey) {
        int poolSize = activityCommonRedis.getCommonListSize(drawPrizeKey);
        if (poolSize <= LIMIT_INIT_POOL) {
            List<String> poolList = new ArrayList<>();
            for (String prizeKey : resourceMetaMap.keySet()) {
                ResourceKeyConfigData.ResourceMeta resourceMeta = resourceMetaMap.get(prizeKey);
                int rateNumber = Integer.parseInt(resourceMeta.getRateNumber());
                poolList.addAll(Stream.generate(resourceMeta::getMetaId).limit(rateNumber).collect(Collectors.toList()));
            }
            Collections.shuffle(poolList);
            activityCommonRedis.rightPushAllCommonList(drawPrizeKey, poolList);
        }
    }

    // 抽奖
    private String shootDraw(String activityId, Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap, String resKey) {
        String drawPrizeKey = getListDrawPrizeKey(activityId, resKey);
        this.initDrawPrizePool(resourceMetaMap, drawPrizeKey);
        String awardKey = activityCommonRedis.leftPopCommonListKey(drawPrizeKey);
        if (StringUtils.isEmpty(awardKey)) {
            throw new CommonH5Exception(HttpCode.SERVER_ERROR);
        }
        return awardKey;
    }

    private Integer getOtherLuckyNum() {
        try {
            List<Integer> numberList = IntStream.rangeClosed(1, 1000).boxed().collect(Collectors.toList());
            numberList.removeAll(LUCKY_NUMBER_LIST);
            Collections.shuffle(numberList);
            return numberList.get(0);
        } catch (Exception e) {
            throw new CommonH5Exception(ActivityHttpCode.SERVER_ERROR);
        }
    }

    public LuckyNumberVO luckyNumDraw(String activityId, String uid, int gameType, int amount, String skin) {
        checkActivityTime(activityId);
        if (!GAME_TYPE_LIST.contains(gameType)) {  //
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        if (!DRAW_NUM_LIST.contains(amount)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);

        String currentDay = getDayByBase(activityId, uid);
        // String currentDay = activityCommonRedis.getCommonStrValue(this.getDailyDate(activityId));

        synchronized (stringPool.intern("luckyNumDraw:" + uid)) {
            String hashActivityId = getHashActivityId(activityId, uid);
            Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);

            String hashDailyActivityId = getHashDailyActivityId(activityId, uid, currentDay);
            Map<String, Integer> userDailyDataMap = activityCommonRedis.getCommonHashAll(hashDailyActivityId);

            int luckyTimes = userDailyDataMap.getOrDefault(ACTIVITY_LUCKY_TIMES, 0);
            if (luckyTimes <= 0 || luckyTimes < amount) {
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER);
            }

            int afterNum = activityCommonRedis.incCommonHashNum(hashDailyActivityId, ACTIVITY_LUCKY_TIMES, -amount);
            int currentTime = DateHelper.getNowSeconds();
            List<PrizeConfigVO> drawRecordList = new ArrayList<>();
            String rewardTitleDesc = "";
            LuckyNumberVO vo = new LuckyNumberVO();
            vo.setTimes(afterNum);

            ResourceKeyConfigData resourceKeyConfigData = this.getResourceKeyConfig(uid, gameType);
            if (resourceKeyConfigData == null) {
                throw new CommonH5Exception(ActivityHttpCode.SERVER_ERROR);
            }
            String resKey = resourceKeyConfigData.getKey();
            rewardTitleDesc = this.getResTitleByKey(resKey, gameType);
            // Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap = resourceKeyConfigData.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));
            for (int i = 0; i < amount; i++) {
                // String awardKey = this.shootDraw(activityId, resourceMetaMap, resKey);
                // ResourceKeyConfigData.ResourceMeta resourceMeta = resourceMetaMap.get(awardKey);
                // if (resourceMeta == null) {
                //     continue;
                // }
                // resourceKeyHandlerService.sendOneResourceData(actorData, resourceMeta, 905, rewardTitleDesc, ACTIVITY_TITLE_AR, rewardTitleDesc, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON, 0);
                ResourceKeyConfigData.ResourceMeta resourceMeta = drawOne(uid, resKey, rewardTitleDesc);
                String awardKey = resourceMeta.getMetaId();
                PrizeConfigVO drawRecord = new PrizeConfigVO();
                drawRecord.setUid(uid);
                drawRecord.setDrawType(awardKey);
                drawRecord.setRewardType(String.valueOf(resourceMeta.getResourceType()));
                drawRecord.setNameEn(resourceMeta.getResourceNameEn());
                drawRecord.setNameAr(resourceMeta.getResourceNameAr());
                drawRecord.setIconEn(resourceMeta.getResourceIcon());
                drawRecord.setRewardTime(resourceMeta.getResourceTime());
                drawRecord.setRewardNum(resourceMeta.getResourceNumber());
                drawRecord.setRewardPrice(resourceMeta.getResourcePrice());
                drawRecord.setCtime(currentTime);

                // 如果是刮数字, 要找对应关系
                if (gameType == 0) {
                    int luckyNum = LUCKY_NUMBER_MAP.getOrDefault(awardKey, 0);
                    luckyNum = luckyNum == 0 ? this.getOtherLuckyNum() : luckyNum;
                    drawRecord.setRateNum(luckyNum);
                    if (SUPER_LUCKY_NUMBER_LIST.contains(awardKey)) {
                        String dailyFinishLuckyKey = getDailyFinishLuckyKey(activityId, currentDay);
                        int finishTime = activityCommonRedis.getCommonZSetRankingScore(dailyFinishLuckyKey, uid);
                        if (finishTime <= 0) {
                            activityCommonRedis.addCommonZSetRankingScore(getDailyFinishLuckyKey(activityId, currentDay), uid, currentTime);
                        }
                    }
                }

                if (gameType == 1 && LUCKY_NICE_LIST.contains(awardKey)) {
                    this.handleAllCollectBadgeReward(uid, userDataMap, awardKey, hashActivityId);
                }
                drawRecordList.add(drawRecord);
                String jsonRecord = JSON.toJSONString(drawRecord);
                activityCommonRedis.addCommonListData(getHistoryRecordListKey(activityId, uid, gameType), jsonRecord);
            }
            this.doDrawReportEvent(uid, gameType, amount, skin, rewardTitleDesc, drawRecordList);
            vo.setDrawRecordList(drawRecordList);
            return vo;
        }
    }


    private void handleAllCollectBadgeReward(String uid, Map<String, Integer> userDataMap, String awardKey, String hashActivityId) {
        if (userDataMap.getOrDefault(LUCKY_BADGE_STATUS, 0) > 0) {
            return;
        }

        if (userDataMap.getOrDefault(awardKey, 0) > 0) {
            return;
        }

        userDataMap.put(awardKey, 1);
        activityCommonRedis.setCommonHashNum(hashActivityId, awardKey, 1);
        int totalFinish = 0;
        for (String itemKey : LUCKY_NICE_LIST) {
            totalFinish += userDataMap.getOrDefault(itemKey, 0);
        }
        if (totalFinish >= LUCKY_NICE_LIST.size()) {
            resourceKeyHandlerService.sendResourceData(uid, "luckynumberbadge", 905, "Lucky Numbers-9task", ACTIVITY_TITLE_AR, "Lucky Numbers-9task", ACTIVITY_URL, ACTIVITY_BROADCAST_ICON, 0);
            activityCommonRedis.setCommonHashNum(hashActivityId, LUCKY_BADGE_STATUS, 1);
        }
    }

    private void doDrawReportEvent(String uid, int gameType, int amount, String skin, String rewardTitleDesc, List<PrizeConfigVO> drawRecordList) {
        Map<String, Integer> drawRecordMap = drawRecordList.stream().collect(Collectors.groupingBy(PrizeConfigVO::getNameEn, Collectors.summingInt(PrizeConfigVO::getRewardNum)));
        StarBeatGameLogEvent event = new StarBeatGameLogEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setSence(ACTIVITY_TITLE_EN);
        event.setTicket_type(gameType);
        event.setCost_ticket(amount);
        event.setDraw_nums(amount);
        event.setDraw_success_nums(drawRecordList.size());
        event.setDesc(skin);
        event.setDraw_detail(rewardTitleDesc);
        event.setDraw_result(JSON.toJSONString(drawRecordMap));
        eventReport.track(new EventDTO(event));
    }

    private void doDrawTimesEvent(String fromUid, String toUid, int score, int actionType) {
        GetActivitySpecialItemsRecordEvent event = new GetActivitySpecialItemsRecordEvent();
        event.setUid(toUid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name(ACTIVITY_TITLE_EN);
        event.setActive_id(ACTIVITY_ID);
        event.setActivity_special_items_resource(String.valueOf(actionType));
        event.setFrom_uid(fromUid);
        event.setGet_activity_special_items_nums(score);
        eventReport.track(new EventDTO(event));
    }

    public LuckyNumberVO luckyNumRecord(String activityId, String uid, int gameType, int page) {
        int start = (page - 1) * RECORD_PAGE_SIZE;
        int end = page * RECORD_PAGE_SIZE;
        String recordListKey = getHistoryRecordListKey(activityId, uid, gameType);
        List<String> prizeKeyTimeList = activityCommonRedis.getCommonListPageRecord(recordListKey, start, end);
        LuckyNumberVO drawRecordVO = new LuckyNumberVO();
        List<PrizeConfigVO> drawRecordList = new ArrayList<>();
        for (String item : prizeKeyTimeList) {
            PrizeConfigVO drawRecord = JSON.parseObject(item, PrizeConfigVO.class);
            drawRecordList.add(drawRecord);
        }
        drawRecordVO.setDrawRecordList(drawRecordList);
        if (drawRecordList.size() < RECORD_PAGE_SIZE) {
            drawRecordVO.setNextUrl(-1);
        } else {
            drawRecordVO.setNextUrl(page + 1);
        }
        return drawRecordVO;
    }

    // 赠送抽奖次数
    public void luckyNumSendTimes(String activityId, String uid, String giveRid) {
        checkActivityTime(activityId);
        ActorData aidActor = actorDao.getActorByStrRid(giveRid);
        if (aidActor == null) {
            throw new CommonH5Exception(ActivityHttpCode.INCORRECT_INPUT_ID);
        }
        if (aidActor.getUid().equals(uid)) {
            throw new CommonH5Exception(ActivityHttpCode.QUEEN_CANNOT_SELF);
        }
        int currentTime = DateHelper.getNowSeconds();
        synchronized (stringPool.intern("luckyNumDraw:" + uid)) {
            String currentDay = getDayByBase(activityId, uid);
            // String currentDay = activityCommonRedis.getCommonStrValue(this.getDailyDate(activityId));
            String hashDailyActivityId = getHashDailyActivityId(activityId, uid, currentDay);
            Map<String, Integer> userDailyDataMap = activityCommonRedis.getCommonHashAll(hashDailyActivityId);
            String aid = aidActor.getUid();

            int sendFlag = activityCommonRedis.isCommonSetData(getSendTimesSetKey(activityId, uid, currentDay), aid);
            if (sendFlag > 0) {
                throw new CommonH5Exception(ActivityHttpCode.QUEEN_CANNOT_SELF.getCode(), "مرة واحدة فقط في اليوم");
            }

            int luckyTimes = userDailyDataMap.getOrDefault(ACTIVITY_LUCKY_TIMES, 0);
            if (luckyTimes <= 0) {
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER);
            }
            activityCommonRedis.incCommonHashNum(hashDailyActivityId, ACTIVITY_LUCKY_TIMES, -1);


            String hashAidDailyActivityId = getHashDailyActivityId(activityId, aid, currentDay);
            activityCommonRedis.incCommonHashNum(hashAidDailyActivityId, ACTIVITY_LUCKY_TIMES, 1);
            PrizeConfigVO uidShootRecord = new PrizeConfigVO();
            uidShootRecord.setUid(aid);
            uidShootRecord.setStatus(0);
            uidShootRecord.setCtime(currentTime);


            PrizeConfigVO aidShootRecord = new PrizeConfigVO();
            aidShootRecord.setUid(uid);
            aidShootRecord.setStatus(1);
            aidShootRecord.setCtime(currentTime);
            activityCommonRedis.addCommonListData(getSendDrawTimeKey(activityId, uid), JSONObject.toJSONString(uidShootRecord));
            activityCommonRedis.addCommonListData(getSendDrawTimeKey(activityId, aid), JSONObject.toJSONString(aidShootRecord));
            activityCommonRedis.addCommonSetData(getSendTimesSetKey(activityId, uid, currentDay), aid);
            this.sendPrivateMsg(uid, aid);
            this.doDrawTimesEvent(uid, aid, 1, 2);
        }
    }

    public void sendPrivateMsg(String uid, String aid) {
        try {
            ActorData actorData = actorDao.getActorDataFromCache(aid);
            SendChatMsgDTO msgDto = new SendChatMsgDTO();
            msgDto.setUid(uid);
            msgDto.setAid(aid);
            msgDto.setMsgType(MsgType.SHARE_ACTIVITY);
            msgDto.setOs(actorData.getIntOs());
            msgDto.setMsgBody("");
            msgDto.setSlang(actorData.getSlang());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("shareId", SHARE_ID);
            msgDto.setMsgInfo(jsonObject);
            msgDto.setVersioncode(actorData.getVersion_code());
            msgDto.setNew_versioncode(5);
            HttpResult<Object> ret = iMsgService.sendMsg(msgDto);
            logger.info("inner msg success msgDto={} return--> code={} msg={}", msgDto, ret.getCode(), ret.getMsg());
        } catch (Exception e) {
            logger.error("sendPrivateMsg error:{}", e.getMessage(), e);
        }
    }

    // 赠送/接收记录
    public LuckyNumberVO luckyNumTimesRecord(String activityId, String uid, int page) {
        int pageSize = 10;
        int start = (page - 1) * RECORD_PAGE_SIZE;
        int end = page * RECORD_PAGE_SIZE;
        String devoteTimesKey = getSendDrawTimeKey(activityId, uid);
        List<String> devoteTimesList = activityCommonRedis.getCommonListPageRecord(devoteTimesKey, start, end);
        LuckyNumberVO vo = new LuckyNumberVO();
        List<PrizeConfigVO> devoteTimesRecordList = new ArrayList<>();

        for (String record : devoteTimesList) {
            PrizeConfigVO devoteRecord = JSONObject.parseObject(record, PrizeConfigVO.class);
            ActorData friendActor = actorDao.getActorDataFromCache(devoteRecord.getUid());
            devoteRecord.setUserRid(friendActor.getStrRid());
            devoteTimesRecordList.add(devoteRecord);
        }
        vo.setDrawRecordList(devoteTimesRecordList);
        if (devoteTimesRecordList.size() < pageSize) {
            vo.setNextUrl(-1);
        } else {
            vo.setNextUrl(page + 1);
        }
        return vo;
    }


    @Override
    public void dailyTaskRun(String dateStr) {
        try {
            OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID);
            if (activityData == null) {
                return;
            }
            int endTime = activityData.getEndTime();
            int currentTime = DateHelper.getNowSeconds();
            if (currentTime - 3600 > endTime) {
                return;
            }
            if (dateStr == null) {
                dateStr = DateHelper.ARABIAN.getYesterdayStr(new Date());
            }
            logger.info("dailyTaskRun Lucky Number");
            List<String> rankingList = activityCommonRedis.getCommonRankingList(getDailyFinishLuckyKey(ACTIVITY_ID, dateStr), 0);
            for (String rankUid : rankingList) {
                resourceKeyHandlerService.sendResourceData(rankUid, "luckynumbertitle1", "Lucky Numbers-honor reward", ACTIVITY_TITLE_AR, "Lucky Numbers-honor reward", ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
            }

            List<String> giftRankList = activityCommonRedis.getCommonRankingList(getDailyGiftRankKey(ACTIVITY_ID, dateStr), 3);
            for (String rankUid : giftRankList) {
                resourceKeyHandlerService.sendResourceData(rankUid, "luckynumbertitle2", "Lucky Numbers-honor reward", ACTIVITY_TITLE_AR, "Lucky Numbers-honor reward", ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
            }

        } catch (Exception e) {
            logger.error("distribution Lucky Number error: {}", e.getMessage(), e);
        }
    }
}
