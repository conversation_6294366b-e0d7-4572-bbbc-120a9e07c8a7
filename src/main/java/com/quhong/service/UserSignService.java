package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.DailySignLogEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.config.SignInRewardConfig;
import com.quhong.config.SignInTaskConfig;
import com.quhong.constant.BackStageConfigConstant;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.constant.UserHttpCode;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.date.DayTimeSupport;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.UserLevelTaskService;
import com.quhong.data.*;
import com.quhong.data.dto.SignCheckV9DTO;
import com.quhong.data.dto.UserSignDTO;
import com.quhong.data.vo.*;
import com.quhong.enums.*;
import com.quhong.exception.CommonException;
import com.quhong.feign.ISundryService;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.msg.chat.OfficialPushMsg;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.AdCampaignGameData;
import com.quhong.redis.DataRedisBean;
import com.quhong.redis.UserSignRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.AppVersionUtils;
import com.quhong.utils.RoomUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/20
 */
@Service
public class UserSignService {

    private static final Logger logger = LoggerFactory.getLogger(UserSignService.class);

    private static final List<Integer> NEW_SIGN_GET_BEANS = Arrays.asList(3, 5, 8, 12, 18, 24, 30);
    private static final int DEBUG_SIGN_BUBBLE_ID = 63;
    private static final int PRO_SIGN_BUBBLE_ID = 65;
    private static final int DEBUG_SIGN_MIC_FRAME_ID = 356;
    private static final int PRO_SIGN_MIC_FRAME_ID = 298;
    private static final int SIGN_A_TYPE = 105;
    private static final String SIGN_REWARD_TITLE = "Sign";
    private static final String SIGN_ROOKIE_REWARD_TITLE_V9 = "New User Pack";
    private static final String SIGN_REWARD_DESC = "Sign reward";
    private static final String PROMOTION_GAME_ADS = "GAME_ADS";
    private static final Map<Integer, String> ROOKIE_SIGN_RES_MAP = new HashMap<>();
    private static final Map<Integer, String> ROOKIE_COUNTRY_SIGN_RES_MAP = new HashMap<>();
    // 海湾六国国家码 (GCC)
    private static final List<String> GCC_COUNTRIES = Arrays.asList("SA", "KW", "AE", "QA", "OM", "BH");
    private static final List<Integer> ROOKIE_SIGN_TYPE = Arrays.asList(1, 2);

    // 新用户签到天数常量
    private static final int ROOKIE_SIGN_DAY_1 = 1;
    private static final int ROOKIE_SIGN_DAY_2 = 2;
    private static final int ROOKIE_SIGN_DAY_3 = 3;

    // 新用户签到消息标题 - 英语
    private static final String ROOKIE_SIGN_TITLE_DAY1_EN = "Congratulations on getting the new user exclusive gift pack";
    private static final String ROOKIE_SIGN_TITLE_DAY2_EN = "Great! Nice to see you again!";
    private static final String ROOKIE_SIGN_TITLE_DAY3_EN = "Thank you for your 3rd day of companionship";

    // 新用户签到消息标题 - 阿语
    private static final String ROOKIE_SIGN_TITLE_DAY1_AR = "تهانينا للحصول على حزمة الهدايا الحصرية للمستخدم الجديد";
    private static final String ROOKIE_SIGN_TITLE_DAY2_AR = "رائع! من الجميل رؤيتك مرة أخرى!";
    private static final String ROOKIE_SIGN_TITLE_DAY3_AR = "شكرا لك على مرافقتك في اليوم الثالث";

    // 新用户签到消息内容 - 英语
    private static final String ROOKIE_SIGN_BODY_DAY1_EN = "Log in for 3 consecutive days to get VIP";
    private static final String ROOKIE_SIGN_BODY_DAY1_REWARD_EN = "Log in for 3 consecutive days to get rich rewards";
    private static final String ROOKIE_SIGN_BODY_DAY2_EN = "Consecutive login day 2, we give you the following rewards";
    private static final String ROOKIE_SIGN_BODY_DAY3_VIP_EN = "Give you %d days VIP privileges";
    private static final String ROOKIE_SIGN_BODY_DAY3_REWARD_EN = "Give you the following rewards";

    // 新用户签到消息内容 - 阿语
    private static final String ROOKIE_SIGN_BODY_DAY1_AR = "سجل الدخول لمدة 3 أيام متتالية للحصول على VIP";
    private static final String ROOKIE_SIGN_BODY_DAY1_REWARD_AR = "سجل الدخول لمدة 3 أيام متتالية للحصول على مكافآت غنية";
    private static final String ROOKIE_SIGN_BODY_DAY2_AR = "تسجيل الدخول المتتالي اليوم الثاني، نقدم لك المكافآت التالية";
    private static final String ROOKIE_SIGN_BODY_DAY3_VIP_AR = "نقدم لك امتيازات VIP لمدة %d أيام";
    private static final String ROOKIE_SIGN_BODY_DAY3_REWARD_AR = "نقدم لك المكافآت التالية";

    // 签到资源Key映射表
    private static final Map<String, String> SIGN_RESOURCE_KEY_MAP = new HashMap<>();

    static {
        ROOKIE_SIGN_RES_MAP.put(1, "appSignRookieNormalMaleUserDay1");
        ROOKIE_SIGN_RES_MAP.put(2, "appSignRookieNormalFemaleUserDay1");
        ROOKIE_SIGN_RES_MAP.put(3, "appSignRookiePotentialMaleUserDay1");
        ROOKIE_SIGN_RES_MAP.put(4, "appSignRookiePotentialFemaleUserDay1");

        ROOKIE_COUNTRY_SIGN_RES_MAP.put(1, "AE_newbie_m");
        ROOKIE_COUNTRY_SIGN_RES_MAP.put(2, "AE_newbie_f");
        ROOKIE_COUNTRY_SIGN_RES_MAP.put(3, "SA_newbie_m");
        ROOKIE_COUNTRY_SIGN_RES_MAP.put(4, "SA_newbie_f");
        ROOKIE_COUNTRY_SIGN_RES_MAP.put(5, "Other_newbie_m");
        ROOKIE_COUNTRY_SIGN_RES_MAP.put(6, "Other_newbie_f");

        // 新设备账号新人礼包 - 海湾国家
        SIGN_RESOURCE_KEY_MAP.put("NEW_DEVICE_MAJOR_DAY1", "appSignRookieNewDeviceMajorDay1");
        SIGN_RESOURCE_KEY_MAP.put("NEW_DEVICE_MAJOR_DAY2", "appSignRookieNewDeviceMajorDay2");
        SIGN_RESOURCE_KEY_MAP.put("NEW_DEVICE_MAJOR_DAY3", "appSignRookieNewDeviceMajorDay3");

        // 新设备账号新人礼包 - 非海湾国家
        SIGN_RESOURCE_KEY_MAP.put("NEW_DEVICE_MINOR_DAY1", "appSignRookieNewDeviceMinorDay1");
        SIGN_RESOURCE_KEY_MAP.put("NEW_DEVICE_MINOR_DAY2", "appSignRookieNewDeviceMinorDay2");
        SIGN_RESOURCE_KEY_MAP.put("NEW_DEVICE_MINOR_DAY3", "appSignRookieNewDeviceMinorDay3");

        // 旧设备账号新人礼包 - 海湾国家
        SIGN_RESOURCE_KEY_MAP.put("OLD_DEVICE_MAJOR_DAY1", "appSignRookieOldDeviceMajorDay1");
        SIGN_RESOURCE_KEY_MAP.put("OLD_DEVICE_MAJOR_DAY2", "appSignRookieOldDeviceMajorDay2");
        SIGN_RESOURCE_KEY_MAP.put("OLD_DEVICE_MAJOR_DAY3", "appSignRookieOldDeviceMajorDay3");

        // 旧设备账号新人礼包 - 非海湾国家
        SIGN_RESOURCE_KEY_MAP.put("OLD_DEVICE_MINOR_DAY1", "appSignRookieOldDeviceMinorDay1");
        SIGN_RESOURCE_KEY_MAP.put("OLD_DEVICE_MINOR_DAY2", "appSignRookieOldDeviceMinorDay2");
        SIGN_RESOURCE_KEY_MAP.put("OLD_DEVICE_MINOR_DAY3", "appSignRookieOldDeviceMinorDay3");

        // 正常签到资源Key
        SIGN_RESOURCE_KEY_MAP.put("NORMAL_DAY1", "appSignNormalDay1");
        SIGN_RESOURCE_KEY_MAP.put("NORMAL_DAY2", "appSignNormalDay2");
        SIGN_RESOURCE_KEY_MAP.put("NORMAL_DAY3", "appSignNormalDay3");
        SIGN_RESOURCE_KEY_MAP.put("NORMAL_DAY4", "appSignNormalDay4");
        SIGN_RESOURCE_KEY_MAP.put("NORMAL_DAY5", "appSignNormalDay5");
        SIGN_RESOURCE_KEY_MAP.put("NORMAL_DAY6", "appSignNormalDay6");
        SIGN_RESOURCE_KEY_MAP.put("NORMAL_DAY7", "appSignNormalDay7");

    }

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;
    @Resource
    private ActorDao actorDao;
    @Resource
    private SignTableDao signTableDao;
    @Resource
    private SignInRewardConfig signInRewardConfig;
    @Resource
    private UserRegisterDao userRegisterDao;
    @Resource
    private BackstageConfigDao backstageConfigDao;
    @Resource
    private SignInTaskConfig signInTaskConfig;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private TnDeviceAccountDao tnDeviceAccountDao;
    @Resource
    private EventReport eventReport;
    @Resource
    private UserLevelTaskService userLevelTaskService;
    @Resource
    private BadgeDao badgeDao;
    @Resource
    private RookieRecommendService rookieRecommendService;
    @Resource
    private DAUDao dAUDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private UserSignRedis userSignRedis;
    @Qualifier("com.quhong.feign.ISundryService")
    @Autowired
    private ISundryService iSundryService;
    @Resource
    private AdCampaignGameDao adCampaignGameDao;
    @Resource
    private AllGameMatchService allGameMatchService;
    @Autowired
    private WhiteTestDao whiteTestDao;
    @Resource
    private OfficialDao officialDao;
    @Resource
    private NoticeNewDao noticeNewDao;
    @Resource
    protected RoomWebSender roomWebSender;

    public UserSignService() {
    }

    @PostConstruct
    public void postInit() {
    }

    /**
     * 新手体验报告
     */
    public RookieSignInfoVO getRookieSignInfo(UserSignDTO req) {
        RookieSignInfoVO vo = new RookieSignInfoVO();
        int endTime = getNewUserEndTime(req.getUid());
        int realTime = endTime > 0 ? endTime - DateHelper.getNowSeconds() : 0;
        vo.setEndTime(Math.max(realTime, 0));
        vo.setRookie(ActorUtils.isNewRegisterActor(req.getUid(), 7) ? 1 : 0);
        setFixedValue(vo);
        return vo;
    }

    /**
     * 新签到
     */
    public SignVO sign(UserSignDTO req) {
        ActorData actorData = actorDao.getActorDataFromCache(req.getUid());
        if (actorData == null) {
            logger.error("can not find user data. uid={}", req.getUid());
            throw new CommonException(UserHttpCode.USER_NOT_EXIST);
        }
        String today;
        if (ServerConfig.isNotProduct()) {
            today = DateHelper.ARABIAN.formatDateInDay(getAfterDay(getFakeDay(req.getUid())));
        } else {
            today = DateHelper.ARABIAN.formatDateInDay();
        }
        try (DistributeLock lock = new DistributeLock(getLockKey(req.getUid()))) {
            lock.lock();
            SignTableData data = signTableDao.findData(req.getUid());
            if (data == null) {
                logger.error("can not find sign table data. uid={}", req.getUid());
                throw new CommonException(new HttpCode(1, ""));
            }
            if (today.equals(data.getLast_sign())) {
                logger.info("You have signed in. uid={}", req.getUid());
                throw new CommonException(UserHttpCode.YOU_HAVE_SIGNED_IN);
            }
            return sign(data, actorData, req.getUid(), today, req.getRookieBag(), req);
        }
    }

    private String getLockKey(String uid) {
        return "userSign_" + uid;
    }

    private SignVO sign(SignTableData data, ActorData actorData, String uid, String today, boolean isRookieBag, UserSignDTO req) {
        SignVO vo = new SignVO();
        String yesterday;
        if (ServerConfig.isNotProduct()) {
            yesterday = DateHelper.ARABIAN.formatDateInDay(getAfterDay(getFakeDay(uid) - 1));
        } else {
            yesterday = DateHelper.ARABIAN.formatDateInDay(getAfterDay(-1));
        }
        int lastSign = !StringUtils.isEmpty(data.getLast_sign()) ? DateHelper.ARABIAN.stringDateToStampSecond(data.getLast_sign()) : 0;
        int intYesterday = DateHelper.ARABIAN.stringDateToStampSecond(yesterday);
        // 根据这个字段去判断该天获得的签到奖励, 连续的天数(连续签到了6天，该字段为6，今天签到后就置为7)
        int combo = lastSign >= intYesterday && data.getCombo() < 7 ? data.getCombo() : 0;
        updateSignTable(data, combo, today, lastSign, intYesterday);
        // 签到勋章下发
        distributeV843SignBadge(uid, data);

        vo.setCombo(combo + 1);
        int curTime = DateHelper.getNowSeconds();
        int endTime = getNewUserEndTime(uid);
        boolean isRookie = ActorUtils.isNewDeviceAccount(uid, actorData.getFirstTnId());
        int realTime = endTime > 0 ? endTime - curTime : 0;
        vo.setRookie(isRookie ? 1 : 0);
        vo.setEndTime(Math.max(realTime, 0));
        vo.setList(Collections.emptyList());
        // 获取用户注册日期
        int registerTime = new ObjectId(actorData.getUid()).getTimestamp();
        String strRegisterTime = DateSupport.format(Instant.ofEpochSecond(registerTime).atZone(ZoneOffset.ofHours(3)).toLocalDate());
        // 用户注册当天截至时间
        String strDayEnd = strRegisterTime + " 23:59:59";
        LocalDateTime dayEnd = DayTimeSupport.parse(strDayEnd);
        int dayEndTime = (int) (dayEnd.toInstant(ZoneOffset.ofTotalSeconds(9000)).toEpochMilli() / 1000);
        // 用户注册第二天
        String strTmrRegisterDay = DateSupport.format(Instant.ofEpochSecond(registerTime + 24 * 60 * 60).atZone(ZoneOffset.ofHours(3)).toLocalDate());
        int dTime = dayEndTime - curTime;
        vo.setRookieEndTime(dTime > 0 && today.equals(strRegisterTime) ? dTime : 0);
        vo.setRookieBagList(Collections.emptyList());
        vo.setRookieBagNext(Collections.emptyList());
        boolean is843V = AppVersionUtils.versionCheck(843, req);
        logger.info("dayEndTime={}, today={}, strRegisterTime={}, strTmrRegisterDay={}, isRookie={}, combo={}, isRookieBag={}", dayEndTime, today, strRegisterTime, strTmrRegisterDay, isRookie, vo.getCombo(), isRookieBag);

        if (isRookie && vo.getCombo() == 1) {
            if (isRookieBag && today.equals(strRegisterTime)) {
                if (is843V) {
                    // 下发3.0版本的新人礼包
                    distribute843SignReward(actorData, vo);
                    String toRoomId;
                    String countryCode = ActorUtils.getCountryCode(actorData.getCountry());
                    boolean isNew = ActorUtils.isNewDeviceAccount(uid, actorData.getFirstTnId());
                    toRoomId = rookieRecommendService.getAllNewRoomId(isNew ? 5 : 6, countryCode);
                    // if (PROMOTION_GAME_ADS.equals(actorData.getPromotion_id())) {
                    //     toRoomId = rookieRecommendService.getRookieRecommendGameRoom(actorData);
                    //     if (StringUtils.isEmpty(toRoomId)) {
                    //         toRoomId = rookieRecommendService.getRookieRecommendRoom(actorData);
                    //
                    //     }
                    // } else {
                    //     toRoomId = rookieRecommendService.getRookieRecommendRoom(actorData);
                    // }
                    vo.setToRoomId(toRoomId);
                } else {
                    Map<String, List<SignInRewardInfo>> signRewardInfoMap = getSignRewardInfo(actorData);
                    vo.setRookieBagList(signRewardInfoMap.get("rookie_day_1"));
                    vo.setRookieBagNext(signRewardInfoMap.get("rookie_day_1_next"));
                    // 下发2.0版本的新人礼包
                    distributeSignReward(uid, 1, false, true, vo.getRookieBagList());
                }
            } else {
                if (isRookieBag) {
                    // 下发常规签到礼包
                    distributeSignReward(uid, combo + 1, false, false, vo.getRookieBagList());
                    List<SignInRewardInfo> toSignInRewardInfos = getReallyRewardInfoList(uid, combo + 1);
                    vo.setList(toSignInRewardInfos);
                } else {
                    // 下发1.0版本的新人礼包
                    distributeSignReward(uid, combo + 1, true, false, vo.getRookieBagList());
                    List<SignInRewardInfo> newUserSignReward = signInRewardConfig.getNewUserSignReward();
                    vo.setList(newUserSignReward);
                }
            }
            vo.setRookie(1);
            vo.setGuideAction(getGuideAction(uid));
            endTime = 24 * 60 * 60;
            vo.setEndTime(endTime);
            setNewUserEndTime(uid, DateHelper.getNowSeconds() + endTime);
        } else {
            if (isRookieBag && vo.getCombo() == 2 && today.equals(strTmrRegisterDay) && isRookie && !is843V) {
                // 获取新版注册第二天新人礼包
                Map<String, List<SignInRewardInfo>> signRewardInfoMap = getSignRewardInfo(actorData);
                List<SignInRewardInfo> rookieBagList = signRewardInfoMap.get("rookie_day_2");
                int beans = 3;
                int rideDays = 1;
                String tnId = actorData.getTn_id();
                if (!StringUtils.isEmpty(tnId)) {
                    if (!hasGetRookieBag(tnId)) {
                        beans = 100;
                        rideDays = 3;
                        setRookieBag(tnId);
                    }
                }
                rookieBagList.get(2).setPrice(String.valueOf(beans));
                rookieBagList.get(2).setNums(beans);
                rookieBagList.get(2).setDiamond(beans);
                if (getUserType(actorData) != 1) {
                    rookieBagList.get(1).setNums(rideDays);
                    rookieBagList.get(1).setDay(rideDays);
                }
                vo.setRookieBagList(rookieBagList);
                // 下发2.0版本的新人礼包
                distributeSignReward(uid, 2, true, true, vo.getRookieBagList());
            } else {
                // 下发常规签到礼包
                distributeSignReward(uid, combo + 1, false, false, vo.getRookieBagList());
                List<SignInRewardInfo> toSignInRewardInfos = getReallyRewardInfoList(uid, combo + 1);
                vo.setList(toSignInRewardInfos);
            }
        }
        int intToday = Integer.parseInt(today.replace("-", ""));
        // 数数埋点
        doReportEvent(uid, intToday, combo);
        dAUDao.updateNewDAUEvent(actorData, 11);
        userLevelTaskService.sendTaskDataToMq(new UserLevelTaskData(uid, UserLevelConstant.CHECK_IN));
        setFixedValue(vo);
        vo.setTask_list(signInTaskConfig.getTaskList());
        return vo;
    }

    private List<SignInRewardInfo> getReallyRewardInfoList(String uid, int combo) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        boolean isAllGet = isAllGet(uid, actorData.getIp(), actorData.getTn_id());
        ResourceKeyConfigData resKeyConfigData = resourceKeyConfigDao.findByKey(String.format("appSignNormalDay%s", combo));
        if (resKeyConfigData == null) {
            return Collections.emptyList();
        }
        List<SignInRewardInfo> signInRewardInfoList = new ArrayList<>();
        for (ResourceKeyConfigData.ResourceMeta resourceMeta : resKeyConfigData.getResourceMetaList()) {
            if (!isAllGet && resourceMeta.getResourceType() != BaseDataResourcesConstant.TYPE_COIN) {
                continue;
            }
            resourceKeyHandlerService.sendOneResourceDataNoBroadcast(actorData, resourceMeta, SIGN_A_TYPE, SIGN_REWARD_TITLE, SIGN_REWARD_DESC, 2);
            SignInRewardInfo signInRewardInfo = new SignInRewardInfo();
            signInRewardInfo.setName(resourceMeta.getResourceNameEn());
            signInRewardInfo.setName_ar(resourceMeta.getResourceNameAr());
            signInRewardInfo.setIcon(resourceMeta.getResourceIcon());
            signInRewardInfo.setNums(resourceMeta.getResourceNumber());
            signInRewardInfo.setDay(resourceMeta.getResourceTime());
            signInRewardInfo.setType(resourceMeta.getResourceType() < 0 || resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_BAG_GIFT ? 2 : 1);
            signInRewardInfoList.add(signInRewardInfo);
        }
        return signInRewardInfoList;
    }

    private boolean isAllGet(String uid, String ip, String tnId) {
        if (StringUtils.isEmpty(ip) || StringUtils.isEmpty(tnId)) {
            return true;
        }
        String ipType = "ipTnType";
        String deviceType = "deviceAccountType";
        List<String> ipTnList = getLimitList(ipType, ip); // 当前ip下的所有设备
        if (ipTnList.contains(tnId) || ipTnList.size() + 1 <= 10) {
            if (!ipTnList.contains(tnId)) {
                rightPushList(ipType, ip, tnId);
            }
            List<String> deviceAccountList = getLimitList(deviceType, tnId);  // 当前设备下的所有账号
            if (deviceAccountList.contains(uid) || deviceAccountList.size() + 1 <= 10) {
                if (!deviceAccountList.contains(uid)) {
                    rightPushList(deviceType, tnId, uid);
                }
                return true;
            }
            logger.info("deviceAccount limit isAllGet false ip:{} tnId:{}", ip, tnId);
            return false;
        } else {
            logger.info("ipTnList limit isAllGet false ip:{} tnId:{}", ip, tnId);
            return false;
        }
    }

    public void rightPushList(String type, String keyId, String data) {
        try {
            String key = getLimitKey(type, keyId);
            clusterRedis.opsForList().rightPush(key, data);
            clusterRedis.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("rightPushList error={}", e.getMessage(), e);
        }
    }

    public List<String> getLimitList(String type, String keyId) {
        try {
            String key = getLimitKey(type, keyId);
            List<String> jsonList = clusterRedis.opsForList().range(key, 0, 9);
            if (CollectionUtils.isEmpty(jsonList)) {
                return new ArrayList<>();
            }
            return jsonList;
        } catch (Exception e) {
            logger.error("getLimitList error", e);
            return new ArrayList<>();
        }
    }

    private String getLimitKey(String type, String keyId) {
        return String.format("list:sign:limit:%s:%s", type, keyId);
    }

    /**
     * 新人礼包同设备限制
     * 同一个设备下只有1个账号可领取30/10钻石，先领先得，其他账号领取1钻石
     * 同一个IP下只有3个账号获得10/5个Qahwa礼物，超出的账号获得1个Qahwa礼物
     */
    private boolean rookieSignTnAllGet(String tnId) {
        if (StringUtils.isEmpty(tnId)) {
            return true;
        }
        String type = "tnType";
        List<String> rookieSignTnUidList = getRookieSignLimitList(type, tnId);
        return CollectionUtils.isEmpty(rookieSignTnUidList);
    }

    private boolean rookieSignIpAllGet(String ip) {
        if (StringUtils.isEmpty(ip)) {
            return true;
        }
        String type = "ipType";
        List<String> rookieSignUidList = getRookieSignLimitList(type, ip);
        return CollectionUtils.isEmpty(rookieSignUidList) || rookieSignUidList.size() < 3;
    }

    public String rookieSignTnIpKey(String type, String keyId) {
        return String.format("list:rookieSign:Limit:%s:%s", type, keyId);
    }

    public void rightPushRookieSignList(String type, String keyId, String uid) {
        try {
            String key = rookieSignTnIpKey(type, keyId);
            clusterRedis.opsForList().rightPush(key, uid);
            clusterRedis.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("rightPushRookieSignTnList error={}", e.getMessage(), e);
        }
    }

    public List<String> getRookieSignLimitList(String type, String keyId) {
        try {
            String key = rookieSignTnIpKey(type, keyId);
            List<String> jsonList = clusterRedis.opsForList().range(key, 0, 5);
            if (CollectionUtils.isEmpty(jsonList)) {
                return Collections.emptyList();
            }
            return jsonList;
        } catch (Exception e) {
            logger.error("getRookieSignLimitList error", e);
            return Collections.emptyList();
        }
    }


    private int getGuideAction(String uid) {

        // char ch = ActorUtils.getUidEndChar(uid);
        // return ch >= '0' && ch <= '7' ? 1 : 0;
        return 0;
    }

    private void doReportEvent(String uid, int today, int combo) {
        DailySignLogEvent event = new DailySignLogEvent();
        event.setUid(uid);
        event.setDate(today);
        event.setDaily_sign_action(3);
        event.setSign_days(combo);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    /**
     * 下发签到奖励
     */
    private void distributeSignReward(String uid, int combo, boolean rookie, boolean rookie2, List<SignInRewardInfo> rewardList) {
        if (rookie) {
            distributionService.sendCoinRecord(uid, 20, SIGN_REWARD_TITLE, SIGN_REWARD_DESC);
            int bubbleId = ServerConfig.isNotProduct() ? DEBUG_SIGN_BUBBLE_ID : PRO_SIGN_BUBBLE_ID;
            distributionService.sendResourceReward(uid, bubbleId, RewardTypeEnum.BUDDLE, BaseDataResourcesConstant.ACTION_GET_WEAR, 3, 1, SIGN_REWARD_DESC);
            int micId = ServerConfig.isNotProduct() ? DEBUG_SIGN_MIC_FRAME_ID : PRO_SIGN_MIC_FRAME_ID;
            distributionService.sendResourceReward(uid, micId, RewardTypeEnum.MIC, BaseDataResourcesConstant.ACTION_GET_WEAR, 3, 1, SIGN_REWARD_DESC);
            sendGiftToGiftBag(uid, 110, 5, 7);
        } else if (rookie2) {
            if (combo == 1 || combo == 2) {
                flushUidToData(uid, combo, rewardList);
            }
        }
    }

    private void sendReward(String uid, List<SignInRewardInfo> rewardList) {
        for (SignInRewardInfo rewardInfo : rewardList) {
            Integer sourceId = ServerConfig.isNotProduct() ? rewardInfo.getSource_id_debug() : rewardInfo.getSource_id_pro();
            distributionService.sendReward(uid, sourceId, RewardTypeEnum.getEnumByName(rewardInfo.getStype()), SIGN_A_TYPE, 1, rewardInfo.getDay(), rewardInfo.getNums(), SIGN_REWARD_TITLE, SIGN_REWARD_DESC, 0);
        }
    }

    /**
     * 下发签到勋章奖励
     */
    private void distributeV843SignBadge(String uid, SignTableData data) {
        int signCount = data.getSignCount();
        Map<Integer, SignBadgeInfo> SignBadgeInfoMap = signInRewardConfig.getSignBadgeInfoList().stream().collect(Collectors.toMap(SignBadgeInfo::getSignCount, Function.identity()));

        SignBadgeInfo signBadgeInfo = SignBadgeInfoMap.get(signCount);
        if (signBadgeInfo != null) {
            int lastSignCount = signBadgeInfo.getLastSignCount();
            SignBadgeInfo lastSignBadgeInfo = SignBadgeInfoMap.get(lastSignCount);
            if (lastSignBadgeInfo != null) {
                int lastSignBadgeId = lastSignBadgeInfo.getBadgeId();
                BadgeData lastBadgeData = badgeDao.getBadgeData(uid, lastSignBadgeId);
                if (lastBadgeData != null && lastBadgeData.getStatus() > 0) {
                    lastBadgeData.setStatus(0);
                    badgeDao.upsert(lastBadgeData);
                }
            }
            int currentSignBadgeId = signBadgeInfo.getBadgeId();
            distributionService.sendResourceReward(uid, currentSignBadgeId, RewardTypeEnum.BADGE, BaseDataResourcesConstant.ACTION_GET_WEAR, 0, 0, "Sign Badge", 1);

        }
    }

    private void flushUidToData(String uid, int combo, List<SignInRewardInfo> rewardList) {
        if (CollectionUtils.isEmpty(rewardList)) {
            return;
        }
        if (combo == 1) {
            rewardList.remove(rewardList.size() - 1);
            distributionService.sendCoinRecord(uid, 500, SIGN_REWARD_TITLE, SIGN_REWARD_DESC);
        }
        for (SignInRewardInfo signReward : rewardList) {
            RewardTypeEnum rewardTypeEnum = RewardTypeEnum.getEnumByName(signReward.getStype());
            if (rewardTypeEnum == null) {
                continue;
            }
            Integer sourceId = ServerConfig.isNotProduct() ? signReward.getSource_id_debug() : signReward.getSource_id_pro();
            distributionService.sendResourceReward(uid, sourceId, rewardTypeEnum, BaseDataResourcesConstant.ACTION_GET, signReward.getDay(), signReward.getNums(), SIGN_REWARD_DESC);
        }
    }

    private void sendGiftToGiftBag(String uid, int gid, int nums, int day) {
        distributionService.sendResourceReward(uid, gid, RewardTypeEnum.BAG_GIFT, BaseDataResourcesConstant.ACTION_GET, day, nums, SIGN_REWARD_DESC);
    }

    private boolean hasGetRookieBag(String tnId) {
        try {
            return Boolean.TRUE.equals(clusterRedis.opsForSet().isMember(getNewRookieBagKey(), tnId));
        } catch (Exception e) {
            logger.error("has get rookie bag 2. {}", e.getMessage(), e);
            return false;
        }
    }

    private void setRookieBag(String tnId) {
        try {
            clusterRedis.opsForSet().add(getNewRookieBagKey(), tnId);
        } catch (Exception e) {
            logger.error("set new rookie bag 2. {}", e.getMessage(), e);
        }
    }

    private String getNewRookieBagKey() {
        return "set:new:rookie:bag:2";
    }

    private void updateSignTable(SignTableData data, int combo, String today, int lastSign, int intYesterday) {
        data.setCombo(combo + 1);
        if (combo == 0) {
            data.setSign_record(Collections.singletonList(1));
        } else {
            List<Integer> signRecord = data.getSign_record();
            if (CollectionUtils.isEmpty(signRecord)) {
                signRecord = new ArrayList<>();
            }
            signRecord.add(combo + 1);
            data.setSign_record(signRecord);
        }
        if (data.getCover() == 1) {
            data.setContinu(lastSign >= intYesterday ? data.getContinu() + 1 : 1);
        } else if (data.getCover() == 0) {
            data.setContinu(data.getCombo());
            data.setCover(1);
        }
        data.setCheck_date(today);
        data.setLast_sign(today);
        data.setBoom_check(today);
        data.setSignCount(data.getSignCount() + 1);
        signTableDao.updateData(data);
    }

    private int getFakeDay(String uid) {
        try {
            String value = clusterRedis.opsForValue().get("str:test:fakeday:" + uid);
            if (!StringUtils.isEmpty(value)) {
                return Integer.parseInt(value);
            }
        } catch (Exception e) {
            logger.error("get test fake day error. uid={} {}", e.getMessage(), e);
        }
        return 0;
    }

    private int getNewUserEndTime(String uid) {
        try {
            Double score = clusterRedis.opsForZSet().score(getNewUserNameKey(), uid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.error("get new user end time error. uid={} {}", uid, e.getMessage(), e);
            return 0;
        }
    }

    private void setNewUserEndTime(String uid, int endTime) {
        try {
            clusterRedis.opsForZSet().add(getNewUserNameKey(), uid, endTime);
        } catch (Exception e) {
            logger.error("set new user end time error. uid={} endTime={} {}", uid, endTime, e.getMessage(), e);
        }
    }

    private String getNewUserNameKey() {
        return "user:common:new_user_zset_name";
    }

    public CheckSignBeansVO checkSignBeans(UserSignDTO req) {
        MongoActorData actorData = actorDao.findActorDataFromDB(req.getUid());
        if (actorData == null) {
            logger.error("can not find user data. uid={}", req.getUid());
            throw new CommonException(UserHttpCode.USER_NOT_EXIST);
        }
        SignTableData data = signTableDao.findData(req.getUid());
        String today = DateHelper.ARABIAN.formatDateInDay();
        if (data != null) {
            if (today.equals(data.getBoom_check())) {
                throw new CommonException(new HttpCode(1, ""));
            }
            data.setBoom_check(today);
            signTableDao.updateData(data);
        } else {
            data = new SignTableData();
            data.set_id(req.getUid());
            data.setCheck_date(today);
            data.setCombo(1);
            data.setBoom_check(today);
            signTableDao.insertData(data);
        }
        return buildCheckSignBeansVO(data);
    }

    private CheckSignBeansVO buildCheckSignBeansVO(SignTableData data) {
        CheckSignBeansVO vo = new CheckSignBeansVO();
        int combo = 0;
        if (!StringUtils.isEmpty(data.getLast_sign()) && data.getCombo() < 7) {
            Date lastSignTime = DateHelper.ARABIAN.parseDate(data.getLast_sign());
            Date yesterdayTime = getAfterDay(-1);
            if (lastSignTime.after(yesterdayTime)) {
                combo = data.getCombo();
            }
        }
        data.setCombo(combo + 1);
        List<Integer> signRecordList = data.getSign_record();
        if (combo == 0) {
            signRecordList = new ArrayList<>();
        }
        int benefit = NEW_SIGN_GET_BEANS.get(combo);
        signRecordList.add(benefit);
        vo.setSign_benas_list(NEW_SIGN_GET_BEANS);
        vo.setCombo(data.getCombo());
        vo.setSign_beans(benefit);
        vo.setGet_beans_list(signRecordList);
        return vo;
    }

    /**
     * 检查签到接口
     */
    public CheckSignRewardVO checkSignReward(UserSignDTO req) {
        ActorData actorData = actorDao.getActorDataFromCache(req.getUid());
        if (actorData == null) {
            logger.info("User not exist. uid={}", req.getUid());
            throw new CommonException(UserHttpCode.USER_NOT_EXIST);
        }
        CheckSignRewardVO vo = new CheckSignRewardVO();
        vo.setIpCodeCountry(actorData.getCountry());
        int v = 0;
        if (ServerConfig.isNotProduct()) {
            try {
                clusterRedis.delete("str:test:rookiePopWindow:" + req.getUid());
                String value = clusterRedis.opsForValue().get("str:test:fakeday:" + req.getUid());
                if (!StringUtils.isEmpty(value)) {
                    v = Integer.parseInt(value);
                }
            } catch (Exception e) {
                logger.error("get test fake day error. uid={} {}", e.getMessage(), e);
            }
        }
        String today;
        String yesterday;
        if (v != 0) {
            today = DateHelper.ARABIAN.formatDateInDay(getAfterDay(v));
            yesterday = DateHelper.ARABIAN.formatDateInDay(getAfterDay(v - 1));
        } else {
            today = DateHelper.ARABIAN.formatDateInDay();
            yesterday = "";
        }
        SignTableData data = signTableDao.findData(req.getUid());
        int isRookie = ActorUtils.isNewDeviceAccount(req.getUid(), actorData.getFirstTnId()) ? 1 : 0;
        int curTime = DateHelper.getNowSeconds();
        int combo = 0;
        setFixedValue(vo);
        if (data != null) {
            int endTime = getNewUserEndTime(req.getUid());
            if (StringUtils.isEmpty(yesterday)) {
                yesterday = DateHelper.ARABIAN.formatDateInDay(getAfterDay(v - 1));
            }
            if (yesterday.equals(data.getLast_sign()) && data.getCombo() < 7) {
                combo = data.getCombo();
            }
            int realTime = endTime > 0 ? endTime - curTime : 0;
            if ((today.equals(data.getBoom_check()) && req.getAccess() == 0) || today.equals(data.getLast_sign())) {
                vo.setEndTime(Math.max(realTime, 0));
                vo.setRookie(isRookie);
                if (req.getRookieBag()) {
                    fillNewRookieBagData(actorData, vo, curTime, today, data.getLast_sign(), data.getCombo(), req);
                    if (vo.getRookiePopWindow() == 1 || vo.getRookiePopWindow() == 2) {
                        vo.setRookiePopWindow(today.equals(data.getLast_sign()) ? 2 : 4);
                        return vo;
                    }
                }
                logger.info("You have signed in，please come back tomorrow. uid={}", req.getUid());
                throw new CommonException(UserHttpCode.YOU_HAVE_SIGNED_IN, vo);
            } else {
                vo.setCombo(combo);
                vo.setEndTime(Math.max(realTime, 0));
                vo.setRookie(isRookie);
                data.setBoom_check(today);
                signTableDao.updateData(data);
                if (req.getRookieBag()) {
                    fillNewRookieBagData(actorData, vo, curTime, today, data.getLast_sign(), data.getCombo(), req);
                }
                return vo;
            }
        } else {
            saveSignTable(req.getUid(), today, combo);
            vo.setCombo(combo);
            vo.setRookie(isRookie);
            vo.setEndTime(0);
            if (req.getRookieBag()) {
                fillNewRookieBagData(actorData, vo, curTime, today, "", combo, req);
            }
        }
        return vo;
    }


    private SignTableData saveSignTable(String uid, String today, int combo) {
        SignTableData data = new SignTableData();
        data.set_id(uid);
        data.setCheck_date(today);
        data.setBoom_check(today);
        data.setCombo(combo);
        data.setLast_sign("");
        signTableDao.insertData(data);
        return data;
    }

    private void fillNewRookieBagData(ActorData actorData, CheckSignRewardVO vo, int curTime, String today, String lastSign, int combo, UserSignDTO req) {
        // 获取用户注册日期
        int registerTime = new ObjectId(actorData.getUid()).getTimestamp();
        String strRegisterTime = DateSupport.format(Instant.ofEpochSecond(registerTime).atZone(ZoneOffset.ofHours(3)).toLocalDate());
        // 用户注册当天截至时间
        String strDayEnd = strRegisterTime + " 23:59:59";
        LocalDateTime dayEnd = DayTimeSupport.parse(strDayEnd);
        int dayEndTime = (int) (dayEnd.toInstant(ZoneOffset.ofTotalSeconds(9000)).toEpochMilli() / 1000);
        // 用户注册第二天
        String strTmrRegisterDay = DateSupport.format(Instant.ofEpochSecond(registerTime + 24 * 60 * 60).atZone(ZoneOffset.ofHours(3)).toLocalDate());
        logger.info("dayEndTime={}, strRegisterTime={}, strTmrRegisterDay={}", dayEndTime, strRegisterTime, strTmrRegisterDay);
        int dTime = dayEndTime - curTime;
        List<SignInRewardInfo> rookieBagList;
        List<SignInRewardInfo> rookieBagNextList;
//        List<SignInRewardInfo> newRookieBagList = Collections.emptyList();
        boolean isRookieDay = today.equals(strRegisterTime) && ActorUtils.isNewDeviceAccount(actorData.getUid(),
                actorData.getFirstTnId());
        boolean is843V = AppVersionUtils.versionCheck(843, req);
        if (isRookieDay) {
            if (is843V) {
                // 首页第一次请求，或者签到配置页弹出
                rookieBagList = getRookie843BagReward(actorData, vo);
                rookieBagNextList = Collections.emptyList();
            } else {
                Map<String, List<SignInRewardInfo>> signRewardInfoMap = getSignRewardInfo(actorData);
                rookieBagList = signRewardInfoMap.get("rookie_day_1");
                rookieBagNextList = signRewardInfoMap.get("rookie_day_1_next");
            }

        } else {
            rookieBagList = Collections.emptyList();
            rookieBagNextList = Collections.emptyList();
        }
//        vo.setNewRookieBagList(newRookieBagList);
        vo.setRookieBagList(rookieBagList);
        vo.setRookieBagNext(rookieBagNextList);
        vo.setRookieEndTime(dTime > 0 && isRookieDay ? dTime : 0);
        // 0 按老签到流程弹窗 1 第一天未领取 2 第一天已领取 3 第二天未领取  4 第一天首页不弹出
        int rookiePopWindow = 0;
        if (isRookieDay) {
            rookiePopWindow = lastSign.equals(today) ? 2 : 1;
        } else if (today.equals(strTmrRegisterDay) && combo == 1 && !is843V) {
            rookiePopWindow = 3;
        }
        vo.setRookiePopWindow(rookiePopWindow);
    }

    private List<SignInRewardInfo> dealNewWithSignRewardInfoList(ActorData actorData, ResourceKeyConfigData rookieSignResKey) {
        if (ObjectUtils.isEmpty(rookieSignResKey)) {
            return new ArrayList<>();
        }
        String tnId = actorData.getTn_id();
        String ip = actorData.getIp();
        boolean rookieSignTnFlag = rookieSignTnAllGet(tnId);
        boolean rookieSignIpFlag = rookieSignIpAllGet(ip);

        List<SignInRewardInfo> toList = new ArrayList<>();
        for (ResourceKeyConfigData.ResourceMeta resourceMeta : rookieSignResKey.getResourceMetaList()) {
            SignInRewardInfo toRewardInfo = new SignInRewardInfo();
            toRewardInfo.setName(resourceMeta.getResourceNameEn());
            toRewardInfo.setName_ar(resourceMeta.getResourceNameAr());
            toRewardInfo.setIcon(resourceMeta.getResourceIcon());
            toRewardInfo.setNums(resourceMeta.getResourceType() < 0 || resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_BAG_GIFT ? resourceMeta.getResourceNumber() : resourceMeta.getResourceTime());
            toRewardInfo.setType(resourceMeta.getResourceType() < 0 || resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_BAG_GIFT ? 2 : 1);
            toRewardInfo.setPrice(String.valueOf(resourceMeta.getResourcePrice()));
            toRewardInfo.setDay(resourceMeta.getResourceTime());
            toRewardInfo.setSource_id(resourceMeta.getResourceId());
            toRewardInfo.setResourceType(resourceMeta.getResourceType());

            // 同设备、同ip判断
            if (!rookieSignTnFlag && resourceMeta.getResourceType() < 0) {
                toRewardInfo.setNums(1);
            }

            if (!rookieSignIpFlag && resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_BAG_GIFT) {
                toRewardInfo.setNums(1);
            }
            toList.add(toRewardInfo);
        }
        return toList;
    }


    /**
     * 获取用户类型 （1:男  2:女  3: 高端男 4: 高端女）
     */
    private int getUserType(ActorData actorData) {
        // UserRegisterInfoData register = userRegisterDao.getRegister(actorData.getUid());
        // if (register != null) {
        //     String strModels = backstageConfigDao.getConfigData(BackStageConfigConstant.HIGN_END_MODELS);
        //     JSONObject jsonObject = JSONObject.parseObject(strModels);
        //     if (jsonObject.getJSONArray("HighEndModels").contains(register.getModel())) {
        //         return actorData.getFb_gender() == 2 ? 4 : 3;
        //     }
        // }

        String userCode = ActorUtils.getUpperCaseCountryCode(actorData.getCountry());
        if ("AE".equals(userCode)) {
            return actorData.getFb_gender() == 1 ? 1 : 2;
        } else if ("SA".equals(userCode)) {
            return actorData.getFb_gender() == 1 ? 3 : 4;
        } else {
            return actorData.getFb_gender() == 1 ? 5 : 6;
        }
    }

    /**
     * 获取签到奖励信息
     */
    private Map<String, List<SignInRewardInfo>> getSignRewardInfo(ActorData actorData) {
        if (actorData.getFb_gender() == 2) {
            // 女用户签到奖励
            return signInRewardConfig.getRookieFamaleDict();
        }
        UserRegisterInfoData register = userRegisterDao.getRegister(actorData.getUid());
        if (register != null) {
            String strModels = backstageConfigDao.getConfigData(BackStageConfigConstant.HIGN_END_MODELS);
            JSONObject jsonObject = JSONObject.parseObject(strModels);
            if (jsonObject.getJSONArray("HighEndModels").contains(register.getModel())) {
                // 高潜男用户签到奖励
                return signInRewardConfig.getRookieHighDiveMale();
            }
        }
        // 普通男用户签到奖励
        return signInRewardConfig.getRookieBagDict();
    }

    /**
     * 获取8.43签到奖励信息
     */
    private ResourceKeyConfigData get843SignRewardInfo(int userType) {
        String resKey = ROOKIE_COUNTRY_SIGN_RES_MAP.get(userType);
        return resourceKeyConfigDao.findByKey(resKey);
    }

    /**
     * 设置固定值
     */
    private void setFixedValue(SignInfoVO vo) {
        vo.setCrushUser(240180);
        vo.setRoomOnlineUser(9820);
        vo.setVideoRoomUser(960);
        vo.setVideoRoomId("");
        vo.setVideoRoomIcon("https://i.ytimg.com/vi/fCZt9QpDXuU/hqdefault.jpg");
        vo.setVideoRoomTitle("The boy Who harnessed the wind 2019 Netfilx Series with English Subtitles");
        vo.setChatRoomId("");
        vo.setInRoomType(0);
    }

    private Date getAfterDay(int diff) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DATE, diff);
        return calendar.getTime();
    }

    /**
     * 8.43新用户礼包列表接口
     */
    public RookieBagReward843VO rookieBagReward843(UserSignDTO req) {
        ActorData actorData = actorDao.getActorDataFromCache(req.getUid());
        if (actorData == null) {
            logger.info("User not exist. uid={}", req.getUid());
            throw new CommonException(UserHttpCode.USER_NOT_EXIST);
        }
        RookieBagReward843VO vo = new RookieBagReward843VO();
        SignTableData data = signTableDao.findData(req.getUid());
        int isRookie = ActorUtils.isNewRegisterActor(req.getUid(), 1) ? 1 : 0;
        int curTime = DateHelper.getNowSeconds();
        if (data == null) {
            String today = DateHelper.ARABIAN.formatDateInDay();
            saveSignTable(req.getUid(), today, 0);
        }
        if (isRookie == 1 || ServerConfig.isNotProduct()) {
            List<SignInRewardInfo> rookieBagList = getRookie843BagReward(actorData, null);
            vo.setNewRookieBagList(rookieBagList);
        } else {
            logger.info("uid={} isRookie={} ", req.getUid(), isRookie);
            vo.setNewRookieBagList(Collections.emptyList());
        }
        return vo;
    }

    /**
     * 8.43新用户签到接口
     */
    public RookieBagReward843VO rookieSign(UserSignDTO req) {
        ActorData actorData = actorDao.getActorDataFromCache(req.getUid());
        if (actorData == null) {
            logger.info("User not exist. uid={}", req.getUid());
            throw new CommonException(UserHttpCode.USER_NOT_EXIST);
        }
        RookieBagReward843VO vo = new RookieBagReward843VO();
//        SignTableData data = signTableDao.findData(req.getUid());
//        int isRookie = ActorUtils.isNewRegisterActor(req.getUid(), 1) ? 1 : 0;
//        int curTime = DateHelper.getNowSeconds();
        vo.setToRoomId(rookieRecommendService.getRookieRecommendGameRoom(actorData));
        return vo;
    }

    private List<SignInRewardInfo> getRookie843BagReward(ActorData actorData, CheckSignRewardVO vo) {
        int userType = getUserType(actorData);
        if (vo != null) {
            vo.setUserType(userType);
        }
        ResourceKeyConfigData rookieSignResKey = get843SignRewardInfo(userType);
        return dealNewWithSignRewardInfoList(actorData, rookieSignResKey);
    }

    /**
     * 下发843新人礼包奖励
     */
    private void distribute843SignReward(ActorData actorData, SignVO vo) {
        int userType = getUserType(actorData);
        if (vo != null) {
            vo.setUserType(userType);
        }
        ResourceKeyConfigData rookieSignResKey = get843SignRewardInfo(userType);
        if (ObjectUtils.isEmpty(rookieSignResKey)) {
            return;
        }
        for (ResourceKeyConfigData.ResourceMeta resourceMeta : rookieSignResKey.getResourceMetaList()) {
            resourceKeyHandlerService.sendOneResourceDataNoBroadcast(actorData, resourceMeta, SIGN_A_TYPE, SIGN_REWARD_TITLE, SIGN_REWARD_DESC, 2);
        }
        // String uid = actorData.getUid();
        // String tnId = actorData.getTn_id();
        // String ip = actorData.getIp();
        // boolean rookieSignTnAllGetFlag = rookieSignTnAllGet(tnId);
        // boolean rookieSignIpAllGetFlag = rookieSignIpAllGet(ip);
        // for (ResourceKeyConfigData.ResourceMeta resourceMeta : rookieSignResKey.getResourceMetaList()) {
        //     // 同设备、同ip判断
        //     if (!rookieSignTnAllGetFlag && resourceMeta.getResourceType() < 0){
        //         resourceMeta.setResourceNumber(1);
        //     }
        //
        //     if (!rookieSignIpAllGetFlag && resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_BAG_GIFT){
        //         resourceMeta.setResourceNumber(1);
        //     }
        //     resourceKeyHandlerService.sendOneResourceDataNoBroadcast(actorData, resourceMeta, SIGN_A_TYPE, SIGN_REWARD_TITLE, SIGN_REWARD_DESC, 2);
        // }
        //
        // if (rookieSignTnAllGetFlag){
        //     rightPushRookieSignList("tnType", tnId, uid);
        // }
        //
        // if (rookieSignIpAllGetFlag){
        //     rightPushRookieSignList("ipType", ip, uid);
        // }
    }

    /**
     * 下发新人礼包奖励
     */
    private void distributeSignRewardByList(String uid, List<SignInRewardInfo> rookieBagList) {
        for (SignInRewardInfo item : rookieBagList) {
            String sType = item.getStype();
            int num = item.getNums();
            Integer resId = item.getSource_id();
            if (sType == null) {
                logger.info("not distribute sType is null name={} icon={}", item.getName(), item.getIcon());
            } else if (sType.equals("mic")) {
                distributionService.sendResourceReward(uid, resId, RewardTypeEnum.MIC, BaseDataResourcesConstant.ACTION_GET_WEAR, num, 1, SIGN_REWARD_DESC);
            } else if (sType.equals("buddle")) {
                distributionService.sendResourceReward(uid, resId, RewardTypeEnum.BUDDLE, BaseDataResourcesConstant.ACTION_GET_WEAR, num, 1, SIGN_REWARD_DESC);
            } else if (sType.equals("gift")) {
                sendGiftToGiftBag(uid, resId, num, item.getDay());
            } else if (sType.equals("diamond")) {
                distributionService.sendDiamondsReward(uid, SIGN_A_TYPE, num, SIGN_REWARD_TITLE, SIGN_REWARD_DESC);
            }
        }
    }


    /**
     * 检查签到接口v9
     * 根据用户类型和签到天数返回对应的签到奖励配置
     *
     * @param dto 签到请求参数
     * @return 签到奖励信息
     */
    public SignCheckV9VO signCheckV9(SignCheckV9DTO dto) {
        String uid = dto.getUid();
        ActorData actorData = actorDao.getActorData(uid);
        if (actorData == null) {
            logger.error("User not found: {}", uid);
            throw new CommonException(UserHttpCode.USER_NOT_EXIST);
        }

        try (DistributeLock lock = new DistributeLock(getLockKey(dto.getUid()))) {
            lock.lock();
            // 检测是否已经弹窗或已经签到
            SignTableData signData = checkSignPopup(actorData);

            // 获取签到奖励资源key
            String signResourceKey = getSignResourceKey(signData.getCombo(), signData.getSignType(), actorData);
            List<ResourceMetaData> resourceDataList = convertToResourceMetaData(signResourceKey);
            SignCheckV9VO vo = new SignCheckV9VO();
            vo.setSignResourceKey(signResourceKey);
            vo.setIpCodeCountry(actorData.getIpCodeCountry());
            vo.setCombo(signData.getCombo());
            vo.setSignType(signData.getSignType());
            vo.setSignRewardList(resourceDataList);

            // 设置新人前三天弹窗描述
            setRookieSignTypeDescription(signData, vo, dto.getSlang());

            return vo;
        }
    }

    /**
     * 设置新人前三天弹窗描述
     */
    private void setRookieSignTypeDescription(SignTableData signData, SignCheckV9VO vo, int slang) {
        int signType = signData.getSignType();
        int combo = signData.getCombo();

        // 只有新人签到类型才需要设置描述
        if (!ROOKIE_SIGN_TYPE.contains(signType)) {
            return;
        }
        String signDescription = "";
        if ((signType == 1 || signType == 2) && combo == 0) {
            // signType=1 且 combo=0 或 signType=2 且 combo=0 时判断第3天资源key是否有vipCard奖励
            String resourceKey = getDay3ResourceKey(signType, vo.getIpCodeCountry());
            boolean hasVipCard = hasVipCardReward(resourceKey);
            if (hasVipCard) {
                signDescription = slang == SLangType.ENGLISH ? "Log in for 3 consecutive days-Get VIP" : "سجّل الدخول لمدة 3 أيام متتالية - احصل على VIP";
            } else {
                signDescription = slang == SLangType.ENGLISH ? "Log in for 3 consecutive days-Get Rewards" : "سجّل الدخول لمدة 3 أيام متتالية - احصل على المكافآت";
            }
        } else if ((signType == 1 || signType == 2) && combo == 1) {
            // signType=1、2 且 combo=1 时设置固定描述
            signDescription = slang == SLangType.ENGLISH ? "2nd day of consecutive logins , Here are your rewards" : "اليوم الثاني من تسجيل الدخول المتتالي  إليك مكافآتك";
        } else if ((signType == 1 || signType == 2) && combo == 2) {
            // signType=1、2 且 combo=2 时判断signRewardList是否有vipCard奖励
            ResourceMetaData vipCardResourceMetaData = hasVipCardInRewardList(vo.getSignRewardList());
            if (vipCardResourceMetaData != null) {
                // 获取VIP天数
                int vipDays = vipCardResourceMetaData.getResourceTime();
                signDescription = slang == SLangType.ENGLISH ? String.format("Send you %d days of VIP privilege", vipDays) : String.format("أرسل لك امتياز VIP لمدة %d أيام", vipDays);
            } else {
                signDescription = slang == SLangType.ENGLISH ? "Here are your rewards" : "إليك مكافآتك";
            }
        }
        vo.setSignDescription(signDescription);
    }

    /**
     * 获取第3天的资源key
     */
    private String getDay3ResourceKey(int signType, String ipCodeCountry) {
        // 判断是否为海湾国家
        boolean isGccCountry = isGccCountry(ipCodeCountry);

        String deviceType = signType == 1 ? "NEW_DEVICE" : "OLD_DEVICE";
        String countryType = isGccCountry ? "MAJOR" : "MINOR";
        String keyPrefix = String.format("%s_%s_DAY3", deviceType, countryType);

        return SIGN_RESOURCE_KEY_MAP.get(keyPrefix);
    }

    /**
     * 检查资源key是否包含VIP卡奖励
     */
    private boolean hasVipCardReward(String resourceKey) {
        if (StringUtils.isEmpty(resourceKey)) {
            return false;
        }

        ResourceKeyConfigData config = resourceKeyConfigDao.findByKey(resourceKey);
        if (config == null || CollectionUtils.isEmpty(config.getResourceMetaList())) {
            return false;
        }

        return config.getResourceMetaList().stream()
                .anyMatch(meta -> meta.getResourceType() == BaseDataResourcesConstant.TYPE_VIP_CARD);
    }

    /**
     * 检查奖励列表是否包含VIP卡
     */
    private ResourceMetaData hasVipCardInRewardList(List<ResourceMetaData> rewardList) {
        if (CollectionUtils.isEmpty(rewardList)) {
            return null;
        }
        return rewardList.stream().filter(reward -> reward.getResourceType() == BaseDataResourcesConstant.TYPE_VIP_CARD).findFirst().orElse(null);
    }

    /**
     * 检测是否已经弹窗或已经签到
     * n种情况校验以下逻辑
     * 1、新用户第一天调用: 创建签到记录，返回正常； 再次调用返回已签到
     * 2、新用户第二天调用: 昨天已经签到，返回正常； 昨天没有签到，重置连续签到天数为0，返回正常； 再次调用返回已签到
     * 3、新用户第四天调用: 昨天没有签到，重置连续签到天数为0，返回正常； 再次调用返回已签到
     */
    private SignTableData checkSignPopup(ActorData actorData) {
        String uid = actorData.getUid();
        String today = userSignRedis.getSignV9Day(uid);
        String yesterday = DateHelper.ARABIAN.getYesterdayStr(DateHelper.ARABIAN.parseDate(today));
        SignTableData signData = signTableDao.findData(uid);

        if (signData != null) {
            // 如果当天已经签到或已经弹窗，直接返回
            if (today.equals(signData.getBoom_check()) || today.equals(signData.getLast_sign())) {
                throw new CommonException(UserHttpCode.YOU_HAVE_SIGNED_IN);
                // return signData;
            }

            // 如果昨天没有签到，或者连续签到天数大于等于7，重置连续签到天数为0
            if (!yesterday.equals(signData.getLast_sign()) || signData.getCombo() >= 7) {
                signData.setCombo(0);
            }

            logger.info("checkSignPopup signData: {}", JSONObject.toJSONString(signData));
            // 如果是新人签到阶段，且连续签到天数大于等于3，或者连续签到天数为0，修改签到类型为正常签到
            if (ROOKIE_SIGN_TYPE.contains(signData.getSignType()) && (signData.getCombo() >= 3 || signData.getCombo() == 0)) {
                signData.setSignType(0);
            }
            signData.setCheck_date(today);
            signData.setBoom_check(today);
            signTableDao.updateData(signData);
        } else {
            // 判断是否为新设备账号
            boolean isNewDevice = ActorUtils.isNewDeviceAccount(uid, actorData.getFirstTnId());

            // 获取签到类型
            int signType = getSignType(uid, isNewDevice);
            signData = new SignTableData();
            signData.set_id(uid);
            signData.setCheck_date(today);
            signData.setBoom_check(today);
            signData.setCombo(0);
            signData.setLast_sign("");
            signData.setSignType(signType);
            signTableDao.insertData(signData);
        }
        return signData;
    }

    /**
     * 获取签到类型
     * 0: 正常签到 (第4天及以后)
     * 1: 设备新用户签到 (前3天)
     * 2: 设备老用户签到 (前3天)
     */
    private int getSignType(String uid, boolean isNewDevice) {
        boolean isNewRegister = ActorUtils.isNewRegisterActor(uid, 3);
        if (!isNewRegister) {
            return 0;
        }
        return isNewDevice ? 1 : 2; // 1:新设备 2:旧设备
    }

    /**
     * 获取签到奖励列表
     */
    private String getSignResourceKey(int combo, int signType, ActorData actorData) {
        // 判断是否为海湾国家
        boolean isGccCountry = isGccCountry(actorData.getIpCodeCountry());
        int nextCombo = combo + 1; // 获取下一天的奖励
        String resourceKey = "";
        if (ROOKIE_SIGN_TYPE.contains(signType) && nextCombo <= 3) {
            // 新人签到阶段 (前3天)
            String deviceType = signType == 1 ? "NEW_DEVICE" : "OLD_DEVICE";
            String countryType = isGccCountry ? "MAJOR" : "MINOR";
            String keyPrefix = String.format("%s_%s_DAY%d", deviceType, countryType, nextCombo);
            resourceKey = SIGN_RESOURCE_KEY_MAP.get(keyPrefix);
        } else {
            // 正常签到阶段 (第4天及以后，7天循环)
            int normalDay = nextCombo > 7 ? 1 : nextCombo;
            String keyPrefix = String.format("NORMAL_DAY%d", normalDay);
            resourceKey = SIGN_RESOURCE_KEY_MAP.get(keyPrefix);
        }
        return resourceKey;
    }

    /**
     * 判断是否为海湾六国 (GCC)
     * 海湾六国：沙特阿拉伯(SA)、科威特(KW)、阿联酋(AE)、卡塔尔(QA)、阿曼(OM)、巴林(BH)
     */
    private boolean isGccCountry(String country) {
        if (StringUtils.isEmpty(country)) {
            return false;
        }
        String countryCode = ActorUtils.getUpperCaseCountryCode(country);
        return GCC_COUNTRIES.contains(countryCode);
    }

    /**
     * 将资源配置转换为ResourceMetaData列表
     */
    private List<ResourceMetaData> convertToResourceMetaData(String resourceKey) {
        if (StringUtils.isEmpty(resourceKey)) {
            logger.warn("Resource key is empty");
            return Collections.emptyList();
        }

        ResourceKeyConfigData config = resourceKeyConfigDao.findByKey(resourceKey);
        if (config == null || CollectionUtils.isEmpty(config.getResourceMetaList())) {
            logger.warn("Resource config not found: {}", resourceKey);
            return Collections.emptyList();
        }
        List<ResourceMetaData> resourceDataList = config.getResourceMetaList().stream().map(meta -> {
            ResourceMetaData data = new ResourceMetaData();
            BeanUtils.copyProperties(meta, data);
            return data;
        }).collect(Collectors.toList());

        return resourceDataList;
    }


    /**
     * 签到V9
     */
    public SignV9VO signV9(SignCheckV9DTO dto) {
        String uid = dto.getUid();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            throw new CommonException(UserHttpCode.USER_NOT_EXIST);
        }

        try (DistributeLock lock = new DistributeLock(getLockKey(dto.getUid()))) {
            lock.lock();
            String today = userSignRedis.getSignV9Day(uid);
            String yesterday = DateHelper.ARABIAN.getYesterdayStr(DateHelper.ARABIAN.parseDate(today));
            SignTableData signData = signTableDao.findData(dto.getUid());
            // 签到检查参数
            checkParamSignV9(signData, actorData, today);

            // 更新签到表
            int lastSignStamp = !StringUtils.isEmpty(signData.getLast_sign()) ? DateHelper.ARABIAN.stringDateToStampSecond(signData.getLast_sign()) : 0;
            int yesterdayStamp = DateHelper.ARABIAN.stringDateToStampSecond(yesterday);
            int combo = lastSignStamp >= yesterdayStamp && signData.getCombo() < 7 ? signData.getCombo() : 0;
            logger.info("signV9 lastSignStamp: {}, yesterdayStamp: {}, combo: {}", lastSignStamp, yesterdayStamp, combo);
            updateSignTable(signData, combo, today, lastSignStamp, yesterdayStamp);

            // 数数埋点
            int intToday = Integer.parseInt(today.replace("-", ""));
            doReportEvent(uid, intToday, combo);
            dAUDao.updateNewDAUEvent(actorData, 11);
            userLevelTaskService.sendTaskDataToMq(new UserLevelTaskData(uid, UserLevelConstant.CHECK_IN));

            // 奖励下发
            String signResourceKey = getSignResourceKey(combo, signData.getSignType(), actorData);
            if (ROOKIE_SIGN_TYPE.contains(signData.getSignType())) {
                resourceKeyHandlerService.sendResourceData(uid, signResourceKey, SIGN_A_TYPE, SIGN_ROOKIE_REWARD_TITLE_V9, SIGN_ROOKIE_REWARD_TITLE_V9, SIGN_ROOKIE_REWARD_TITLE_V9, "", "", 2);
            } else {
                resourceKeyHandlerService.sendResourceData(uid, signResourceKey, SIGN_A_TYPE, SIGN_REWARD_TITLE, SIGN_REWARD_TITLE, SIGN_REWARD_DESC, "", "", 2);
            }
            List<ResourceMetaData> resourceDataList = convertToResourceMetaData(signResourceKey);

            // 设置返回对象
            String countryCode = ActorUtils.getCountryCode(actorData.getCountry());
            boolean isNew = ActorUtils.isNewDeviceAccount(uid, actorData.getFirstTnId());
            SignV9VO vo = new SignV9VO();
            vo.setCombo(signData.getCombo());
            vo.setSignType(signData.getSignType());
            String jumpRoomId = "";
            if (!ObjectUtils.isEmpty(actorData.getPromotion_id())) {
                jumpRoomId = getCampaignRoomId(uid, actorData.getPromotion_id());
            }
            if (StringUtils.isEmpty(jumpRoomId)) {
                jumpRoomId = rookieRecommendService.getAllNewRoomId(isNew ? 5 : 6, countryCode);
            }
            vo.setJumpRoomId(jumpRoomId);
            vo.setSignRewardList(resourceDataList);
            if (signData.getCombo() <= 3 && ROOKIE_SIGN_TYPE.contains(signData.getSignType())) {
                BaseTaskFactory.getFactory().addSlow(new Task() {
                    @Override
                    protected void execute() {
                        sendRookieSignMsg(uid,signData.getCombo(),signData.getSignType(), resourceDataList);
                        logger.info("sendRookieSignMsg success uid={} combo={} resourceDataList={}", uid, signData.getCombo(), resourceDataList);
                    }
                });
            }

            return vo;
        }
    }

    private void sendRookieSignMsg(String uid, int combo,int signType, List<ResourceMetaData> resourceDataList) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", uid);
            return;
        }


        // 检查是否有VIP奖励
        String resourceKey = getDay3ResourceKey(signType, actorData.getIpCodeCountry());
        boolean isVipReward = hasVipCardReward(resourceKey);


        // boolean isVipReward = hasVipReward(resourceDataList);

        int vipDays = getVipDays(resourceDataList);

        int slang = actorData.getSlang();
        String title = getRookieSignTitle(combo, slang);
        String body = getRookieSignBody(combo, slang, isVipReward, vipDays);
        OfficialData officialData = new OfficialData();
        officialData.setTo_uid(uid);
        officialData.setTitle(title);
        officialData.setBody(body);
        officialData.setValid(1);
        officialData.setAtype(0);
        officialData.setAct("");
        officialData.setNews_type(6);
        officialData.setNtype(0);
        List<OfficialData.AwardInfo> awardList = new ArrayList<>();
        for (ResourceMetaData resourceData : resourceDataList) {
            int resourceType = resourceData.getResourceType() == BaseDataResourcesConstant.TYPE_COIN ? 100
                    : resourceData.getResourceType() == BaseDataResourcesConstant.TYPE_DIAMOND ? 999
                    : resourceData.getResourceType();
            ResTypeEnum typeEnum = ResTypeEnum.getByType(resourceType);
            if (typeEnum == null) {
                continue;
            }
            int num = resourceData.getResourceNumber() > 0 ? resourceData.getResourceNumber() : resourceData.getResourceTime() > 0 ? resourceData.getResourceTime() : -1;
            String tag = num > 0 ? typeEnum.formatTag(slang, num) : "";
            awardList.add(new OfficialData.AwardInfo(
                    typeEnum.getNameBySlang(slang),
                    resourceData.getResourceIcon(),
                    tag));
        }
        officialData.setAward_list(awardList);
        // 和记录时间相同方便撤回时删除
        officialData.setCtime(DateHelper.getNowSeconds());
        officialMsgPush(officialData);
    }

    private void officialMsgPush(OfficialData officialData) {
        officialDao.save(officialData);
        if (officialData.get_id() != null) {
            NoticeNewData noticeNewData = new NoticeNewData(officialData.getTo_uid(), officialData.get_id().toString());
            noticeNewData.setNtype(0);
            noticeNewDao.save(noticeNewData);
            OfficialPushMsg msg = new OfficialPushMsg();
            msg.setTitle(officialData.getTitle());
            msg.setBody(officialData.getBody());
            msg.setMsg_type(0);
            roomWebSender.sendPlayerWebMsg(null, null, officialData.getTo_uid(), msg, true);
        }
    }


    /**
     * 处理广告奖励发放
     * 检查用户注册时的广告来源信息，如果匹配则发放1000个金币奖励
     */
    private String getCampaignRoomId(String uid, String promotionId) {
        try {
            if (ObjectUtils.isEmpty(promotionId)) {
                return null;
            }

            String[] promotionPartArray = promotionId.split("-");
            if (promotionPartArray.length == 0){
                return null;
            }
            String campaign = promotionPartArray[promotionPartArray.length - 1];
            logger.info("getCampaignRoomId campaign={}, uid={}", campaign, uid);
            // 查询广告系列映射表
            AdCampaignGameData adCampaignGameData = adCampaignGameDao.findByCampaign(campaign);
            if (adCampaignGameData == null) {
                return null;
            }
            String roomId = allGameMatchService.findGameRoomByPriority(uid, adCampaignGameData.getGameType(), adCampaignGameData.getRoomType());
            if (StringUtils.isEmpty(roomId)) {
                roomId = adCampaignGameData.getRoomType() == RoomConstant.GAME_ROOM_MODE ? RoomUtils.formatGameRoomId(uid) : RoomUtils.formatRoomId(uid);
            }
            return roomId;
        } catch (Exception e) {
            logger.error("getAdjustCampaignRoomId error uid={}, error={}", uid, e.getMessage(), e);
        }
        return null;
    }


    /**
     * 签到检查参数
     */
    private void checkParamSignV9(SignTableData data, ActorData actorData, String today) {
        String uid = actorData.getUid();
        if (data == null) {
            logger.error("can not find sign table data. uid={}", uid);
            throw new CommonException(UserHttpCode.PARAM_ERROR);
        }

        // 如果当天没有弹窗，直接返回
        if (!today.equals(data.getBoom_check())) {
            logger.error("You have signed in. uid={}", uid);
            throw new CommonException(UserHttpCode.SIGN_IN_ABNORMAL);
        }

        // 如果当天已经签到，直接返回
        if (today.equals(data.getLast_sign())) {
            logger.info("You have signed in. uid={}", uid);
            throw new CommonException(UserHttpCode.YOU_HAVE_SIGNED_IN);
        }
    }


    /**
     * 设置日期
     */
    public void signV9setDay(SignCheckV9DTO req) {
        String setDay = req.getSetDay();
        String uid = req.getUid();
        if (ObjectUtils.isEmpty(uid)) {
            throw new CommonException(UserHttpCode.PARAM_ERROR);
        }

        if (!whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID)) {
            throw new CommonException(UserHttpCode.SIGN_IN_ABNORMAL);
        }

        if (!ObjectUtils.isEmpty(uid) && !ObjectUtils.isEmpty(setDay)) {
            userSignRedis.signV9setDay(uid, setDay);
        }

        if (!ObjectUtils.isEmpty(req.getUid()) && !ObjectUtils.isEmpty(req.getClearSign())) {
            signTableDao.removeSignData(req.getUid());
        }
    }

    /**
     * 根据签到天数获取新用户签到标题
     */
    private String getRookieSignTitle(int combo, int slang) {
        boolean isArabic = slang == SLangType.ARABIC;

        switch (combo) {
            case ROOKIE_SIGN_DAY_1:
                return isArabic ? ROOKIE_SIGN_TITLE_DAY1_AR : ROOKIE_SIGN_TITLE_DAY1_EN;
            case ROOKIE_SIGN_DAY_2:
                return isArabic ? ROOKIE_SIGN_TITLE_DAY2_AR : ROOKIE_SIGN_TITLE_DAY2_EN;
            case ROOKIE_SIGN_DAY_3:
                return isArabic ? ROOKIE_SIGN_TITLE_DAY3_AR : ROOKIE_SIGN_TITLE_DAY3_EN;
            default:
                return isArabic ? ROOKIE_SIGN_TITLE_DAY1_AR : ROOKIE_SIGN_TITLE_DAY1_EN;
        }
    }

    /**
     * 根据签到天数获取新用户签到内容
     */
    private String getRookieSignBody(int combo, int slang, boolean isVipReward, int vipDays) {
        boolean isArabic = slang == SLangType.ARABIC;

        switch (combo) {
            case ROOKIE_SIGN_DAY_1:
                if (isVipReward) {
                    return isArabic ? ROOKIE_SIGN_BODY_DAY1_AR : ROOKIE_SIGN_BODY_DAY1_EN;
                } else {
                    return isArabic ? ROOKIE_SIGN_BODY_DAY1_REWARD_AR : ROOKIE_SIGN_BODY_DAY1_REWARD_EN;
                }
            case ROOKIE_SIGN_DAY_2:
                return isArabic ? ROOKIE_SIGN_BODY_DAY2_AR : ROOKIE_SIGN_BODY_DAY2_EN;
            case ROOKIE_SIGN_DAY_3:
                if (isVipReward && vipDays > 0) {
                    String template = isArabic ? ROOKIE_SIGN_BODY_DAY3_VIP_AR : ROOKIE_SIGN_BODY_DAY3_VIP_EN;
                    return String.format(template, vipDays);
                } else {
                    return isArabic ? ROOKIE_SIGN_BODY_DAY3_REWARD_AR : ROOKIE_SIGN_BODY_DAY3_REWARD_EN;
                }
            default:
                return isArabic ? ROOKIE_SIGN_BODY_DAY1_REWARD_AR : ROOKIE_SIGN_BODY_DAY1_REWARD_EN;
        }
    }

    /**
     * 检查奖励列表中是否包含VIP奖励
     */
    private boolean hasVipReward(List<ResourceMetaData> resourceDataList) {
        if (resourceDataList == null || resourceDataList.isEmpty()) {
            return false;
        }

        for (ResourceMetaData resourceData : resourceDataList) {
            // 检查是否为VIP卡类型的奖励
            if (resourceData.getResourceType() == BaseDataResourcesConstant.TYPE_VIP_CARD) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取VIP奖励的天数
     */
    private int getVipDays(List<ResourceMetaData> resourceDataList) {
        if (resourceDataList == null || resourceDataList.isEmpty()) {
            return 0;
        }

        for (ResourceMetaData resourceData : resourceDataList) {
            // 检查是否为VIP卡类型的奖励
            if (resourceData.getResourceType() == BaseDataResourcesConstant.TYPE_VIP_CARD) {
                return resourceData.getResourceTime() > 0 ? resourceData.getResourceTime() :
                       resourceData.getResourceNumber() > 0 ? resourceData.getResourceNumber() : 0;
            }
        }
        return 0;
    }
}
