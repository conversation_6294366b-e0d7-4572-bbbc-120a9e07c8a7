package com.quhong.service;

import com.quhong.data.dto.ApplyMicDTO;
import com.quhong.data.vo.ApplyMicVO;
import com.quhong.enums.RoomHttpCode;
import com.quhong.exception.CommonException;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.msg.room.MicApplyChangeMsg;
import com.quhong.redis.RoomMicRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.room.mic.RoomMicService;
import com.quhong.room.redis.MicApplyRedis;
import com.quhong.utils.CustomServiceUtils;
import com.quhong.utils.PageUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class MicApplyService {

    private static final Logger logger = LoggerFactory.getLogger(MicApplyService.class);
    private static final int PAGE_SIZE = 12;

    @Autowired
    private RoomMicService roomMicService;
    @Autowired
    private RoomActorCache roomActorCache;
    @Autowired
    private RoomWebSender roomWebSender;
    @Autowired
    private MongoRoomDao mongoRoomDao;
    @Autowired
    private MicApplyRedis micApplyRedis;
    @Autowired
    private RoomMicRedis roomMicRedis;
    @Autowired
    private MicApplyService micApplyService;

    public ApplyMicVO applyForMic(ApplyMicDTO req) {
        if (1 == req.getApplyType()) {
            MongoRoomData roomData = mongoRoomDao.findData(req.getRoomId());
            if (3 != roomData.getPrivi()) {
                throw new CommonException(RoomHttpCode.APPLICATION_CANCELED);
            }
            if (!micApplyRedis.addMicApply(req.getRoomId(), req.getUid())) {
                throw new CommonException(RoomHttpCode.APPLIED);
            }
        } else if (2 == req.getApplyType()) {
            micApplyRedis.removeMicApply(req.getRoomId(), req.getUid());
        } else if (3 == req.getApplyType()) {
            roomMicService.sendInviteUpMicMsg(req.getRoomId(), req.getUid(), req.getAid());
            roomMicRedis.setApplyForMic(req.getAid());
            return micApplyService.getMicApplyList(req.getRoomId(), req.getPage());
        } else if (4 == req.getApplyType()) {
            return micApplyService.getMicApplyList(req.getRoomId(), req.getPage());
        }
        sendApplyChangeMsg(req.getRoomId());
        return micApplyService.getMicApplyList(req.getRoomId(), req.getPage());
    }

    public void cancelApply(String roomId, String uid) {
        MicApplyChangeMsg micApplyChangeMsg = micApplyRedis.cancelApply(roomId, uid);
        if (null != micApplyChangeMsg) {
            roomWebSender.sendRoomWebMsg(roomId, null, micApplyChangeMsg, true);
        }
    }

    private void sendApplyChangeMsg(String roomId) {
        int micApplyCount = micApplyRedis.getMicApplyCount(roomId);
        if (micApplyCount > 20 && CustomServiceUtils.isCustomService(RoomUtils.getRoomHostId(roomId))) {
            // 客服房间麦位申请数大于20时不发送麦位申请变化消息
            return;
        }
        MicApplyChangeMsg msg = new MicApplyChangeMsg();
        msg.setApplyCount(micApplyCount);
        msg.setFirstHead(micApplyRedis.getFirstMicApplyHead(roomId));
        roomWebSender.sendRoomWebMsg(roomId, null, msg, true);
    }

    public ApplyMicVO getMicApplyList(String roomId, int page) {
        List<String> micApplyList = micApplyRedis.getMicApplyList(roomId);
        PageUtils.PageData<String> pageData = PageUtils.getPageData(micApplyList, page, PAGE_SIZE);
        ApplyMicVO vo = new ApplyMicVO();
        for (String aid : pageData.list) {
            ApplyMicVO.ApplyActorVO applyActorVO = new ApplyMicVO.ApplyActorVO();
            applyActorVO.setAid(aid);
            RoomActorDetailData detailData = roomActorCache.getData(null, aid, false);
            applyActorVO.setName(detailData.getName());
            applyActorVO.setHead(detailData.getHead());
            applyActorVO.setMicFrame(detailData.getMicFrame());
            applyActorVO.setVipLevel(detailData.getVipLevel());
            applyActorVO.setVipMedal(detailData.getVipMedal());
            applyActorVO.setUserLevel(detailData.getLevel());
            applyActorVO.setGender(detailData.getGender());
            applyActorVO.setAge(detailData.getAge());
            applyActorVO.setBadgeList(detailData.getBadgeList());
            vo.getList().add(applyActorVO);
        }
        vo.setNextUrl(pageData.nextPage == 0 ? "" : pageData.nextPage + "");
        vo.setApplyCount(pageData.totalSize);
        return vo;
    }
}
