package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.config.AsyncConfig;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.BannerDTO;
import com.quhong.data.vo.*;
import com.quhong.enums.ClientOS;
import com.quhong.enums.SLangType;
import com.quhong.handler.HttpEnvData;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.CommonConfig;
import com.quhong.mongo.dao.IndexBannerDao;
import com.quhong.mongo.dao.RoomBannerDao;
import com.quhong.mongo.data.IndexBannerData;
import com.quhong.mysql.dao.ActorPayExternalDao;
import com.quhong.redis.BackUserStateRedis;
import com.quhong.redis.GameKingRedis;
import com.quhong.redis.HomeRankListRedis;
import com.quhong.utils.ActorUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 主页banner
 *
 * <AUTHOR>
 * @date 2022/8/25
 */
@Service
public class BannerService {

    private static final Logger logger = LoggerFactory.getLogger(BannerService.class);

    private static final String PY_CHECK_OFFICE_PUSH_CHANNEL = "check_office_push";
    private static final String GAME_KING_BANNER = "game_king";
    private static final boolean ALL_HOME_POPUP_SW = false;

    @Resource
    private IndexBannerDao indexBannerDao;
    @Resource
    private CommonConfig commonConfig;
    @Resource
    private ActorDao actorDao;
    @Resource
    private HomeRankListRedis homeRankListRedis;
    @Resource
    private ActorPayExternalDao actorPayExternalDao;
    @Resource
    private GameKingRedis gameKingRedis;
    @Resource
    private BackUserStateRedis backUserStateRedis;
    @Resource(name = AsyncConfig.ASYNC_TASK)
    private Executor executor;
    @Resource
    private HomeBannerService homeBannerService;
    @Resource
    private BackInviteUserService backInviteUserService;

    /**
     * 附带跳转内容的banner接口
     */
    public BannerVO getBanner(BannerDTO req) {
        BannerVO vo = new BannerVO();
        int area = req.getSlang() == SLangType.ENGLISH ? 1 : 2;
        String lang = req.getLang() != null ? req.getLang() : "en_US";
        // 根据不同的区域来显示不同的banner
        JSONObject switchConfig = commonConfig.getSwitchConfig();
        vo.setList(homeBannerService.getHomeBannerList(req.getUid(), IndexBannerDao.TYPE_HOME, req.getSlang(), req.getVersioncode(), req.getApp_package_name(), req.getOs(), false));
        vo.setSvip_name_color("#C39D48");
        vo.setLive(switchConfig.getIntValue("live"));
        vo.setWatchSwitch(switchConfig.getIntValue("watch_switch"));
        vo.setMusicSwitch(switchConfig.getIntValue("music_switch"));
        vo.setTopicSwitch(switchConfig.getIntValue("topic_switch"));
        vo.setBig_boss(switchConfig.getIntValue("big_boss"));
        String bigBossUrl = ServerConfig.isNotProduct() ? "https://test2.qmovies.tv/top_up/" : "https://static.youstar.live/top_up/";
        vo.setBoss_url(dealWithUrl(bigBossUrl, req.getUid(), area, req.getVersioncode(), req.getOs(), lang));
        // 返回每日榜单数据
        // 房间榜top3
        List<BannerVO.RankUserInfo> roomList = new ArrayList<>();
        List<RoomRankItemVO> roomRankList = homeRankListRedis.getRoomRankList(1);
        if (!CollectionUtils.isEmpty(roomRankList)) {
            for (int i = 0; i < Math.min(3, roomRankList.size()); i++) {
                RoomRankItemVO rankItem = roomRankList.get(i);
                BannerVO.RankUserInfo userInfo = new BannerVO.RankUserInfo();
                userInfo.setName(rankItem.getName());
                userInfo.setHead(rankItem.getHead());
                roomList.add(userInfo);
            }
        }
        // send top3
        List<BannerVO.RankUserInfo> sentList = new ArrayList<>();
        List<UserInfoItemVO> sendRankList = homeRankListRedis.getSendRankList(1);
        if (!CollectionUtils.isEmpty(sendRankList)) {
            for (int i = 0; i < Math.min(3, sendRankList.size()); i++) {
                UserInfoItemVO rankItem = sendRankList.get(i);
                BannerVO.RankUserInfo userInfo = new BannerVO.RankUserInfo();
                userInfo.setName(rankItem.getName());
                userInfo.setHead(rankItem.getHead());
                sentList.add(userInfo);
            }
        }
        // recv top3
        List<BannerVO.RankUserInfo> recvList = new ArrayList<>();
        List<UserInfoItemVO> receiveRankList = homeRankListRedis.getReceiveRankList(1);
        if (!CollectionUtils.isEmpty(receiveRankList)) {
            for (int i = 0; i < Math.min(3, receiveRankList.size()); i++) {
                UserInfoItemVO rankItem = receiveRankList.get(i);
                BannerVO.RankUserInfo userInfo = new BannerVO.RankUserInfo();
                userInfo.setName(rankItem.getName());
                userInfo.setHead(rankItem.getHead());
                recvList.add(userInfo);
            }
        }
        Map<String, List<BannerVO.RankUserInfo>> devoteList = new HashMap<>(4);
        devoteList.put("roomList", roomList);
        devoteList.put("sentList", sentList);
        devoteList.put("recvList", recvList);
        vo.setDevoteList(devoteList);
        if (req.getOs() == ClientOS.ANDROID) {
            BannerVO.SignInfo signInfo = new BannerVO.SignInfo();
            signInfo.setSignStatus(1);
            String signUrl;
            if (ServerConfig.isNotProduct()) {
                signUrl = "https://test2.qmovies.tv/sign_up/";
            } else {
                signUrl = "https://static.youstar.live/sign_up/";
            }
            signInfo.setSign_url(dealWithUrl(signUrl, req.getUid(), area, req.getVersioncode(), req.getOs(), lang));
            vo.setSignInfo(signInfo);
        }
//        if (!StringUtils.isEmpty(req.getUid())) {
//            sendBroadcastMessage(req.getUid());
//        }
        return vo;
    }


    private String dealWithUrl(String url, String uid, int nlang, int versioncode, int os, String lang) {
        if (StringUtils.isEmpty(url)) {
            return "";
        }
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(url);
        urlBuilder.queryParam("uid", uid);
        urlBuilder.queryParam("nlang", nlang);
        urlBuilder.queryParam("versioncode", versioncode);
        urlBuilder.queryParam("os", os);
        urlBuilder.queryParam("lang", lang);
        return urlBuilder.build(false).encode().toUriString();
    }

    /**
     * 判断是否是阿联酋最近60天的日活用户
     */
    private boolean isAeActiveUser(ActorData actorData) {
        if (actorData == null) {
            logger.info("actorData is null");
            return false;
        }
        if (!"AE_United Arab Emirates".equals(actorData.getCountry())) {
            return false;
        }
        if (actorData.getLastLogin() == null || actorData.getLastLogin().getLoginTime() == null) {
            return false;
        }
        int time = DateHelper.getNowSeconds() - 60 * 24 * 60 * 60;
        return actorData.getLastLogin().getLoginTime() > time;
    }

    public HomePopupVO getPopupBanner(BannerDTO req) {

        HomePopupVO vo = new HomePopupVO();
//        if (ActorUtils.isNewRegisterActor(req.getUid(), 7) && ALL_HOME_POPUP_SW) {
//
//        } else {
        List<HomeBanner> banners = homeBannerService.getHomeBannerList(req.getUid(), IndexBannerDao.TYPE_HOME_POPUP, req.getSlang(), req.getVersioncode(), req.getApp_package_name(), req.getOs(), false);
        String popupVersion = banners.stream().map(HomeBanner::getDataVersion).collect(Collectors.joining());
        vo.setList(banners);
        vo.setPopupVersion(popupVersion);
//        }

        return vo;
    }

    public MeBannerListVO meBanner(HttpEnvData dto) {
        MeBannerListVO vo = new MeBannerListVO();
        List<HomeBanner> banners = homeBannerService.getHomeBannerList(dto.getUid(), IndexBannerDao.TYPE_ME_PAGE, dto.getSlang(), dto.getVersioncode(), dto.getApp_package_name(), dto.getOs(), false);
        vo.setBanners(banners);
        return vo;
    }

    public HomePopupVO luckyGameBanner(BannerDTO req) {
        HomePopupVO vo = new HomePopupVO();
        vo.setMainList(homeBannerService.getHomeBannerList(req.getUid(), IndexBannerDao.TYPE_GAME_ROOM_MAIN, req.getSlang(), req.getVersioncode(), req.getApp_package_name(), req.getOs(), false));
        vo.setSubList(homeBannerService.getRoomBannerList(RoomBannerDao.ZONE_GAME_ROOM_SUB, req.getUid(), req.getOs(), req.getVersioncode(), req.getToken(), req.getSlang(), req.getRoomId(), req.getApp_package_name()));
        return vo;
    }

}
