package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.ActivityParticipationEvent;
import com.quhong.analysis.ActivitySpecialItemsChangeEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.SendGiftData;
import com.quhong.data.dto.ShareActivityDTO;
import com.quhong.data.vo.CrashExchangeShopVO;
import com.quhong.data.vo.HappySaudiVO;
import com.quhong.data.vo.HappyTripVO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.FriendsDao;
import com.quhong.mongo.data.FriendsData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.utils.ActorUtils;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * 沙特国庆活动2025
 */
@Service
public class HappySaudiNationalService extends OtherActivityService implements DailyTaskHandler {


    private static final Logger logger = LoggerFactory.getLogger(HappySaudiNationalService.class);
    private static final String ACTIVITY_TITLE_EN = "Happy Saudi National Day";
    private static final String ACTIVITY_TITLE_AR = "عيد وطني سعودي سعيد";
    public static String ACTIVITY_ID = "68cd5824773670795cdfdba1";
    private static String ACTIVITY_URL = String.format("https://static.youstar.live/happy_saudi2025/?activityId=%s", ACTIVITY_ID);

    // 机会兑换比例：每xxx钻石=1机会
    private static int DIAMONDS_PER_POINT = 190000;
    // 共同庆祝抽奖key
    private static final String SA_NATIONAL_DRAW_KEY = "2025SANationalDayDraw";
    private static final String eventDrawTitle = "2025SA National Day-task reward";

    // 幸运奖励
    private static final String SA_NATIONAL_LUCKY_KEY = "2025SANationalDayLuckyReward";
    private static final String SA_NATIONAL_LUCKY_TITLE = "2025SA National Day-lucky reward";

    private static final List<Integer> GIFT_LEVEL_LIST = Arrays.asList(0, 70, 300, 1000, 10000, 100000);
    private static final List<String> GIFT_KEY_LEVEL_LIST = Arrays.asList("", "2025SANationalDayReward1", "2025SANationalDayReward2", "2025SANationalDayReward3", "2025SANationalDayReward4", "2025SANationalDayReward6");
    private static final List<String> GIFT_EVENT_LEVEL_LIST = Arrays.asList("", "2025SA National Day-task reward", "2025SA National Day-task reward", "2025SA National Day-task reward", "2025SA National Day-task reward", "2025SA National Day-task reward");

    private static final List<Integer> SHARE_LEVEL_LIST = Arrays.asList(1, 3, 5);
    private static final List<String> SHARE_KEY_LEVEL_LIST = Arrays.asList("2025SANationalDayShare1", "2025SANationalDayShare3", "2025SANationalDayShare5");
    private static final List<String> SHARE_EVENT_LEVEL_LIST = Arrays.asList("2025SA National Day-share reward", "2025SA National Day-share reward", "2025SA National Day-share reward");

    private static final Interner<String> stringPool = Interners.newWeakInterner();



    private static final int HISTORY_USER_MAX_SIZE = 1000;
    private static final int PAGE_SIZE = 20;

    private static final String SA_COUNTRY_CODE = "sa";
    // 用户配置字段
    private static final String SHARE_FIELD = "share_field"; // 分享次数字段
    private static final String TOTAL_DIAMOND_FIELD = "total_d_field"; // 总钻石字段
    private static final String LEFT_CHANCE_FIELD = "left_chance_field"; // 剩余抽奖机会字段
    private static final String GIFT_LEVEL_FIELD = "gift_level_field"; // 发送礼物等级字段

    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private GiftDao giftDao;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private FriendsDao friendsDao;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            ACTIVITY_ID = "68c7e2a79384d88e067a0ada";
            ACTIVITY_URL = String.format("https://test2.qmovies.tv/happy_saudi2025/?activityId=%s", ACTIVITY_ID);
            // DIAMONDS_PER_POINT = 5000;
        }
    }

    // Redis key 方法
    private String getLocalLockKey(String activityId, String uid) {
        return String.format("getLocalLockKey:%s:%s", activityId, uid);
    }


    // 积分key - zset存储用户每日积分 flied 为uid
    private String getDayPointsKey(String activityId, String dateStr) {
        return String.format("saNational:points:%s:%s", activityId, dateStr);
    }

    //  hash存储用户相关配置
    private String getUserConfigKey(String activityId, String uid) {
        return String.format("saNational:user:config:%s:%s", activityId, uid);
    }

    // 抽奖用户记录key - list存储最近抽奖用户id
    private String getRollRecordKey(String activityId) {
        return String.format("saNational:draw:record:%s", activityId);
    }


    // 发送礼物记录key - list存储最近抽奖用户id
    private String getSendGiftRecordKey(String activityId) {
        return String.format("saNational:sendGift:record:%s", activityId);
    }

    // 中奖幸运儿历史记录key - list存储最近中奖幸运用户id,
    private String getLuckyHistoryKey(String activityId) {
        return activityId + ":saNational:lucky:history:%s" + activityId;
    }

    // 分享好友记录key - list存储最近抽奖用户id
    private String getShareRecordKey(String activityId,String uid) {
        return String.format("saNational:share:record:%s:%s", activityId,uid);
    }

    // 每日参与沙特用户key，大R抽中大奖的群发对象 - set存储每日打开活动的用户id
    private String getDailyJoinKey(String activityId, String dateStr) {
        return String.format("saNational:all:join:%s", activityId, dateStr);
    }

    public HappySaudiVO happySaudiConfig(String activityId, String uid) {
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);

        HappySaudiVO vo = new HappySaudiVO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        String dateStr = getDayByBase(activityId, uid);
        String userConfigKey = getUserConfigKey(activityId, uid);
        String dailyJoinKey = getDailyJoinKey(activityId, dateStr);
        String luckyHistoryKey = getLuckyHistoryKey(activityId);

        Map<String, String> userConfigMap = activityCommonRedis.getCommonHashAllMapStr(userConfigKey);
        vo.setTotalDiamond(Integer.parseInt(userConfigMap.getOrDefault(TOTAL_DIAMOND_FIELD, "0")));
        vo.setTravelLevel(Integer.parseInt(userConfigMap.getOrDefault(GIFT_LEVEL_FIELD, "0")));
        vo.setChanceNum(Integer.parseInt(userConfigMap.getOrDefault(LEFT_CHANCE_FIELD, "0")));
        vo.setShareCount(Integer.parseInt(userConfigMap.getOrDefault(SHARE_FIELD, "0")));
        vo.setDrawUserList(getDrawUserList(activityId));

        List<HappySaudiVO.LuckyOneVO> luckyOneList = new ArrayList<>();
        if (inActivityTime(activityId)) {
            HappySaudiVO.LuckyOneVO todayLuckyOneVO = new HappySaudiVO.LuckyOneVO();
            todayLuckyOneVO.setDayStr(dateStr);
            todayLuckyOneVO.setLuckyUserId(""); // 为空则为当天，幸运儿头像展示为app icon
            todayLuckyOneVO.setTodayDiamond(activityCommonRedis.getCommonZSetRankingScore(getDayPointsKey(activityId, dateStr), uid));
            todayLuckyOneVO.setDayCount(activityCommonRedis.getCommonZSetMemberNum(getDayPointsKey(activityId, dateStr)));
            luckyOneList.add(todayLuckyOneVO);
        }
        activityCommonRedis.getCommonListPageRecord(luckyHistoryKey, 0, 0).forEach(item -> {
            HappySaudiVO.LuckyOneVO luckyOneVO = JSON.parseObject(item, HappySaudiVO.LuckyOneVO.class);
            String dayStr = luckyOneVO.getDayStr();
            luckyOneVO.setLuckyUserIcon(ImageUrlGenerator.generateRoomUserUrl(actorDao.getActorDataFromCache(luckyOneVO.getLuckyUserId()).getHead()));
            luckyOneVO.setTodayDiamond(activityCommonRedis.getCommonZSetRankingScore(getDayPointsKey(activityId, dayStr), uid));
            luckyOneVO.setDayCount(activityCommonRedis.getCommonZSetMemberNum(getDayPointsKey(activityId, dayStr)));
            luckyOneList.add(luckyOneVO);
        });
        vo.setLuckyOneList(luckyOneList);

        // 获取最近2条发送礼物数据
        List<String> recordJsonList = activityCommonRedis.getCommonListPageRecord(getSendGiftRecordKey(activityId), 0, 2);
        List<HappySaudiVO.DrawUserVO> recordList = new ArrayList<>();
        for (String json : recordJsonList) {
            if (!StringUtils.isEmpty(json)) {
                try {
                    HappySaudiVO.DrawUserVO rollRecord = JSON.parseObject(json, HappySaudiVO.DrawUserVO.class);
                    if (rollRecord != null) {
                        rollRecord.setHead(ImageUrlGenerator.generateRoomUserUrl(actorDao.getActorDataFromCache(rollRecord.getAid()).getHead()));
                        recordList.add(rollRecord);
                    }
                } catch (Exception e) {
                    logger.error("parse roll record failed, json:{}, error:{}", json, e.getMessage());
                }
            }
        }
        vo.setRecentSendGiftList(recordList);


        List<String> shareRecordList = activityCommonRedis.getCommonListPageRecord(getShareRecordKey(activityId,uid), 0, 0);
        List<String> shareHeadList = new ArrayList<>();
        for (String shareAid : shareRecordList) {
            ActorData actorData = actorDao.getActorDataFromCache(shareAid);
            if (actorData == null) {
                continue;
            }
            shareHeadList.add(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        }
        vo.setShareHeadList(shareHeadList);
        addDailyJoinUser(dailyJoinKey, uid);

        return vo;
    }

    public HappySaudiVO.LuckyOneVO happySaudiDraw(String activityId, String uid) {
        if (!inActivityTime(activityId)) {
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }

        HappySaudiVO.LuckyOneVO vo = new HappySaudiVO.LuckyOneVO();
        int drawNum = 1;
        synchronized (stringPool.intern(getLocalLockKey(activityId, uid))) {
            String userConfigKey = getUserConfigKey(activityId, uid);
            int nowChance = activityCommonRedis.getCommonHashValue(userConfigKey, LEFT_CHANCE_FIELD);

            if (nowChance < drawNum) {
                logger.info("insufficient points. uid={} currentPoints={} drawNum={}", uid, nowChance, drawNum);
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER_POINT.getCode(), "الفرص غير كافية، أرسل هدايا لتحصل على المزيد");
            }

            // 扣除积分
            nowChance = activityCommonRedis.incCommonHashNum(userConfigKey, LEFT_CHANCE_FIELD, -drawNum);

            // 执行抽奖逻辑
            ResourceKeyConfigData.ResourceMeta meta = drawOne(uid, SA_NATIONAL_DRAW_KEY, eventDrawTitle);
            if (meta != null) {
                BeanUtils.copyProperties(meta, vo);
            }

            doReportSpecialItemsEvent(ACTIVITY_ID, uid, 2, 3, drawNum);
            vo.setChanceNum(nowChance);

            String rollRecordKey = getRollRecordKey(activityId);
            activityCommonRedis.addCommonListData(rollRecordKey, uid);
            rewardAndNoticeAll(activityId, uid, meta);
        }

        vo.setDrawUserList(getDrawUserList(activityId));

        return vo;
    }


    private void rewardAndNoticeAll(String activityId, String uid, ResourceKeyConfigData.ResourceMeta meta) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                Set<String> uidSet = activityCommonRedis.getCommonSetMember(getDailyJoinKey(activityId, getDayByBase(activityId, uid)));
                ActorData actorData = actorDao.getActorDataFromCache(uid);
                String strRid = actorData.getStrRid();
                String titleEn = "Happy Saudi National Day to %s";
                String titleAr = "%s يحتفل بالعيد الوطني السعودي";
                String bodyEn = "%s has issued %s %s days to all Saudi users participating in today's activity. Let's celebrate the glory of Saudi Arabia together!";
                String bodyAr = "%s وزّع %s %s على جميع المستخدمين السعوديين المشاركين في نشاط اليوم، لنحتفل معًا بمجد السعودية!";
                for (String aid : uidSet) {
                    resourceKeyHandlerService.sendOneResourceData(aid, meta, 905, eventDrawTitle, eventDrawTitle, eventDrawTitle, "", "", 1);
                    sendOfficialMsg(aid, "", String.format(titleEn, strRid), String.format(titleAr, strRid)
                            , String.format(bodyEn, strRid, meta.getResourceNameEn(), meta.getResourceTime()),
                            String.format(bodyAr, strRid, meta.getResourceNameAr(), meta.getResourceTime())
                            , ACTIVITY_URL);
                }
                logger.info("rewardAndNoticeAll success metaId:{} resourceName:{} uidSet:{}", meta.getMetaId(), meta.getResourceNameEn(), uidSet.size());
            }
        });


    }


    private void sendOfficialMsg(String uid, String picture, String titleEn, String titleAr, String bodyEn, String bodyAr, String url
    ) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("sendOfficialMsg actor not found for uid: {}", uid);
            return;
        }
        int slang = actorData.getSlang();
        String title = slang == SLangType.ARABIC ? titleAr : titleEn;
        String body = slang == SLangType.ARABIC ? bodyAr : bodyEn;
        String actText = slang == SLangType.ARABIC ? "شاهد" : "View";
        commonOfficialMsg(uid, picture, 0, 0, actText, title, body, url, "");
    }


    private List<HappySaudiVO.DrawUserVO> getDrawUserList(String activityId) {
        String rollRecordKey = getRollRecordKey(activityId);
        List<String> drawUserList = activityCommonRedis.getCommonListPageRecord(rollRecordKey, 0, 0);

        if (drawUserList == null || drawUserList.isEmpty()) {
            return new ArrayList<>();
        }

        // 使用LinkedHashMap保持插入顺序，同时统计每个用户的抽奖次数
        Map<String, Integer> userDrawCountMap = new LinkedHashMap<>();
        for (String uid : drawUserList) {
            userDrawCountMap.put(uid, userDrawCountMap.getOrDefault(uid, 0) + 1);
        }

        // 构建结果列表，保持原有顺序
        List<HappySaudiVO.DrawUserVO> resultList = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : userDrawCountMap.entrySet()) {
            String uid = entry.getKey();
            Integer drawCount = entry.getValue();

            ActorData actorData = actorDao.getActorDataFromCache(uid);
            if (actorData != null) {
                HappySaudiVO.DrawUserVO drawUserVO = new HappySaudiVO.DrawUserVO();
                drawUserVO.setAid(uid);
                drawUserVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                drawUserVO.setDrawCount(drawCount);
                resultList.add(drawUserVO);
            }
        }

        return resultList;
    }

    public void sendGiftHandle(SendGiftData data, String activityId) {
        try {
            int sendD = data.getNumber() * data.getPrice() * data.getAid_list().size();
            int totalNum = data.getNumber() * data.getAid_list().size();
            String uid = data.getFrom_uid();
            String dateStr = getDayByBase(activityId, uid);
            String userConfigKey = getUserConfigKey(activityId, uid);
            String dayPointsKey = getDayPointsKey(activityId, dateStr);
            String dailyJoinKey = getDailyJoinKey(activityId, dateStr);

            activityCommonRedis.incrCommonZSetRankingScoreSimple(dayPointsKey, uid, sendD);

            int beforeDiamond = activityCommonRedis.getCommonHashValue(userConfigKey, TOTAL_DIAMOND_FIELD);
            int afterDiamond = activityCommonRedis.incCommonHashNum(userConfigKey, TOTAL_DIAMOND_FIELD, sendD);

            int chanceNum = afterDiamond / DIAMONDS_PER_POINT - beforeDiamond / DIAMONDS_PER_POINT;
            if (chanceNum > 0) {
                activityCommonRedis.incCommonHashNum(userConfigKey, LEFT_CHANCE_FIELD, chanceNum);
                doReportSpecialItemsEvent(ACTIVITY_ID, uid, 1, 3, chanceNum);
            }

            int oldLevel = getNewBaseIndexLevel(beforeDiamond, GIFT_LEVEL_LIST);
            int newLevel = getNewBaseIndexLevel(afterDiamond, GIFT_LEVEL_LIST);
            if (newLevel > oldLevel) {
                // 跨级的，需要把之前等级的key都下发
                for (int level = oldLevel + 1; level <= newLevel; level++) {
                    if (level < GIFT_KEY_LEVEL_LIST.size() && StringUtils.hasLength(GIFT_KEY_LEVEL_LIST.get(level))) {
                        String resKey = GIFT_KEY_LEVEL_LIST.get(level);
                        String eventTitle = GIFT_EVENT_LEVEL_LIST.get(level);
                        resourceKeyHandlerService.sendResourceData(uid, resKey, eventTitle, eventTitle, eventTitle, ACTIVITY_URL, "");
                        logger.info("sendGiftHandle reward sent to uid: {}, level: {}, resKey: {}", uid, level, resKey);
                    }
                }
                activityCommonRedis.setCommonHashNum(userConfigKey, GIFT_LEVEL_FIELD, newLevel);
            }

            addDailyJoinUser(dailyJoinKey, uid);

            String sendGiftKey = getSendGiftRecordKey(activityId);
            HappySaudiVO.DrawUserVO rollRecord = new HappySaudiVO.DrawUserVO();
            rollRecord.setAid(uid);
            rollRecord.setSendCount(totalNum);
            GiftData giftData = giftDao.getGiftFromCache(data.getGid());
            rollRecord.setGiftIcon(giftData.getGicon());
            String json = JSONObject.toJSONString(rollRecord);
            activityCommonRedis.addCommonListRecord(sendGiftKey, json);

            logger.info("sendGiftHandle uid:{} sendD:{} beforeDiamond:{} afterDiamond:{} chanceNum:{}"
                    , uid, sendD, beforeDiamond, afterDiamond, chanceNum);
        } catch (Exception e) {
            logger.error("sendGiftHandle error: {}", e.getMessage(), e);
        }
    }

    private void addDailyJoinUser(String dailyJoinKey, String uid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        String countryCode = ActorUtils.getCountryCode(actorData.getCountry());
        if (SA_COUNTRY_CODE.equals(countryCode)) {
            activityCommonRedis.addCommonSetData(dailyJoinKey, uid);
        }
    }


    @Override
    public void dailyTaskRun(String dateStr) {
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID);
        if (activityData == null) {
            return;
        }
        int endTime = activityData.getEndTime();
        int currentTime = DateHelper.getNowSeconds();
        if (currentTime - 3600 > endTime) {
            return;
        }

        if (ObjectUtils.isEmpty(dateStr)) {
            dateStr = DateHelper.ARABIAN.getYesterdayStr(new Date());
        }

        String dayPointsKey = getDayPointsKey(ACTIVITY_ID, dateStr);
        String luckyHistoryKey = getLuckyHistoryKey(ACTIVITY_ID);

        List<String> uidList = activityCommonRedis.getCommonRankingList(dayPointsKey, 0, 0);
        List<String> luckyAlreadyList = new ArrayList<>();

        List<String> jsonList = activityCommonRedis.getCommonListPageRecord(luckyHistoryKey, 0, 0);
        for (String json : jsonList) {
            HappySaudiVO.LuckyOneVO luckyOneVO = JSON.parseObject(json, HappySaudiVO.LuckyOneVO.class);
            luckyAlreadyList.add(luckyOneVO.getLuckyUserId());
        }

        String luckyUid = null;
        Collections.shuffle(uidList);
        for (String uid : uidList) {
            luckyUid = uid;
            if (luckyAlreadyList.contains(uid)) {
                continue;
            }
            if (rechargeDailyInfoDao.getUserLastRechargeCache(uid, 30) > 0) {
                break;
            }
        }
        if (!StringUtils.isEmpty(luckyUid)) {
            handleRes(luckyUid, SA_NATIONAL_LUCKY_KEY, SA_NATIONAL_LUCKY_TITLE);
            HappySaudiVO.LuckyOneVO luckyOneVO = new HappySaudiVO.LuckyOneVO();
            luckyOneVO.setLuckyUserId(luckyUid);
            luckyOneVO.setDayStr(dateStr);
            String json = JSONObject.toJSONString(luckyOneVO);
            activityCommonRedis.addCommonListData(luckyHistoryKey, json);
            String titleEn = "Congratulations to %s on winning a prize.";
            String titleAr = "تهانينا لـ %s على فوزه بالجائزة.";
            String bodyEn = "You won 923 diamonds last day by participating in the lucky draw activity in %s.";
            String bodyAr = "فزْتَ بـ 923 ماس أمس من خلال المشاركة في السحب المحظوظ لـ %s.";
            ActorData actorData = actorDao.getActorDataFromCache(luckyUid);
            String userName = actorData.getName();
            sendOfficialMsg(luckyUid, "", String.format(titleEn, userName), String.format(titleAr, userName)
                    , String.format(bodyEn, ACTIVITY_TITLE_EN), String.format(bodyAr, ACTIVITY_TITLE_AR), ACTIVITY_URL);
            logger.info("dailyTaskRun dateStr:{} uidListSize:{} luckyUid:{}", dateStr, uidList.size(), luckyUid);
        }

    }

    public PageVO<HappySaudiVO.FriendVO> getFriendList(String activityId, String uid, int page) {
        int start = (page - 1) * PAGE_SIZE;
        List<FriendsData> dataList = friendsDao.findDataList(uid, start, PAGE_SIZE);
        List<HappySaudiVO.FriendVO> friendVOList = new ArrayList<>();
        for (FriendsData friendsData : dataList) {
            String friendAid = Objects.equals(uid, friendsData.getUidFirst()) ? friendsData.getUidSecond() : friendsData.getUidFirst();
            ActorData actorData = actorDao.getActorDataFromCache(friendAid);
            HappySaudiVO.FriendVO friendVO = new HappySaudiVO.FriendVO();
            friendVO.setUid(friendAid);
            friendVO.setUserName(actorData.getName());
            friendVO.setUserRid(actorData.getStrRid());
            friendVO.setUserHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            String shareLimitKey = getShareLimitKey(activityId, uid, friendAid);
            String shareLimit = activityCommonRedis.getCommonStrValue(shareLimitKey);
            friendVO.setStatus(StringUtils.isEmpty(shareLimit) ? 0 : 1);
            friendVOList.add(friendVO);
        }
        PageVO<HappySaudiVO.FriendVO> vo = new PageVO<>();
        vo.setList(friendVOList);
        vo.setNextUrl(friendVOList.size() >= PAGE_SIZE ? String.valueOf(page + 1) : "");
        return vo;
    }

    public void shareToPrivateMsg(ShareActivityDTO dto) {
        try {
            super.shareToPrivateMsgByAid(dto);
        } catch (CommonH5Exception e) {
            throw e;
        }
        synchronized (stringPool.intern(getLocalLockKey(dto.getActivity_id(), dto.getUid()))) {
            String userConfigKey = getUserConfigKey(dto.getActivity_id(), dto.getUid());
            int shareNum = activityCommonRedis.incCommonHashNum(userConfigKey, SHARE_FIELD, 1);
            activityCommonRedis.addCommonListRecord(getShareRecordKey(dto.getActivity_id(), dto.getUid()), dto.getAid());
            if (SHARE_LEVEL_LIST.contains(shareNum)) {
                int levelIndex = SHARE_LEVEL_LIST.indexOf(shareNum);
                handleRes(dto.getUid(), SHARE_KEY_LEVEL_LIST.get(levelIndex), SHARE_EVENT_LEVEL_LIST.get(levelIndex));
                logger.info("shareToPrivateMsg uid:{} shareNum:{} levelIndex:{} aid:{}", dto.getUid(), shareNum, levelIndex, dto.getAid());
            }
            doActivityParticipationEvent(dto.getActivity_id(), dto.getUid());
        }
    }

    private boolean checkAc(String uid) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTest = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
            if (!isWhiteTest) {
                // 灰度测试,只统计测试用户的
                return false;
            }
        }
        return true;
    }


    // 下发榜单奖励
    public void distributionRanking(String activityId) {
    }

    private void handleRes(String aid, String resKey, String eventTitle) {
        resourceKeyHandlerService.sendResourceData(aid, resKey,
                eventTitle, eventTitle, eventTitle, ACTIVITY_URL, "");
    }

    /**
     * 上报机会获取消耗事件
     */
    private void doReportSpecialItemsEvent(String activityId, String uid, int action, int source, int num) {
        ActivitySpecialItemsChangeEvent event = new ActivitySpecialItemsChangeEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setChange_action(action);
        event.setActivity_special_items_id("0");
        event.setActivity_name("2025SA National Day");
        event.setActive_id(activityId);
        event.setActivity_special_items_resource(source);
        event.setResource_desc("");
        event.setChange_nums(num);
        eventReport.track(new EventDTO(event));
    }

    private void doActivityParticipationEvent(String activityId, String uid) {
        ActivityParticipationEvent event = new ActivityParticipationEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name("2025SA National Day");
        event.setActive_id(activityId);
        event.setActivity_stage(1);
        event.setActivity_stage_desc("");
        eventReport.track(new EventDTO(event));
    }

}
