package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.RefundEvent;
import com.quhong.cache.CacheMap;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.web.HttpResponseData;
import com.quhong.data.ActorData;
import com.quhong.data.GoogleVerificationData;
import com.quhong.data.GoogleVoidedData;
import com.quhong.data.RechargeInfo;
import com.quhong.data.dto.GooglePayDTO;
import com.quhong.data.vo.GooglePayVO;
import com.quhong.exception.CommonException;
import com.quhong.httpResult.PayHttpCode;
import com.quhong.mongo.data.GooglePayNoticeData;
import com.quhong.mq.MqSenderService;
import com.quhong.mysql.data.GoodsData;
import com.quhong.mysql.data.GooglePayData;
import com.quhong.mysql.mapper.ustar.GooglePayMapper;
import com.quhong.redis.DataRedisBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;


@Service
public class GooglePayService extends AbstractPayService {
    private final static Logger logger = LoggerFactory.getLogger(GooglePayService.class);

    private static final String GOOGLE_PAY_CHARGE = "googlePayCharge";
    private static final String GOOGLE_GET_ACCESS_TOKEN = "https://accounts.google.com/o/oauth2/token";
    private static final String GOOGLE_VOIDED_URL = "https://www.googleapis.com/androidpublisher/v3/applications/in.dradhanus.liveher/purchases/voidedpurchases";
    private static final String GRANT_TYPE = "refresh_token";
    private static final String CLIENT_ID = "***********-iquai7tmunvm94lo9dekrivv4vt74ae4.apps.googleusercontent.com";
    private static final String CLIENT_SECRET = "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";
    private static final String GOOGLE_REFRESH_TOKEN = "1//04uCrniJKWl3TCgYIARAAGAQSNgF-L9Ir7g7rBhNGIiqvCHhtI4OGSX4i21ThWBJGFm8SgyQLjc1cmgt5Z2cj1YZ0FZeY74D34w";
    private static final String ACCESS_TOKEN_REQ_BODY;
    private final CacheMap<String, String> tokenCacheMap;
    private final CacheMap<String, String> purchaseCacheMap;
    private static final Set<String> ALARMED_TOKEN_SET = new HashSet<>();

    public GooglePayService() {
        tokenCacheMap = new CacheMap<>(45 * 60 * 1000L);
        purchaseCacheMap = new CacheMap<>(6 * 60 * 60 * 1000L);
    }

    @PostConstruct
    public void postInit() {
        purchaseCacheMap.start();
    }

    static {
        JSONObject reqObj = new JSONObject();
        reqObj.put("grant_type", GRANT_TYPE);
        reqObj.put("client_id", CLIENT_ID);
        reqObj.put("client_secret", CLIENT_SECRET);
        reqObj.put("refresh_token", GOOGLE_REFRESH_TOKEN);
        ACCESS_TOKEN_REQ_BODY = reqObj.toJSONString();
    }

    @Resource
    private GooglePayMapper googlePayMapper;
    @Resource
    private MqSenderService mqSenderService;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;

    /**
     * <a href="https://developers.google.com/android-publisher/authorization">...</a>
     */
    public String getAccessToken(boolean cache) {
        if (cache && tokenCacheMap.hasData(GOOGLE_GET_ACCESS_TOKEN)) {
            return tokenCacheMap.getData(GOOGLE_GET_ACCESS_TOKEN);
        }
        HttpResponseData<String> response = webClient.sendRestfulPost(GOOGLE_GET_ACCESS_TOKEN, ACCESS_TOKEN_REQ_BODY, null);
        logger.info("get google access token. response={}", JSON.toJSONString(response));
        JSONObject jsonObject = JSON.parseObject(response.getBody());
        String accessToken = jsonObject.getString("access_token");
        if (StringUtils.isEmpty(accessToken)) {
            logger.error("request google access token error. status={} body={}", response.getStatus(), response.getBody());
            if (Boolean.TRUE.equals(clusterTemplate.opsForValue().setIfAbsent("str:googleAccessToken", "ok", 60, TimeUnit.SECONDS))) {
                sendWarn("Google支付获取accessTokens失败", jsonObject.toJSONString());
            }
            throw new CommonException(PayHttpCode.SERVER_ERROR);
        }
        tokenCacheMap.cacheData(GOOGLE_GET_ACCESS_TOKEN, accessToken);
        return accessToken;
    }

    public GooglePayData getByPurchaseToken(String purchaseToken) {
        QueryWrapper<GooglePayData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("purchaseToken", purchaseToken);
        return googlePayMapper.selectOne(queryWrapper);
    }

    public GooglePayData getByOrderId(String orderId) {
        QueryWrapper<GooglePayData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("orderId", orderId);
        return googlePayMapper.selectOne(queryWrapper);
    }

    public GooglePayData getByProductIdAndToken(String productId, String purchaseToken) {
        QueryWrapper<GooglePayData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("gkind", 2);
        queryWrapper.eq("productId", productId);
        queryWrapper.eq("purchaseToken", purchaseToken);
        List<GooglePayData> googlePayDataList = googlePayMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(googlePayDataList)) {
            return null;
        }
        // 脏数据处理
        for (GooglePayData googlePayData : googlePayDataList) {
            if (null != googlePayData.getOrderId()) {
                return googlePayData;
            }
        }
        return googlePayDataList.get(0);
    }

    public void finishOrder(int orderId, GooglePayDTO payDTO, boolean firstRecharge) {
        JSONObject payload = new JSONObject();
        payload.put("i", payDTO.getImei());
        payload.put("s", payDTO.getSerial());
        payload.put("r", firstRecharge ? 1 : 0);
        LambdaUpdateWrapper<GooglePayData> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(GooglePayData::getRid, orderId).ne(GooglePayData::getFstatus, 1)
                .set(GooglePayData::getFstatus, 1)
                .set(GooglePayData::getPayload, payload.toJSONString())
                .set(GooglePayData::getMtime, Date.from(LocalDateTime.now(ZoneOffset.of("+05:30")).atZone(ZoneId.systemDefault()).toInstant()));
        int result = googlePayMapper.update(null, lambdaUpdateWrapper);
        logger.info("update finish order. orderId={} uid={} updateCount={}", orderId, payDTO.getUid(), result);
    }

    public GooglePayVO googlePayVerify(GooglePayDTO payDTO) {
        String key = payDTO.getProductId() + "-" + payDTO.getPurchaseToken();
        if (purchaseCacheMap.hasData(key, true)) {
            logger.info("repeated token from cache. uid={} token={}", payDTO.getUid(), payDTO.getPurchaseToken());
            ActorData actorData = actorDao.getActorData(payDTO.getUid());
            return new GooglePayVO(0, 0, actorData == null ? 0 : actorData.getBeans());
        }
        if (null != payDTO.getUid() && clusterRedis.hasKey("str:google:invalid:token:" + payDTO.getUid())) {
            logger.error("hacker attack. uid={} token={}", payDTO.getUid(), payDTO.getPurchaseToken());
            return new GooglePayVO(0, 0, 0);
        }
        try (DistributeLock lock = new DistributeLock(key)) {
            lock.lock();
            // 订单验证
            GooglePayData googleOrder = getByProductIdAndToken(payDTO.getProductId(), payDTO.getPurchaseToken());
            if (null != googleOrder) {
                if (googleOrder.getUserId().equals(payDTO.getUid()) && googleOrder.getFstatus() != 0) {
                    logger.info("repeated token. uid={} token={}", payDTO.getUid(), payDTO.getPurchaseToken());
                    purchaseCacheMap.cacheData(key, null);
                    ActorData actorData = actorDao.getActorData(payDTO.getUid());
                    return new GooglePayVO(0, 0, actorData == null ? 0 : actorData.getBeans());
                } else if (!googleOrder.getUserId().equals(payDTO.getUid())) {
                    logger.info("posted order={} belongs to={}", JSON.toJSONString(payDTO), googleOrder.getUserId());
                    purchaseCacheMap.cacheData(key, null);
                    throw new CommonException(PayHttpCode.NOT_YOUR_ORDER);
                }
            }
            // 判断订单是否合法
            boolean verifyResult = verifyProduct(payDTO);
            // 保存订单
            int orderId = saveOrder(googleOrder, payDTO);
            if (verifyResult) {
                // 下发商品
                String bizId = DigestUtils.md5DigestAsHex((payDTO.getPurchaseToken()).getBytes());
                int balance = chargeBeans(bizId, payDTO.getProductId(), payDTO.getUid(), GOOGLE_PAY_CHARGE, "buy " + payDTO.getProductId(), payDTO.getOrderId(), payDTO.getCouponId());
                // 首充检查
                boolean firstRecharge = firstRechargeService.checkFirstRecharge(payDTO.getUid(), 1, payDTO.getProductId());
                // 保存完成状态
                finishOrder(orderId, payDTO, firstRecharge);
                return new GooglePayVO(0, 0, balance);
            }
            throw new CommonException(PayHttpCode.GOOGLE_VERIFY_FAILED);
        }
    }

    public GoogleVerificationData googleVerification(GooglePayDTO payDTO, String accessToken) {
        String url = getGoogleVerifyUrl(payDTO.getApp_package_name(), payDTO.getProductId(), payDTO.getPurchaseToken(), accessToken);
        HttpResponseData<String> resp = webClient.sendGet(url, null);
        if (null == resp || ObjectUtils.isEmpty(resp.getBody()) || resp.getBody().contains("Invalid Credentials")) {
            logger.error("google verify failed. responseData. body is empty, token={} uid={}", payDTO.getPurchaseToken(), payDTO.getUid());
            sendWarn("Google支付接口异常", "googleVerifyUrl=" + url);
            throw new CommonException(PayHttpCode.SERVER_ERROR);
        }
        logger.info("verify google product. status={} body={}", resp.getStatus(), resp.getBody());
        GoogleVerificationData verificationData = JSON.parseObject(resp.getBody(), GoogleVerificationData.class);
        if (verificationData.getPurchaseState() == null || verificationData.getPurchaseTimeMillis() == null) {
            logger.error("google verify invalid. token={} uid={} respBody={}", accessToken, payDTO.getUid(), resp.getBody());
            if (!ALARMED_TOKEN_SET.contains(payDTO.getPurchaseToken())) {
                sendWarn("Google支付接口响应异常", "respBody=" + resp.getBody() + "\nuid=" + payDTO.getUid() + "\ntoken=" + payDTO.getPurchaseToken());
            }
            if (resp.getBody().contains("Invalid Value")) {
                // 疑似黑客攻击，拉黑，防止google取消授权，后续不走/ggp/verify
                if (null != payDTO.getUid()) {
                    clusterRedis.opsForValue().set("str:google:invalid:token:" + payDTO.getUid(), "ok", 6, TimeUnit.HOURS);
                }
                ALARMED_TOKEN_SET.add(payDTO.getPurchaseToken());
                if (ALARMED_TOKEN_SET.size() > 1000) {
                    ALARMED_TOKEN_SET.clear();
                }
            }
            throw new CommonException(PayHttpCode.PARAM_ERROR);
        }
        verificationData.setBody(resp.getBody());
        return verificationData;
    }

    /**
     * 判断订单是否合法
     * 需要注意，客户端在调用支付的时候需要把 bill_id 传给 extra 字段
     * 文档: <a href="https://developers.google.com/android-publisher/api-ref/purchases/products/get?hl=zh">...</a>
     * 文档: <a href="https://developers.google.com/android-publisher/api-ref/rest/v3/purchases.products#ProductPurchase">...</a>
     */
    public boolean verifyProduct(GooglePayDTO payDTO) {
        String accessToken = getAccessToken(true);
        if (accessToken == null) {
            logger.error("get access token error msg. uid={}", payDTO.getUid());
            throw new CommonException(PayHttpCode.SERVER_ERROR);
        }
        GoogleVerificationData verificationData = googleVerification(payDTO, accessToken);
        if (verificationData.getPurchaseState() == 1) {
            logger.info("google purchase canceled, token={} uid={} data={}", payDTO.getPurchaseToken(), payDTO.getUid(), verificationData.getBody());
            throw new CommonException(PayHttpCode.GOOGLE_DATA_CANCELED);
        }
        if (verificationData.getPurchaseState() == 2) {
            logger.info("google purchase pending, token={} uid={} data={}", payDTO.getPurchaseToken(), payDTO.getUid(), verificationData.getBody());
            throw new CommonException(PayHttpCode.GOOGLE_DATA_PENDING);
        }
        verificationData = checkAndConsume(payDTO, verificationData, accessToken);
        if (verificationData.getPurchaseState() == 0 && verificationData.getConsumptionState() == 1) {
            logger.info("google purchase valid, uid={} token={} orderId={}", payDTO.getUid(), payDTO.getPurchaseToken(), verificationData.getOrderId());
            if (null != verificationData.getPurchaseType()) {
                sendWarn("谷歌沙盒订单", verificationData.getBody());
            }
            if (StringUtils.isEmpty(payDTO.getOrderId())) {
                logger.info("google verifyProduct request orderId is empty. set from google verificationData. orderId={}", verificationData.getOrderId());
                payDTO.setOrderId(verificationData.getOrderId());
            }
            if (StringUtils.isEmpty(payDTO.getUid())) {
                logger.info("google verifyProduct uid is empty");
                String uid = parseUid(verificationData.getObfuscatedExternalAccountId());
                if (StringUtils.isEmpty(uid)) {
                    sendWarn("谷歌订单无法找到用户id", verificationData.getBody());
                    throw new CommonException(PayHttpCode.GOOGLE_DATA_INVALID);
                } else {
                    logger.info("google verifyProduct, get uid from verify data. orderId={} uid={}", verificationData.getOrderId(), uid);
                    // 判断是否是测试服的Google通知回调，测试服订单走https接口验证
                    if (null == actorDao.getActorDataFromCache(uid)) {
                        logger.info("cannot find actor, maybe from test. uid={}", uid);
                        throw new CommonException(PayHttpCode.GOOGLE_DATA_INVALID);
                    }
                    payDTO.setUid(uid);
                }
            }
            return true;
        } else {
            logger.info("google purchase invalid, token={} uid={} data={}", payDTO.getPurchaseToken(), payDTO.getUid(), verificationData.getBody());
            throw new CommonException(PayHttpCode.GOOGLE_DATA_INVALID);
        }
    }

    /**
     * 新版本取消客户端消耗透传字段，版本号为2，格式：uid-版本号
     */
    private boolean backendConsume(String obfuscatedExternalAccountId) {
        return null != obfuscatedExternalAccountId && obfuscatedExternalAccountId.contains("-");
    }

    private String parseUid(String obfuscatedExternalAccountId) {
        if (null == obfuscatedExternalAccountId) {
            return null;
        }
        // 新版本取消客户端消耗透传字段，版本号为2，格式：uid-版本号
        if (obfuscatedExternalAccountId.contains("-")) {
            return obfuscatedExternalAccountId.split("-")[0];
        }
        return obfuscatedExternalAccountId;
    }

    /**
     * 保存订单
     */
    private int saveOrder(GooglePayData googlePayData, GooglePayDTO payDTO) {
        try {
            Date date = Date.from(LocalDateTime.now(ZoneOffset.of("+05:30")).atZone(ZoneId.systemDefault()).toInstant());
            if (null != googlePayData) {
                LambdaUpdateWrapper<GooglePayData> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                lambdaUpdateWrapper.eq(GooglePayData::getRid, googlePayData.getRid())
                        .set(GooglePayData::getMtime, date);
                // 客户端只有首次购买成功时才会上传orderId，这里的orderId其实没必要跟新，因为不会变，首次保存时已经写入
                if (!StringUtils.isEmpty(payDTO.getOrderId())) {
                    lambdaUpdateWrapper.set(GooglePayData::getOrderId, payDTO.getOrderId());
                }
                googlePayMapper.update(null, lambdaUpdateWrapper);
            } else {
                int innerAcc = 0;
                if (sandboxPayRedis.androidSandbox(payDTO.getUid())) {
                    innerAcc = 1;
                }
                googlePayData = new GooglePayData();
                googlePayData.setGkind(2);
                googlePayData.setUserId(payDTO.getUid());
                googlePayData.setOrderId(payDTO.getOrderId());
                googlePayData.setPurchaseToken(payDTO.getPurchaseToken());
                googlePayData.setProductId(payDTO.getProductId());
                googlePayData.setPurchaseTimeMillis(payDTO.getPurchaseTime());
                googlePayData.setPurchaseState(payDTO.getPurchaseState());
                googlePayData.setFstatus(0);
                googlePayData.setIsAc(payDTO.getIs_ac() == null ? 0 : payDTO.getIs_ac());
                googlePayData.setInnerAcc(innerAcc);
                googlePayData.setCtime(date);
                googlePayData.setMtime(date);
                JSONObject payload = new JSONObject();
                payload.put("i", payDTO.getImei());
                payload.put("s", payDTO.getSerial());
                googlePayData.setPayload(payload.toJSONString());
                googlePayMapper.insert(googlePayData);
            }
            return googlePayData.getRid();
        } catch (Exception e) {
            logger.error("save google order error. uid={} {}", payDTO.getUid(), e.getMessage(), e);
            sendWarn("谷歌订单保存失败", JSON.toJSONString(payDTO));
            throw new CommonException(PayHttpCode.SERVER_ERROR);
        }
    }

    private String getGoogleVerifyUrl(String packageName, String productId, String token, String accessToken) {
        return "https://www.googleapis.com/androidpublisher/v3/applications/" + packageName + "/purchases/products/"
                + productId + "/tokens/" + token + "?access_token=" + accessToken;
    }

    @Override
    void onChargeFailure(String bizId, String productId, String uid) {

    }

    /**
     * 对于退款的订单尝试扣除钻石，并告警
     * <a href="https://developer.android.google.cn/google/play/billing/security?hl=zh-cn#verify">打击欺诈和滥用行为</a>
     */
    public void dealWithVoidedPurchases(String purchaseToken) {
        GooglePayData googlePayData = getByPurchaseToken(purchaseToken);
        if (null == googlePayData) {
            return;
        }
        if (1 == googlePayData.getFstatus()) {
            int deductDiamonds = deductRefundBeans(googlePayData.getUserId(), googlePayData.getProductId(), googlePayData.getOrderId(), "refund", "refund from google");
            RechargeInfo rechargeInfo = new RechargeInfo(googlePayData.getUserId(), googlePayData.getOrderId());
            rechargeInfo.setRechargeItem(RECHARGE_ITEM_MAP.get(GOOGLE_PAY_CHARGE));
            mqSenderService.sendRefundOrderToMq(MqSenderService.REFUND_GOOGLE_ROUTE_KEY, rechargeInfo);
            ActorData actorData = actorDao.getActorDataFromCache(googlePayData.getUserId());
            monitorSender.info("diamonds", "谷歌支付发生退款，[" + actorData.getRid() + "]已扣钻石：" + deductDiamonds, JSON.toJSONString(googlePayData));
            // 退款事件
            GoodsData goodsData = goodsDao.getGoodsMap().get(googlePayData.getProductId());
            RefundEvent event = new RefundEvent();
            event.setUid(googlePayData.getUserId());
            event.setPay_title(GOOGLE_PAY_CHARGE);
            event.setPay_type(1);
            event.setRecharge_time((int) (googlePayData.getCtime().getTime() / 1000));
            event.setRefund_type(2);
            event.setRecharge_money(goodsData.getShowInfoData().getOrigin().replace("$", ""));
            event.setShould_deduct(goodsData.getBeans());
            event.setActual_deduct(deductDiamonds);
            event.setCtime(DateHelper.getNowSeconds());
            eventReport.track(new EventDTO(event));
        } else {
            logger.info("cache voided purchases uid={} purchaseToken={} orderId={}",
                    googlePayData.getUserId(), purchaseToken, googlePayData.getOrderId());
        }
        googlePayData.setFstatus(3);
        updateGooglePayData(googlePayData);
    }

    /**
     * <a href="https://developers.google.cn/android-publisher/voided-purchases">Voided Purchases API</a>
     */
    public void googleVoidedPurchasesCheck() {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(GOOGLE_VOIDED_URL);
        urlBuilder.queryParam("access_token", getAccessToken(true));
        urlBuilder.queryParam("startTime", System.currentTimeMillis() - 400000);
        String url = urlBuilder.build(false).encode().toUriString();
        HttpResponseData<String> resp = webClient.sendGet(url, null);
        if (null == resp || StringUtils.isEmpty(resp.getBody())) {
            logger.error("google voided check failed. responseData. body is empty");
            return;
        }
        logger.info("google voided check result={}", resp.getBody());
        GoogleVoidedData verificationData = JSON.parseObject(resp.getBody(), GoogleVoidedData.class);
        if (null == verificationData || null == verificationData.getVoidedPurchases()) {
            return;
        }
        for (GoogleVoidedData.VoidedPurchases voidedPurchase : verificationData.getVoidedPurchases()) {
            if (!StringUtils.isEmpty(voidedPurchase.getPurchaseToken())) {
                dealWithVoidedPurchases(voidedPurchase.getPurchaseToken());
            }
        }
    }

    public List<GooglePayData> getListByOrders(Set<String> keySet) {
        QueryWrapper<GooglePayData> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("orderId", keySet);
        return googlePayMapper.selectList(queryWrapper);
    }

    public void updateGooglePayData(GooglePayData googlePayData) {
        googlePayMapper.updateById(googlePayData);
    }

    /**
     * @see <a href="https://developers.google.com/android-publisher/api-ref/rest/v3/purchases.products/consume?hl=zh-cn">充值消费</a>
     */
    public void rechargeConsume(GooglePayDTO dto, boolean sendWarn) {
        JSONObject body = new JSONObject();
        body.put("developerPayload", ServerConfig.isProduct() ? 0 : 1);
        String url = getRechargeConsumeUrl(dto.getApp_package_name(), dto.getProductId(), dto.getPurchaseToken(), getAccessToken(true));
        try {
            logger.info("recharge consume orderId={}", dto.getOrderId());
            HttpResponseData<String> response = webClient.sendRestfulPost(url, body.toJSONString(), null);
            if (null == response || !response.is2xxSuccessful()) {
                if (sendWarn) {
                    logger.error("recharge consume error. response={} consumeUrl={}", JSON.toJSONString(response), url);
                    sendWarn("谷歌购买确认失败", "response=" + JSON.toJSONString(response) + "\nconsumeUrl=" + url);
                }
                throw new CommonException(PayHttpCode.GOOGLE_DATA_INVALID);
            } else {
                logger.info("recharge consume successful. orderId={}", dto.getOrderId());
            }
        } catch (Exception e) {
            if (sendWarn) {
                logger.error("recharge consume error. consumeUrl={} {}", dto.getOrderId(), e.getMessage(), e);
                sendWarn("谷歌购买确认失败", "consumeUrl=" + url);
            }
            throw new CommonException(PayHttpCode.GOOGLE_DATA_INVALID);
        }
    }

    /**
     * 区分新老版本，旧版本走客户端消耗，新版走后台消耗。同时兼容旧版本，收到旧版本的Google通知时，延迟3分钟走一次完整流程
     */
    public GoogleVerificationData checkAndConsume(GooglePayDTO payDTO, GoogleVerificationData verificationData, String accessToken) {
        if (verificationData.getConsumptionState() == 0) {
            if (backendConsume(verificationData.getObfuscatedExternalAccountId())) {
                // 新版本后台消耗
                rechargeConsume(payDTO, true);
                verificationData = googleVerification(payDTO, accessToken);
            }
        }
        return verificationData;
    }

    public String getRechargeConsumeUrl(String packageName, String productId, String token, String accessToken) {
        return "https://androidpublisher.googleapis.com/androidpublisher/v3/applications/" + packageName + "/purchases/products/"
                + productId + "/tokens/" + token + ":consume?access_token=" + accessToken;
    }

    /**
     * 一次性商品通知，内购商品由待付费变为付费或者取消状态时通知
     * <a href="https://developer.android.google.cn/google/play/billing/lifecycle/one-time?hl=zh-cn">一次性购买</a>
     */
    private void notifyOnePurchase(GooglePayNoticeData dto) {
        GooglePayNoticeData.GoogleOneTimeNotificationData notificationData = dto.getOneTimeProductNotification();
        GoodsData goodsData = goodsDao.getGoodsMap().get(notificationData.getSku());
        if (goodsData == null) {
            logger.info("one time purchase. do not find payData. notificationType={} purchaseToken={}", notificationData.getNotificationType(), notificationData.getPurchaseToken());
            sendWarn("一次性商品通知，找不到payData", "找不到productId, channelProductId=" + notificationData.getSku() + " notificationType=" + notificationData.getNotificationType() + " purchaseToken=" + notificationData.getPurchaseToken());
            return;
        }
        GooglePayDTO googlePayDTO = new GooglePayDTO();
        googlePayDTO.setApp_package_name(dto.getPackageName());
        googlePayDTO.setProductId(notificationData.getSku());
        googlePayDTO.setPurchaseToken(notificationData.getPurchaseToken());
        googlePayDTO.setGoogleNotify(true);
        googlePayVerify(googlePayDTO);
        // 后续告警
        if (notificationData.getNotificationType() == 1) {
            // monitorSender.info("diamonds", "一次性商品通知，未付费订单付费", "notificationType=" + notificationData.getNotificationType() + " purchaseToken=" + notificationData.getPurchaseToken());
        } else if (notificationData.getNotificationType() == 2) {
            sendWarn("一次性商品通知，取消待付费商品", "channelProductId=" + notificationData.getSku() + " notificationType=" + notificationData.getNotificationType() + " purchaseToken=" + notificationData.getPurchaseToken());
        }
    }

    public void notification(GooglePayNoticeData dto) {
        if (dto.getVoidedPurchaseNotification() != null) {
            // 订单作废
            dealWithVoidedPurchases(dto.getVoidedPurchaseNotification().getPurchaseToken());
        } else if (dto.getSubscriptionNotification() != null) {
            // 订阅
            logger.error("google subscript notification not handled.");
        } else if (dto.getOneTimeProductNotification() != null) {
            // 一次性购买
            notifyOnePurchase(dto);
        } else {
            sendWarn("Google通知异常", JSON.toJSONString(dto));
        }
    }
}
