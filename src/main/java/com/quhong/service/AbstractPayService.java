package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.quhong.analysis.EventReport;
import com.quhong.constant.MatchConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.web.WebClient;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.RechargeInfo;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.feign.DataCenterService;
import com.quhong.feign.DataResourcesService;
import com.quhong.httpResult.PayHttpCode;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.CommonConfig;
import com.quhong.mongo.data.MongoActorData;
import com.quhong.monitor.MonitorSender;
import com.quhong.mq.MqSenderService;
import com.quhong.mysql.dao.GoodsDao;
import com.quhong.mysql.dao.RechargeCouponDao;
import com.quhong.mysql.dao.UserRechargeCouponDao;
import com.quhong.mysql.dao.UserRechargeRecordDao;
import com.quhong.mysql.data.GoodsData;
import com.quhong.mysql.data.RechargeCouponData;
import com.quhong.mysql.data.UserRechargeCouponData;
import com.quhong.mysql.data.UserRechargeRecordData;
import com.quhong.redis.DataRedisBean;
import com.quhong.redis.PayRedis;
import com.quhong.redis.SandboxPayRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

public abstract class AbstractPayService {
    private final static Logger logger = LoggerFactory.getLogger(AbstractPayService.class);
    // todo usdPrice、bounds写入到goods表
    private final Map<String, String> PRODUCT_USD_MAP = new HashMap<String, String>() {{
        put("product_coins_diamonds1", "0.99");
        put("product_coins_diamonds2", "4.99");
        put("product_coins_diamonds3", "19.99");
        put("product_coins_diamonds4", "49.99");
        put("product_coins_diamonds5", "89.99");
        put("product_coins_diamonds9", "199.99");
        put("product_coins_diamonds6", "299.99");
        put("product_coins_diamonds10", "399.99");
    }};
    private final Map<String, Integer> PRODUCT_BOUNDS_MAP = new HashMap<String, Integer>() {{
        put("product_coins_diamonds1", 22);
        put("product_coins_diamonds2", 120);
        put("product_coins_diamonds3", 520);
        put("product_coins_diamonds4", 1400);
        put("product_coins_diamonds5", 2600);
        put("product_coins_diamonds9", 6000);
        put("product_coins_diamonds6", 9400);
        put("product_coins_diamonds10", 0);
    }};

    protected final Map<String, String> RECHARGE_ITEM_MAP = new HashMap<String, String>() {{
        put("googlePayCharge", "google");
        put("applePayCharge", "apple");
        put("huaweiPayCharge", "huawei");
    }};

    private static final int COUPON_REWARDS_A_TYPE = 965;
    private static final String COUPON_REWARDS_TITLE = "Recharge rewards (%s)";

    @Resource
    protected WebClient webClient;
    @Resource
    protected GoodsDao goodsDao;
    @Resource
    protected FirstRechargeService firstRechargeService;
    @Resource
    protected MonitorSender monitorSender;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    protected StringRedisTemplate clusterTemplate;
    @Resource
    protected SandboxPayRedis sandboxPayRedis;
    @Resource
    protected ActorDao actorDao;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private MqSenderService mqSenderService;
    @Autowired(required = false)
    protected EventReport eventReport;
    @Resource
    private CommonTaskService commonTaskService;
    @Resource
    private DataResourcesService dataResourcesService;
    @Resource
    private RechargeCouponDao rechargeCouponDao;
    @Resource
    private UserRechargeCouponDao userRechargeCouponDao;
    @Resource
    private CommonConfig commonConfig;
    @Resource
    private UserRechargeRecordDao userRechargeRecordDao;
    @Resource
    private PayRedis payRedis;

    /**
     * 获取商品折扣
     *
     * @param beans    钻石数
     * @param discount 折扣
     */
    public int getBounds(int beans, float discount) {
        try {
            return (int) (beans * discount);
        } catch (Exception e) {
            logger.error("get bounds error.{}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 下发商品，目前都是钻石
     *
     * @param bizId     唯一业务id
     * @param productId 商品id
     * @param uid       用户id
     * @param title     title
     * @param desc      desc
     * @param couponId  优惠劵id
     * @return 用户余额
     */
    public int chargeBeans(String bizId, String productId, String uid, String title, String desc, String orderId, Integer couponId) {
        GoodsData goodsData = goodsDao.getGoodsMap().get(productId);
        if (null == goodsData || null == goodsData.getBeans()) {
            logger.error("cannot find goods. productId={} uid={}", productId, uid);
            sendWarn(title + "无法获取商品", "商品id：" + productId);
            throw new CommonException(PayHttpCode.UNKNOWN_PRODUCT);
        }
        int firstChargedAdd = firstChargedAdd(uid, goodsData.getBeans());
        boolean useCoupon = couponId != null && couponId != 0;
        float discount = useCoupon ? 0f : payRedis.getDiscount();
        int boundsAdd = getBounds(goodsData.getBeans(), discount);
        if (firstChargedAdd != 0) {
            if (firstChargedAdd > boundsAdd) {
                // 这笔充值加首充的
                logger.info("selected first charged uid:{} firstChargedAdd:{} boundsAdd:{}",
                        uid, firstChargedAdd, boundsAdd);
                boundsAdd = 0;
            } else {
                // 这笔充值不加首充的
                firstChargedAdd = 0;
            }
        }
        int changed = goodsData.getBeans() + boundsAdd;
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setId(bizId);
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(1);
        moneyDetailReq.setChanged(changed);
        moneyDetailReq.setTitle(title);
        moneyDetailReq.setDesc(desc);
        logger.info("charge beans uid={} change={} discount={}", uid, changed, discount);
        ApiResult<String> result = dataCenterService.chargeBeans(moneyDetailReq);
        // 重复打钻返回true
        if (result.isOk()) {
            // 使用优惠劵获得额外奖励
            if (useCoupon) {
                firstChargedAdd = useCoupon(uid, goodsData.getBeans(), couponId, orderId, new BigDecimal(PRODUCT_USD_MAP.getOrDefault(productId, "0")), firstChargedAdd);
            }
            RechargeInfo rechargeInfo = new RechargeInfo();
            rechargeInfo.setUid(uid);
            rechargeInfo.setOrderId(orderId);
            rechargeInfo.setRechargeMoney(Double.parseDouble(PRODUCT_USD_MAP.getOrDefault(productId, "0")));
            rechargeInfo.setRechargeDiamond(changed);
            rechargeInfo.setRechargeTime(DateHelper.getNowSeconds());
            rechargeInfo.setRechargeType(1);
            rechargeInfo.setSubType("");
            rechargeInfo.setRechargeItem(RECHARGE_ITEM_MAP.get(title));
            rechargeInfo.setFirstChargedAdd(firstChargedAdd);
            rechargeInfo.setProductId(productId);
            mqSenderService.sendUserRechargeToMq(title, rechargeInfo);
            commonTaskService.sendCommonTaskMq(new CommonMqTopicData(uid, "", "", orderId, CommonMqTaskConstant.RECHARGE_DIAMONDS, changed));
            return Integer.parseInt(result.getData());
        } else if (result.getCode().getCode() == 2) {
            // 重复打钻
            logger.error("repeat charge beans. uid={} id={}", uid, moneyDetailReq.getId());
            sendWarn("商品触发重复发钻", JSON.toJSONString(moneyDetailReq));
            return 0;
        } else {
            logger.error("data center server charge beans invoking error. code={} msg={} uid={} beans={}",
                    result.getCode().getCode(), result.getCode().getMsg(), uid, changed);
            sendWarn("商品下发失败", JSON.toJSONString(moneyDetailReq));
            onChargeFailure(bizId, productId, uid);
            throw new CommonException(HttpCode.SERVER_ERROR);
        }
    }

    private int firstChargedAdd(String uid, int beans) {
        int checkSwitch = commonConfig.getSwitchConfigValue(CommonConfig.FIRST_CHARGE_SWITCH, 0);
        if (checkSwitch == 1) {
            ActorData actorData = actorDao.getActorData(uid);
            UserRechargeRecordData firstChargeData = userRechargeRecordDao.getFirstChargeData(uid, actorData.getTn_id());
            if (firstChargeData == null) {
                return (int) (beans * MatchConstant.FIRST_CHARGE_DISCOUNT);
            }
        }
        return 0;
    }

    private int useCoupon(String uid, int beans, int couponId, String orderId, BigDecimal money, int firstChargedAdd) {
        RechargeCouponData couponData = rechargeCouponDao.selectOneById(couponId);
        if (couponData == null) {
            logger.error("can not find recharge coupon data. uid={} couponId={}", uid, couponId);
            return firstChargedAdd;
        }
        int changed = couponData.getExtraProp().multiply(BigDecimal.valueOf(beans)).intValue();
        if (firstChargedAdd != 0) {
            if (firstChargedAdd > changed) {
                // 这笔充值加首充的
                logger.info("selected first charged uid:{} firstChargedAdd:{} couponAdd:{} couponId:{}",
                        uid, firstChargedAdd, changed, couponId);
                changed = 0;
                return firstChargedAdd;
            } else {
                // 这笔充值不加首充的
                firstChargedAdd = 0;
            }
        }
        ResourcesDTO resourcesDTO = new ResourcesDTO();
        resourcesDTO.setUid(uid);
        resourcesDTO.setResId(couponId + "");
        resourcesDTO.setResType(BaseDataResourcesConstant.TYPE_RECHARGE_COUPON);
        resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_WEAR);
        resourcesDTO.setBeans(beans);
        resourcesDTO.setDesc(orderId);
        resourcesDTO.setMoney(money);
        resourcesDTO.setmTime(DateHelper.getNowSeconds());
        ApiResult<String> result = dataResourcesService.handleRes(resourcesDTO);
        if (result.isOk()) {
            MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
            moneyDetailReq.setRandomId();
            moneyDetailReq.setUid(uid);
            moneyDetailReq.setAtype(COUPON_REWARDS_A_TYPE);
            moneyDetailReq.setChanged(changed);
            String title = String.format(COUPON_REWARDS_TITLE, couponData.getExtraProp().multiply(BigDecimal.valueOf(100)).intValue() + "%");
            moneyDetailReq.setTitle(title);
            moneyDetailReq.setDesc(title);
            mqSenderService.asyncChargeDiamonds(moneyDetailReq);
        } else {
            logger.error("use coupon fail. uid={} couponId={} code={} msg={}", uid, couponId, result.getCode().getCode(), result.getCode().getMsg());
        }
        return firstChargedAdd;
    }


    public int deductRefundBeans(String uid, String productId, String orderId, String title, String desc) {
        GoodsData goodsData = goodsDao.getGoodsMap().get(productId);
        MongoActorData actorData = actorDao.findActorDataFromDB(uid);
        if (null == actorData) {
            logger.error("deductRefundBeans error. cannot find actor data uid={}", uid);
            return 0;
        }
        int rechargeDiamonds = goodsData.getBeans() + getCouponExtraReward(uid, orderId);
        int deductBeans = Math.min(actorData.getBeans(), rechargeDiamonds);
        if (deductBeans <= 0) {
            return 0;
        }
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        // TODO 需要纳入常量 退款
        moneyDetailReq.setAtype(4);
        moneyDetailReq.setChanged(-deductBeans);
        moneyDetailReq.setTitle(title);
        moneyDetailReq.setDesc(desc);
        ApiResult<String> result = dataCenterService.reduceBeans(moneyDetailReq);
        if (result.isError()) {
            logger.error("reduce beans error, msg={}", result.getData());
            return 0;
        }
        return deductBeans;
    }

    private int getCouponExtraReward(String uid, String orderId) {
        if (StringUtils.isEmpty(orderId)) {
            return 0;
        }
        UserRechargeCouponData userCouponData = userRechargeCouponDao.selectOneByUidAndOrderId(uid, orderId);
        if (userCouponData == null) {
            return 0;
        }
        RechargeCouponData couponData = rechargeCouponDao.selectOneById(userCouponData.getCouponId());
        if (couponData == null) {
            return 0;
        }
        return BigDecimal.valueOf(userCouponData.getRechargeDiamonds()).multiply(couponData.getExtraProp()).intValue();
    }


    abstract void onChargeFailure(String bizId, String productId, String uid);

    public void sendWarn(String desc, String detail, String warnName) {
        try {
            monitorSender.info(warnName, desc, detail);
        } catch (Exception e) {
            logger.error("monitor error. {}", e.getMessage(), e);
        }
    }

    public void sendWarn(String desc, String detail) {
        sendWarn(desc, detail, "ustar_pay");
    }
}
