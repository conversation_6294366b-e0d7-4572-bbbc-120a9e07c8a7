package com.quhong.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.core.web.HttpResponseData;
import com.quhong.data.GoogleGeminiData;
import com.quhong.data.dto.RoomMsgDTO;
import com.quhong.msg.room.SendRoomMsg;
import com.quhong.room.RoomWebSender;
import com.quhong.service.RoomOpenAiService;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service
public class GoogleGeminiAiServiceImpl implements RoomOpenAiService {
    private static final Logger logger = LoggerFactory.getLogger(GoogleGeminiAiServiceImpl.class);
    private static final String GOOGLE_GEMINI_API = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=AIzaSyCcsiOEh9AXsnek4hzoQBe_y_Yvajg0J6Q";
    private static final Map<String, String> headerMap = new HashMap<String, String>() {
        {
            put("Content-Type", "application/json");
        }
    };

    @Resource
    private RoomWebSender roomWebSender;

    public String chatWithOpenAi(String question){
        try {
            String data = String.format("{\"contents\":[{\"parts\":[{\"text\":\"%s\"}]}]}", question);
            long timeMillis = System.currentTimeMillis();
            HttpResponseData<String> responseData = webClient.sendRestfulPost(GOOGLE_GEMINI_API, data, headerMap);
            long costTime = System.currentTimeMillis() - timeMillis;
            logger.info("chatWithOpenAi question:{} costTime={}", question, costTime);

            if (responseData.getStatus() != 200) {
                return null;
            }

            GoogleGeminiData googleGeminiData = JSON.parseObject(responseData.getBody(), GoogleGeminiData.class);
            logger.info("googleGeminiData :{}", JSONObject.toJSONString(googleGeminiData));
            return googleGeminiData.getCandidates().get(0).getContent().getParts().get(0).getText();
        }catch (Exception e){
            logger.error("sendOpenAiResponse error");
        }
        return null;
    }


    public void sendOpenAiResponse(RoomMsgDTO roomMsgDTO){

        String responseText = chatWithOpenAi(roomMsgDTO.getContent());
        if(StringUtils.isEmpty(responseText)){
            return;
        }
        SendRoomMsg msg = new SendRoomMsg();
        msg.setMsgType(1);
        msg.setSlang(1);
        msg.setContent(responseText);
        String fromUid = RoomUtils.getRoomHostId(roomMsgDTO.getRoomId());
        roomWebSender.sendRoomWebMsg(roomMsgDTO.getRoomId(), fromUid, msg, false);
    }
}
