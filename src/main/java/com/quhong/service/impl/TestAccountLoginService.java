package com.quhong.service.impl;

import com.quhong.api.AccountLoginService;
import com.quhong.constant.LoginConstant;
import com.quhong.constant.LoginHttpCode;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.RegisterOrLoginContext;
import com.quhong.data.dto.CheckOutPhoneDTO;
import com.quhong.data.dto.RegisterPhoneAccountDTO;
import com.quhong.data.vo.RegisterRespListVO;
import com.quhong.data.vo.RegisterRespVO;
import com.quhong.enums.ClientOS;
import com.quhong.exception.CommonException;
import com.quhong.mongo.dao.PhoneAccountDao;
import com.quhong.mongo.data.MongoLoginActorData;
import com.quhong.mysql.dao.RobotActorDao;
import com.quhong.mysql.dao.TnWhiteConfigDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.RobotActorData;
import com.quhong.mysql.data.TnWhiteConfigData;
import com.quhong.mysql.data.WhiteTestData;
import com.quhong.redis.GenRidRedis;
import com.quhong.service.AbstractLoginService;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

@DubboService(timeout = 10000)
public class TestAccountLoginService extends AbstractLoginService implements AccountLoginService {
    private static final Logger logger = LoggerFactory.getLogger(TestAccountLoginService.class);
    // 无账号
    private static final int SYSTEM_ROBOT = 1;
    // 有账号
    private static final int HUMAN_ROBOT = 2;

    private static final int BASE_NUM = **********;

    private static final String BASE_PASS_WORD = "test11";
    @Resource
    private FireBasePhoneLoginService fireBasePhoneLoginService;
    @Resource
    private RobotActorDao robotActorDao;
    @Resource
    private PhoneAccountDao phoneAccountDao;
    @Resource
    private TnWhiteConfigDao tnWhiteConfigDao;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private GenRidRedis genRidRedis;

    @Override
    public RegisterOrLoginContext beforeLogin(RegisterOrLoginContext context) {
        return context;
    }

    @Override
    public RegisterOrLoginContext afterLogin(RegisterOrLoginContext context) {
        return context;
    }


    public RegisterRespVO registerHumanAccount(RegisterPhoneAccountDTO dto) {
        String code = "+86";
        String number = dto.getpNumber();
        String passWord = dto.getPassword();
        if (StringUtils.isEmpty(number) || StringUtils.isEmpty(passWord)) {
            int numberInt = genRidRedis.incrementPhoneNum();
            if (numberInt < BASE_NUM) {
                numberInt = BASE_NUM;
                genRidRedis.setPhoneNum(numberInt);
            }
            number = String.valueOf(numberInt);
            passWord = BASE_PASS_WORD;
        }
        String optUid = dto.getOptUid();
        String opUserName = dto.getOpUserName();
        if (StringUtils.isEmpty(number) || StringUtils.isEmpty(passWord)) {
            logger.error("number or pwd is empty ");
            throw new CommonException(LoginHttpCode.PARAM_ERROR);
        }
        int numberLength = number.length();
        if (numberLength > 12 || numberLength < 7) {
            logger.error("number length must between 7 and 12 but number:{} ", numberLength);
            throw new CommonException(LoginHttpCode.PARAM_ERROR.getCode(), "number length must between 7 and 12");
        }
        int newPwdLength = passWord.length();
        if (newPwdLength > 12 || newPwdLength < 6) {
            logger.error("newPwdLength={} ", newPwdLength);
            throw new CommonException(LoginHttpCode.SPECIAL_GUST_SET_FORMAT);
        }
        CheckOutPhoneDTO checkDto = new CheckOutPhoneDTO();
        checkDto.setNew_versioncode(5);
        checkDto.setAccount(null);
        checkDto.setpCountryCode(code);
        checkDto.setpNumber(number);
        checkDto.setOs(ClientOS.NONE);
        checkDto.setVersioncode(888);
        checkDto.setFromType(1);
        fireBasePhoneLoginService.checkPhone(checkDto);

        RegisterOrLoginContext context = new RegisterOrLoginContext();
        context.setpCountryCode(code);
        context.setpNumber(number);
        fireBasePhoneLoginService.initData(context);

        MongoLoginActorData registerData = new MongoLoginActorData();
        int fbGender = 1;
        String head = baseInitData.generateRandomHead(fbGender);
        String name = "User" + ThreadLocalRandom.current().nextInt(100000, 999999);
        String country = "us_United States";
        int improveData = 0;
        Map<String, Object> generalConf = new HashMap<>();
        generalConf.put("fill_gender", 0);
        generalConf.put("fill_done", 0);
        int age = ThreadLocalRandom.current().nextInt(18, 36); // 18-35
        String birthDay = generateBirthDay(age);
        String thirdUid = LoginConstant.TEST_USER_PRE + number;

        registerData.setUid(thirdUid);
        registerData.setLogin_type(LoginConstant.FIRE_BASE_PHONE_TYPE);
        registerData.setName(name);
        registerData.setHead(head);
        registerData.setAccept_talk(TALK_VSTATUS_ACCEPT);
        registerData.setVchat_status(TALK_VCHAT_FREE);
        registerData.setBeans(0);
        registerData.setGold(0);
        registerData.setBirthday(birthDay);
        registerData.setAge(age);
        registerData.setFb_gender(fbGender);
        registerData.setGender(GENDER_MALE);
        registerData.setOs(context.getOs() + "");
        registerData.setIp("**************");
        registerData.setIpCodeCountry("CN_China");
        registerData.setAndroid_id("");
        registerData.setIos_key("");
        registerData.setLang("");
        registerData.setArea(2);
        registerData.setRoom("");
        registerData.setVideo_option("0");
        registerData.setImprove_data(improveData);
        registerData.setGeneral_conf(generalConf);
        registerData.setCountry(country);
        registerData.setPromotion_id("");
        registerData.setChannel("test_user");
        registerData.setApp_package_name("");
        registerData.setVersion_code(888);
        registerData.setIdfa("");
        registerData.setDistinct_id("");
        registerData.setAccount_status(0);
        registerData.setTn_id("");
        registerData.setTn_risk(context.getTnRisk());
        registerData.setRobot(HUMAN_ROBOT);
        registerData.setFirstTnId(thirdUid);
        context.setRegisterData(registerData);

        context.setAcceptTalk(TALK_VSTATUS_ACCEPT);
        context.setToClientName(name);
        context.setToClientHead(head);
        context.setFbGender(fbGender);
        context.setToClientCountry(country);
        context.setType(LoginConstant.FIRE_BASE_PHONE_TYPE);
        context.setThirdUid(thirdUid);
        context.setLang("");
        context.setVersioncode(888);
        context.setTnId("");
        context.setChannel("test_user");
        context.setToClientPkgName("");
        context.setIp("");
        context.setRegister(true);
        registerSyn(context);
        if (!insertMysqlFinderDb(context)) {
            logger.error("registerHuman fail, save actor to finder db fail context={}", context);
            monitorSender.info(LoginConstant.USTAR_JAVA_EXCEPTION_WARN_NAME,
                    "registerHuman insert finder db t_user_money fail",
                    "context=" + context);
            throw new CommonException(LoginHttpCode.LOGIN_FAIL);
        }
        // insert  robot actor
        long cTime = (long) DateHelper.getNowSeconds();
        RobotActorData robotActorData = new RobotActorData();
        robotActorData.setStatus(HUMAN_ROBOT);
        robotActorData.setFbGender(fbGender);
        robotActorData.setUid(context.getUid());
        robotActorData.setApplicantUid(optUid);
        robotActorData.setOpUserName(opUserName);
        robotActorData.setCrt(cTime);
        robotActorDao.addOrUpdate(robotActorData);
        loginDataCountService.updateTgaData(context);
        phoneAccountDao.insertData(context.getAccount(), context.getUid(), passWord
                , context.getAccount(), thirdUid);
        TnWhiteConfigData tnWhiteConfigData = new TnWhiteConfigData();
        tnWhiteConfigData.setUid(context.getUid());
        tnWhiteConfigData.setTnId(thirdUid);
        tnWhiteConfigData.setcTime(DateHelper.getNowSeconds());
        List<TnWhiteConfigData> addList = new ArrayList<>();
        addList.add(tnWhiteConfigData);
        tnWhiteConfigDao.insertList(addList);

        WhiteTestData whiteTestData = new WhiteTestData();
        whiteTestData.setWhiteId(context.getUid());
        whiteTestData.setBelong(opUserName);
        whiteTestData.setType(WhiteTestDao.WHITE_TYPE_RID);
        whiteTestData.setCtime(DateHelper.getNowSeconds());
        whiteTestDao.insertOne(whiteTestData);

//        loginActorDao.updateFirstTnId(context.getUid(),thirdUid);

        RegisterRespVO vo = new RegisterRespVO();
        vo.setUid(context.getUid());
        vo.setRid(context.getRid());
        vo.setName(name);
        vo.setHead(head);
        vo.setApplicantUid(optUid);
        vo.setOpUserName(opUserName);
        vo.setcTime((int) cTime);
        vo.setAccount(context.getAccount());
        logger.info("account:{} pwd:{} uid:{} rid:{} name:{} register success", context.getAccount(), passWord, context.getUid(), context.getRid(), name);
        return vo;
    }

    @Override
    public RegisterRespListVO getHumanAccountList(RegisterPhoneAccountDTO dto) {
        logger.info("RegisterPhoneAccountDTO={}", dto);
        int page = dto.getPage();
        int size = dto.getSize() == 0 ? 20 : dto.getSize();
//        int start = page > 1 ? (page - 1) * size : 0;
        int queryRid = dto.getRid();
        String queryUid = null;
        if (queryRid > 0) {
            ActorData actorData = actorDao.getActorByRid(queryRid);
            if (actorData != null) {
                queryUid = actorData.getUid();
            }
        }
        String queryOptUid = dto.getOptUid();
        List<RegisterRespVO> voList = new ArrayList<>();
        List<RobotActorData> allActors = robotActorDao.getActorByHumanAccount(page, size, queryUid, queryOptUid);
        int total = robotActorDao.getTotalHumanAccount(queryUid, queryOptUid);
        if (!CollectionUtils.isEmpty(allActors)) {
            for (RobotActorData item : allActors) {
                RegisterRespVO vo = new RegisterRespVO();
                String uid = item.getUid();
                String optUid = item.getApplicantUid();
                long cTime = item.getCrt();
                String opUserName = item.getOpUserName();
                ActorData actorData = actorDao.getActorDataFromCache(uid);
                vo.setUid(uid);
                vo.setRid(actorData.getRid());
                vo.setName(actorData.getName());
                vo.setHead(actorData.getHead());
                vo.setApplicantUid(StringUtils.isEmpty(optUid) ? "" : optUid);
                vo.setOpUserName(StringUtils.isEmpty(opUserName) ? "" : opUserName);
                vo.setcTime((int) cTime);
                vo.setApplicantName("");
                String account = phoneAccountDao.getUserPhone(uid);
                vo.setAccount(StringUtils.isEmpty(account) ? "" : account);
                voList.add(vo);
            }
        }
        RegisterRespListVO vo = new RegisterRespListVO();
        vo.setTotal(total);
        vo.setList(voList);
        return vo;
    }

    @Override
    public RegisterRespVO updateHumanAccount(RegisterPhoneAccountDTO dto) {
        String uid = dto.getUid();
        String opUser = dto.getOpUserName();
        String password = dto.getPassword();
        if (!StringUtils.isEmpty(uid)) {
            RobotActorData robotActorData = robotActorDao.getOneHumanAccount(uid);
            if (robotActorData != null) {
                if (!StringUtils.isEmpty(password)) {
                    int newPwdLength = password.length();
                    if (newPwdLength > 12 || newPwdLength < 6) {
                        logger.error("newPwdLength={} ", newPwdLength);
                        throw new CommonException(LoginHttpCode.SPECIAL_GUST_SET_FORMAT);
                    }
                    String account = phoneAccountDao.getUserPhone(uid);
                    if (!StringUtils.isEmpty(account)) {
                        phoneAccountDao.updatePwd(account, uid, password);
                        logger.info("update success opUid:{} uid:{} account:{} newPwd:{}", dto.getOptUid(), uid, account, password);
                    }
                }
                if (!StringUtils.isEmpty(opUser)) {
                    robotActorData.setOpUserName(opUser);
                    robotActorDao.addOrUpdate(robotActorData);
                    logger.info("update success opUid:{} uid:{}  opUserName:{}", dto.getOptUid(), uid, opUser);
                }

                ActorData actorData = actorDao.getActorDataFromCache(uid);
                RegisterRespVO vo = new RegisterRespVO();
                vo.setUid(uid);
                vo.setRid(actorData.getRid());
                vo.setName(actorData.getName());
                vo.setHead(actorData.getHead());
                vo.setApplicantUid(StringUtils.isEmpty(robotActorData.getApplicantUid()) ? "" : robotActorData.getApplicantUid());
                vo.setOpUserName(StringUtils.isEmpty(robotActorData.getOpUserName()) ? "" : robotActorData.getOpUserName());
                vo.setApplicantName("");
                String account = phoneAccountDao.getUserPhone(uid);
                vo.setAccount(StringUtils.isEmpty(account) ? "" : account);
                long cTime = robotActorData.getCrt();
                vo.setcTime((int) cTime);
                return vo;
            }
        }
        return null;
    }

    @Override
    public RegisterRespVO deleteHumanAccount(RegisterPhoneAccountDTO dto) {
        String uid = dto.getUid();
        if (!StringUtils.isEmpty(uid)) {
            RobotActorData robotActorData = robotActorDao.getOneHumanAccount(uid);
            if (robotActorData != null) {

            }
        }
        return null;
    }
}
