package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.AppFeatureUsageEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.queues.SlowTaskQueue;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.timers.LoopTask;
import com.quhong.core.timers.TimerService;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.data.*;
import com.quhong.data.dto.RoomThemeDTO;
import com.quhong.data.dto.TruthDareV2DTO;
import com.quhong.data.vo.OptMicThemeVO;
import com.quhong.data.vo.TruthDareTopicVO;
import com.quhong.data.vo.TruthDareV2GameVO;
import com.quhong.enums.*;
import com.quhong.exception.GameException;
import com.quhong.feign.IRoomService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorConfigDao;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.dao.RoomMemberDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.monitor.MonitorSender;
import com.quhong.msg.obj.RoomMicInfoObject;
import com.quhong.msg.obj.TruthDareThemeInfoObject;
import com.quhong.msg.obj.UNameObject;
import com.quhong.msg.room.TruthDareThemeMsg;
import com.quhong.mysql.dao.RoomBlacklistDao;
import com.quhong.mysql.dao.TruthDareTopicDao;
import com.quhong.mysql.data.TruthDareTopicData;
import com.quhong.redis.RoomMicRedis;
import com.quhong.redis.RoomPwdRedis;
import com.quhong.redis.SudGameRedis;
import com.quhong.redis.TruthOrDareV2Redis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.room.redis.RoomKickRedis;
import com.quhong.sud.vo.MatchingVO;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.RoomMicListVo;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 真心话大冒险转盘
 */
@Service
public class TruthDareV2Service extends SlowTaskQueue {

    private static final Logger logger = LoggerFactory.getLogger(TruthDareV2Service.class);
    private static final String TRUTH_DARE_V2_LOCK_KEY = "create_truth_dare_v2_game_";
    private static final String GAME_RULE_URL = ServerConfig.isProduct() ? "https://static.youstar.live/truth_or_dare/" : "https://test2.qmovies.tv/truth_or_dare/";
    private static final Integer TRUTH_DARE_THEME_ID = 23;
    private static final String CREATE_GAME_MSG = "Created the truth or dare game"; // 创建游戏消息
    private static final String CREATE_GAME_MSG_AR = "تم إنشاء لعبة الحقيقة أو الجرأة"; // 创建游戏消息阿语
    private static final String CLOSED_GAME_MSG = "Truth or dare game is closed"; // 关闭游戏消息
    private static final String CLOSED_GAME_MSG_AR = "لعبة الحقيقة أو الجرأة مغلقة"; // 关闭游戏消息阿语
    private static final String GAME_TIMEOUT_NOT_START = "Truth or dare game timeout does not start, it has ended automatically"; // 游戏超时未开始
    private static final String GAME_TIMEOUT_NOT_START_AR = "لم تبدأ مهلة انتظار لعبة الحقيقة أو الجرأة، لقد انتهت تلقائيًا"; // 游戏超时未开始阿语
    /**
     * 最大等待时间 10 分钟
     */
    private static final int MAX_WAIT_TIME = 10 * 60;
    private static final int SELECT_WAIT_TIME = 30;
    private static final int MAX_SELECT_USER_SIZE = 5;

    @Resource
    private TruthDareTopicDao truthDareTopicDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomActorCache roomActorCache;
    @Resource
    private RoomMemberDao roomMemberDao;
    @Resource
    private ActorConfigDao actorConfigDao;
    @Resource
    private TruthOrDareV2Redis truthOrDareV2Redis;
    @Resource
    private IRoomService iRoomService;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private RoomMicRedis roomMicRedis;
    @Resource
    private RoomBlacklistDao roomBlacklistDao;
    @Resource
    private RoomKickRedis roomKickRedis;
    @Resource
    private RoomPwdRedis roomPwdRedis;
    @Resource
    private BaseInitData baseInitData;
    @Resource
    private MonitorSender monitorSender;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private CommonTaskService commonTaskService;
    @Resource
    private SudGameRedis sudGameRedis;

    @PostConstruct
    public void postInit() {
        TimerService.getService().addDelay(new LoopTask(this, 4000) {
            @Override
            protected void execute() {
                Set<String> waitingEndGameIds = truthOrDareV2Redis.getWaitingEndGameIds(DateHelper.getNowSeconds());
                if (!CollectionUtils.isEmpty(waitingEndGameIds)) {
                    logger.info("handle TruthDareV2 game end. waitingEndGameIds.size={} waitingEndGameIds={}", waitingEndGameIds.size(), Arrays.toString(waitingEndGameIds.toArray()));
                    BaseTaskFactory.getFactory().addSlow(new Task() {
                        @Override
                        protected void execute() {
                            for (String gameId : waitingEndGameIds) {
                                // 结束等待超时游戏
                                dismissTruthOrDareV2Game(gameId, false);
                            }
                        }
                    });
                }

                // 选择话题倒计时结束未选择话题
                Set<String> selectTopicEndIds = truthOrDareV2Redis.getGameSelectTopicEndIds(DateHelper.getNowSeconds());
                if (!CollectionUtils.isEmpty(selectTopicEndIds)) {
                    logger.info("handle selectTopicEndIds ={}", selectTopicEndIds);
                    BaseTaskFactory.getFactory().addSlow(new Task() {
                        @Override
                        protected void execute() {
                            for (String gameId : selectTopicEndIds) {
                                // 结束等待超时游戏
                                defaultSelectTopicEnd(gameId);
                            }
                        }
                    });
                }

                // 创建者不在麦位自动结束游戏
                checkGameCreatorOnRoomMic();
            }
        });
    }

    /**
     * 游戏创建者不在麦位上，结算游戏
     */
    private void checkGameCreatorOnRoomMic() {
        try {
            List<TruthOrDareV2Info> truthOrDareV2InfoList = truthOrDareV2Redis.getAllTruthDareV2Game();
            for (TruthOrDareV2Info gameInfo : truthOrDareV2InfoList) {
                String gameId = gameInfo.getGameId();
                String roomId = gameInfo.getRoomId();
                String createUid = gameInfo.getCreateUid();
                // 判断麦位是否有创建人
                RoomMicListVo roomMicVO = roomMicRedis.getRoomMicFromRedis(roomId);
                if (roomMicVO == null || CollectionUtils.isEmpty(roomMicVO.getList())){
                    logger.info("checkGameCreatorOnRoomMic1 gameInfo={}", JSONObject.toJSONString(gameInfo));
                    dismissTruthOrDareV2Game(gameId, true);
                    continue;
                }
                RoomMicInfoObject roomMicInfoObject = roomMicVO.getList().stream().filter(item -> item.getStatus() == 1 && item.getUser().getAid().equals(createUid)).findFirst().orElse(null);
                if (roomMicInfoObject == null){
                    logger.info("checkGameCreatorOnRoomMic2 gameInfo={}", JSONObject.toJSONString(gameInfo));
                    dismissTruthOrDareV2Game(gameId, true);
                }
            }

        }catch (Exception e){
            logger.error("checkGameCreatorOnRoomMic e={}", e.getMessage(), e);
        }
    }

    /**
     * 游戏等待超时或主动结束，结算游戏
     */
    private void dismissTruthOrDareV2Game(String gameId, boolean autoClose) {
        DistributeLock lock = new DistributeLock(TRUTH_DARE_V2_LOCK_KEY + gameId);
        try {
            lock.lock();
            TruthOrDareV2Info gameInfo = truthOrDareV2Redis.getGameInfo(gameId);
            if (gameInfo == null) {
                logger.error("can not find game info. gameId={}", gameId);
                return;
            }

            if (gameInfo.getStatus() == TruthDareV2Constant.GAME_RUNNING){
                logger.error("dismissTruthOrDareV2Game running. gameInfo={}", JSONObject.toJSONString(gameInfo));
            }
            String createUid = gameInfo.getCreateUid();
            String roomId = gameInfo.getRoomId();

            // 1、先设置状态
            gameInfo.setStatus(TruthDareV2Constant.GAME_END);
            truthOrDareV2Redis.saveGameInfo(gameInfo);
            truthOrDareV2Redis.removeGameTimerWaiting(gameId);
            truthOrDareV2Redis.removeGameId(roomId);

            // 2、后调用room_service接口 切换原来的麦位主题及背景
            RoomThemeDTO roomThemeDTO = new RoomThemeDTO();
            ActorData actorData = actorDao.getActorDataFromCache(createUid);
            roomThemeDTO.setRoomId(roomId);
            roomThemeDTO.setUid(createUid);
            roomThemeDTO.setOs(actorData.getIntOs());
            roomThemeDTO.setSlang(actorData.getSlang());
            roomThemeDTO.setNew_versioncode(5);
            roomThemeDTO.setThemeId(gameInfo.getOriginMicTheme());
            roomThemeDTO.setPushAll(true);
            roomThemeDTO.setNoCheckRole(true);
            roomThemeDTO.setVersioncode(actorData.getVersion_code());
            ApiResult<OptMicThemeVO> apiResult = iRoomService.optMicTheme(roomThemeDTO);
            if (apiResult.isError()){
                logger.error("dismissTruthOrDareV2Game optMicTheme roomThemeDTO:{} error:{}", JSONObject.toJSONString(roomThemeDTO), JSONObject.toJSONString(apiResult));
                monitorSender.info("ustar_java_exception", "真心话大冒险主题游戏", String.format("注意更新主题【自动结束】报错, 参数: %s", JSONObject.toJSONString(roomThemeDTO)));
                return;
            }

            // 3、发送msg
            TruthDareThemeMsg msg = new TruthDareThemeMsg();
            msg.setOpt_user(null);
            msg.setMsgEn(autoClose ? CLOSED_GAME_MSG : GAME_TIMEOUT_NOT_START);
            msg.setMsgAr(autoClose ? CLOSED_GAME_MSG_AR: GAME_TIMEOUT_NOT_START_AR);
            msg.setGameIcon(TruthDareV2Constant.GAME_ICON);
            msg.setOpt(autoClose ? TruthDareV2Constant.GAME_CLOSED_MSG : TruthDareV2Constant.GAME_TIME_OUT_MSG);
            TruthDareThemeInfoObject infoObject = new TruthDareThemeInfoObject();
            BeanUtils.copyProperties(gameInfo, infoObject);
            msg.setGameInfo(infoObject);
            roomWebSender.sendRoomWebMsg(gameInfo.getRoomId(), "", msg, true, 862);
            // 数据上报
            RoomRoleData roleData = roomMemberDao.getRoleData(roomId, createUid);
            int exitCode = autoClose ? TruthDareV2Constant.EXIT_CODE_2: TruthDareV2Constant.EXIT_CODE_1;
            reportTruthDareV2Event(gameInfo, gameInfo.getRoomId(), gameInfo.getCreateUid(), roleData.getReportRole(), exitCode);
        }catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("dismissTruthOrDareV2Game error. gameId={} {}", gameId, e.getMessage(), e);
            throw e;
        } finally {
            lock.unlock();
        }
    }


    /**
     * 默认选择话题
     */
    private void defaultSelectTopicEnd(String gameId) {
        TruthOrDareV2Info gameInfo = truthOrDareV2Redis.getGameInfo(gameId);
        if (gameInfo == null) {
            logger.error("defaultSelectTopicEnd can not find game info. gameId={}", gameId);
            return;
        }

        TruthOrDareV2Info.SelectedUserInfo selectedUserInfo = gameInfo.getSelectedUserInfo();
        if (selectedUserInfo == null) {
            logger.error("selectedUserInfo not find gameId={}", gameId);
            return;
        }
        gameInfo.setStatus(TruthDareV2Constant.GAME_WAITING);
        TruthDareTopicVO topicVO = genAndSendTopicInfo(gameInfo, selectedUserInfo, null, true);
        selectedUserInfo.setTopicId(topicVO.getId());
        selectedUserInfo.setTopicNameEn(topicVO.getTopicNameEn());
        selectedUserInfo.setTopicNameAr(topicVO.getTopicNameAr());
        selectedUserInfo.setTopicType(topicVO.getTopicType());

        List<TruthOrDareV2Info.SelectedUserInfo> selectedUserInfoList = gameInfo.getSelectedUserList() != null ? gameInfo.getSelectedUserList() : new ArrayList<>();
        selectedUserInfoList.add(selectedUserInfo);
        gameInfo.setSelectedUserInfo(null);
        int currentSize = selectedUserInfoList.size();
        int startIndex = Math.max(0, currentSize - MAX_SELECT_USER_SIZE);
        gameInfo.setSelectedUserList(selectedUserInfoList.subList(startIndex, currentSize));
        truthOrDareV2Redis.saveGameInfo(gameInfo);
    }

    /**
     * 自定义话题列表选择
     */
    public TruthDareV2GameVO gameCheck(TruthDareV2DTO reqDTO) {
        TruthDareV2GameVO vo = new TruthDareV2GameVO();
        String uid = reqDTO.getUid();
        int slang = reqDTO.getSlang();
        String roomId = reqDTO.getRoomId();
        MongoRoomData roomData = mongoRoomDao.findData(roomId);
        if (roomData == null) {
            logger.error("can not find room data. roomId={}", roomId);
            throw new GameException(HttpCode.PARAM_ERROR);
        }

        // 校验视屏房切换主题需要关掉视频开关
        if (roomData.getVideo_switch() == 1) {
            logger.info("video opt mic theme need close video first. roomId={}", roomId);
            throw new GameException(GameHttpCode.GAME_TRUTH_DARE_VIDEO_CLOSE);
        }
        List<String> myTopicIdStrList = actorConfigDao.getListUserConfig(uid, ActorConfigDao.TRUTH_DARE_TOPIC, Collections.emptyList());
        List<Integer> myTopicIdList = myTopicIdStrList.stream().map(Integer::parseInt).collect(Collectors.toList());
        List<TruthDareTopicData> truthDareTopicDataList = truthDareTopicDao.selectAllValidTruthDareTopic();
        List<TruthDareTopicVO> myTopicList = new ArrayList<>();
        List<TruthDareTopicVO> truthTopicList = new ArrayList<>();
        List<TruthDareTopicVO> dareTopicList = new ArrayList<>();

        for (TruthDareTopicData truthDareTopicData : truthDareTopicDataList) {
            TruthDareTopicVO topicVO = new TruthDareTopicVO();
            BeanUtils.copyProperties(truthDareTopicData, topicVO);
            topicVO.setName(slang == SLangType.ARABIC ? truthDareTopicData.getNameAr() : truthDareTopicData.getNameEn());
            topicVO.setSelected(0);
            if (myTopicIdList.contains(truthDareTopicData.getId())){
                topicVO.setSelected(1);

                TruthDareTopicVO topicSelectedVO = new TruthDareTopicVO();
                BeanUtils.copyProperties(truthDareTopicData, topicSelectedVO);
                topicSelectedVO.setName(slang == SLangType.ARABIC ? truthDareTopicData.getNameAr() : truthDareTopicData.getNameEn());
                topicSelectedVO.setSelected(1);
                myTopicList.add(topicSelectedVO);
            }
            if (truthDareTopicData.getTopicType() == 0){
                truthTopicList.add(topicVO);
            }else {
                dareTopicList.add(topicVO);
            }
        }
        vo.setMyTopicList(myTopicList);
        vo.setTruthTopicList(truthTopicList);
        vo.setDareTopicList(dareTopicList);
        vo.setGameRuleUrl(GAME_RULE_URL);
        return vo;
    }

    /**
     * 更新个人话题
     */
    public TruthDareV2GameVO updateTopic(TruthDareV2DTO reqDTO) {
        TruthDareV2GameVO vo = new TruthDareV2GameVO();
        String uid = reqDTO.getUid();
        int slang = reqDTO.getSlang();
        List<Integer> topicIdList = reqDTO.getTopicIdList();
        List<TruthDareTopicData> truthDareTopicDataList = truthDareTopicDao.selectAllValidTruthDareTopic();
        Set<Integer> topicAllIdSet = truthDareTopicDataList.stream().map(TruthDareTopicData::getId).collect(Collectors.toSet());
        if (!topicAllIdSet.containsAll(topicIdList)){
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        List<String> topicStrIdList = topicIdList.stream().map(String::valueOf).collect(Collectors.toList());
        actorConfigDao.updateUserListConfig(uid,  ActorConfigDao.TRUTH_DARE_TOPIC, topicStrIdList);
        List<TruthDareTopicVO> myTopicList = new ArrayList<>();
        for (TruthDareTopicData truthDareTopicData : truthDareTopicDataList) {
            if (!topicIdList.contains(truthDareTopicData.getId())){
                continue;
            }
            TruthDareTopicVO topicSelectedVO = new TruthDareTopicVO();
            BeanUtils.copyProperties(truthDareTopicData, topicSelectedVO);
            topicSelectedVO.setName(slang == SLangType.ARABIC ? truthDareTopicData.getNameAr() : truthDareTopicData.getNameEn());
            topicSelectedVO.setSelected(1);
            myTopicList.add(topicSelectedVO);
        }
        vo.setMyTopicList(myTopicList);
        return vo;
    }


    /**
     * 创建真心话大冒险V2转盘游戏
     */
    public TruthDareV2GameVO createGame(TruthDareV2DTO reqDTO) {
        String uid = reqDTO.getUid();
        String roomId = reqDTO.getRoomId();
        RoomActorDetailData actorData = roomActorCache.getData(roomId, uid, false);
        if (actorData == null) {
            logger.error("The actor not found. uid={}", uid);
            throw new GameException(GameHttpCode.THE_ACTOR_NOT_FOUND);
        }

        RoomRoleData roleData = roomMemberDao.getRoleData(roomId, uid);
        if (!roleData.isAdmin()) {
            logger.info("Only room owner and admin can create the game. uid={}, roomId={}", uid, roomId);
            throw new GameException(GameHttpCode.OWNER_AND_ADMIN_CAN_CREATE);
        }

        MongoRoomData roomData = mongoRoomDao.findData(roomId);
        // 校验视频房切换主题需要关掉视频开关
        if (roomData.getVideo_switch() == 1) {
            logger.info("video opt mic theme need close video first. roomId={}", roomId);
            throw new GameException(GameHttpCode.GAME_TRUTH_DARE_VIDEO_CLOSE);
        }

        SudGameSimpleInfo sudGameInfo = sudGameRedis.getSudGameSimpleInfo(roomId);
        if (sudGameInfo != null && sudGameInfo.getGameType() == SudGameConstant.WOISSPY_GAME) {
            throw new GameException(GameHttpCode.EXIST_GAME, new Object[]{SLangType.ENGLISH == reqDTO.getSlang() ? "Who is the spy" : "من هو الجاسوس"});
        }

        DistributeLock lock = new DistributeLock(TRUTH_DARE_V2_LOCK_KEY + roomId);
        try {
            lock.lock();
            // 判断用户在麦位索引0-9可创建
            RoomMicListVo roomMicVO = roomMicRedis.getRoomMicFromRedis(roomId);
            if (roomMicVO == null || CollectionUtils.isEmpty(roomMicVO.getList())){
                logger.info("createGame roomMicVO={}", JSONObject.toJSONString(roomMicVO));
                throw new GameException(GameHttpCode.THE_TRUTH_GAME_NEED_ON_MIC);
            }
            RoomMicInfoObject roomMicInfoObject = roomMicVO.getList().stream().filter(item -> item.getStatus() == 1 && item.getUser().getAid().equals(uid)).findFirst().orElse(null);
            if (roomMicInfoObject == null){
                throw new GameException(GameHttpCode.THE_TRUTH_GAME_NEED_ON_MIC);
            }
            if (roomMicInfoObject.getIndex() > 9){
                throw new GameException(GameHttpCode.THE_TRUTH_GAME_NEED_ON_ORDER_MIC);
            }
            // 校验是否已经存在该转盘游戏了
            String gameId = truthOrDareV2Redis.getGameIdByRoomId(roomId);
            if (!StringUtils.isEmpty(gameId)) {
                logger.info("The game has already existed. gameId={}", JSONObject.toJSONString(gameId));
                throw new GameException(GameHttpCode.GAME_HAS_ALREADY_EXISTED);
            }
            // 判断游戏模式, 如果是自定义话题模式必须（至少选择一个真心话题目和一个大冒险题目）
            validateTopicCheck(reqDTO);

            // 1.先调用room_service接口 切换麦位主题已经获取麦位列表
            RoomThemeDTO roomThemeDTO = new RoomThemeDTO();
            BeanUtils.copyProperties(reqDTO, roomThemeDTO);
            roomThemeDTO.setThemeId(TRUTH_DARE_THEME_ID);
            ApiResult<OptMicThemeVO> apiResult = iRoomService.optMicTheme(roomThemeDTO);
            if (apiResult.isError()){
                logger.error("optMicTheme error:{}", JSONObject.toJSONString(apiResult));
                throw new GameException(apiResult.getCode());
            }

            // 2.后创建游戏信息
            TruthOrDareV2Info gameInfo = buildTruthOrDareV2Info(reqDTO, roomData);
            truthOrDareV2Redis.saveGameInfo(gameInfo);
            truthOrDareV2Redis.saveGameId(roomId, gameInfo.getGameId());
            int endTime = DateHelper.getNowSeconds() + MAX_WAIT_TIME;
            truthOrDareV2Redis.setGameTimeOut(gameInfo.getGameId(), endTime);

            TruthDareThemeMsg msg = new TruthDareThemeMsg();
            msg.setOpt_user(buildUNameObject(actorData));
            msg.setMsgEn(CREATE_GAME_MSG);
            msg.setMsgAr(CREATE_GAME_MSG_AR);
            msg.setGameIcon(TruthDareV2Constant.GAME_ICON);
            msg.setOpt(TruthDareV2Constant.GAME_CREATE_MSG);
            TruthDareThemeInfoObject infoObject = new TruthDareThemeInfoObject();
            BeanUtils.copyProperties(gameInfo, infoObject);
            msg.setGameInfo(infoObject);
            roomWebSender.sendRoomWebMsg(roomId, "", msg, false, 862);

            TruthDareV2GameVO vo = new TruthDareV2GameVO();
            gameInfo.setSelectedUserList(null);
            vo.setGameInfo(gameInfo);
            vo.setMicThemeInfo(apiResult.getData());
            return vo;
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("create turntable game error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
            throw e;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 构建UNameObject
     */
    private UNameObject buildUNameObject(RoomActorDetailData actorData) {
        if (actorData == null) {
            return null;
        }
        String uid = actorData.getAid();
        UNameObject userInfo = new UNameObject();
        userInfo.setRid(actorData.getRid());
        userInfo.setName(actorData.getName());
        userInfo.setRole(actorData.getNewRole());
        userInfo.setHead(actorData.getHead());
        // 设置vip
        userInfo.setVip(actorData.getVipLevel());
        userInfo.setVipMedal(actorData.getVipMedal());
        // 设置徽章
        userInfo.setBadgeList(actorData.getBadgeList());
        userInfo.setLevel(actorData.getLevel());
        // 设置气泡
        userInfo.setBid(actorData.getBubbleId());
        userInfo.setIdentify(actorData.getIdentify());
        userInfo.setUid(uid);
        userInfo.setIsNewUser(actorData.getIsNewUser());
        return userInfo;
    }


    public void validateTopicCheck(TruthDareV2DTO reqDTO) {
        Integer gameMode = reqDTO.getGameMode();
        if (gameMode == null){
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        if (gameMode != TruthDareV2Constant.MODE_CUSTOM){
            return;
        }
        List<Integer> topicIdList = reqDTO.getTopicIdList();
        if (CollectionUtils.isEmpty(topicIdList) || topicIdList.size() <= 1){
            throw new GameException(GameHttpCode.GAME_TRUTH_DARE_LEAST_TOPIC);
        }
        if(topicIdList.size() > 10){
            throw new GameException(GameHttpCode.GAME_TRUTH_DARE_MAX_TOPIC);
        }

        List<TruthDareTopicData> truthDareTopicDataList = truthDareTopicDao.selectAllValidTruthDareTopic();
        Set<Integer> topicAllIdSet = truthDareTopicDataList.stream().map(TruthDareTopicData::getId).collect(Collectors.toSet());
        if (!topicAllIdSet.containsAll(topicIdList)){
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        // 创建id到有效题目的映射
        Map<Integer, TruthDareTopicData> topicMap = truthDareTopicDataList.stream().collect(Collectors.toMap(TruthDareTopicData::getId, Function.identity()));
        boolean hasTruth = false;
        boolean hasDare = false;
        for (Integer topicId : topicIdList) {
            TruthDareTopicData topic = topicMap.get(topicId);
            if (topic != null) {
                if (topic.getTopicType() == 0) {
                    hasTruth = true;
                } else if (topic.getTopicType() == 1) {
                    hasDare = true;
                }
                // 如果两种类型都已找到，提前终止循环
                if (hasTruth && hasDare) {
                    break;
                }
            }
        }

        if (!hasTruth){
            throw new GameException(GameHttpCode.GAME_TRUTH_DARE_SHOULD_SELECT_TRUTH);
        }

        if (!hasDare){
            throw new GameException(GameHttpCode.GAME_TRUTH_DARE_SHOULD_SELECT_DARE);
        }
    }

    private TruthOrDareV2Info buildTruthOrDareV2Info(TruthDareV2DTO reqDTO, MongoRoomData roomData) {
        TruthOrDareV2Info info = new TruthOrDareV2Info();
        info.setGameId(new ObjectId().toString());
        info.setCreateUid(reqDTO.getUid());
        info.setRoomId(reqDTO.getRoomId());
        info.setStatus(TruthDareV2Constant.GAME_WAITING);
        info.setGameMode(reqDTO.getGameMode());
        int originMicTheme = roomData.getMic_theme();
        // 新用户创建room时mic_theme默认0, 0是直播房麦位主题
        originMicTheme = originMicTheme == 0 && roomData.getRoomMode() != RoomConstant.LIVE_ROOM_MODE ? RoomConstant.MIC_THEME_DEFAULT_ID : originMicTheme;
        info.setOriginMicTheme(originMicTheme);
        info.setTopicList(CollectionUtils.isEmpty(reqDTO.getTopicIdList()) ? Collections.emptyList() : reqDTO.getTopicIdList());
        return info;
    }

    /**
     * 开始真心话大冒险V2转盘游戏
     */
    public TruthDareV2GameVO startGame(TruthDareV2DTO reqDTO) {
        String uid = reqDTO.getUid();
        String roomId = reqDTO.getRoomId();
        String gameId = reqDTO.getGameId();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("The actor not found. uid={}", uid);
            throw new GameException(GameHttpCode.THE_ACTOR_NOT_FOUND);
        }

        DistributeLock lock = new DistributeLock(TRUTH_DARE_V2_LOCK_KEY +gameId);
        try {
            lock.lock();
            // 校验是否已经存在该转盘游戏了
            TruthOrDareV2Info gameInfo = truthOrDareV2Redis.getGameInfo(gameId);
            if (gameInfo == null) {
                logger.error("The game is not found. uid={} gameId={}", reqDTO.getUid(), gameId);
                throw new GameException(GameHttpCode.THE_GAME_IS_NOT_FOUND);
            }

            if (!gameInfo.getCreateUid().equals(uid)){
                logger.info("The game auth error. uid={} gameInfo={}", reqDTO.getUid(), JSONObject.toJSONString(gameInfo));
                throw new GameException(GameHttpCode.AUTH_ERROR);
            }

            if (gameInfo.getStatus() == TruthDareV2Constant.GAME_RUNNING) {
                logger.info("The game status is running. uid={} gameId={}", reqDTO.getUid(), reqDTO.getGameId());
                throw new GameException(GameHttpCode.THE_TRUTH_GAME_IS_RUNNING);
            }

            if (gameInfo.getStatus() == TruthDareV2Constant.GAME_END) {
                logger.info("The game status is error. uid={} gameId={}", reqDTO.getUid(), reqDTO.getGameId());
                throw new GameException(GameHttpCode.THE_GAME_IS_END);
            }
            // 判断麦位是否大于等于2人
            RoomMicListVo roomMicVO = roomMicRedis.getRoomMicFromRedis(roomId);
            if (roomMicVO == null || CollectionUtils.isEmpty(roomMicVO.getList())){
                throw new GameException(GameHttpCode.THE_GAME_CANNOT_CREATE);
            }
            List<RoomMicInfoObject> micUserList = roomMicVO.getList().stream().filter(item -> item.getStatus() == 1).collect(Collectors.toList());
            if (micUserList.size() < TruthDareV2Constant.GAME_MIN_USER){
                throw new GameException(GameHttpCode.THE_GAME_CANNOT_CREATE);
            }
            // 随机抽取麦位用户
            gameInfo.setStatus(TruthDareV2Constant.GAME_RUNNING);
            gameInfo.setRoundNum(gameInfo.getRoundNum() + 1);
            RoomMicInfoObject selectedUser = this.selectUserForRegular(gameInfo, micUserList);
            String selectUid = selectedUser.getUser().getAid();
            int selectIndex = selectedUser.getIndex();

            ActorData selectActor = actorDao.getActorDataFromCache(selectUid);
            TruthOrDareV2Info.SelectedUserInfo selectedUserInfo = new TruthOrDareV2Info.SelectedUserInfo();
            selectedUserInfo.setRoundNum(gameInfo.getRoundNum());
            selectedUserInfo.setIndex(selectIndex);
            selectedUserInfo.setUid(selectUid);
            selectedUserInfo.setName(selectActor.getName());
            selectedUserInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(selectActor.getHead()));
            gameInfo.setSelectedUserInfo(selectedUserInfo);
            List<String> aidList = micUserList.stream().map(micInfo -> micInfo.getUser().getAid()).collect(Collectors.toList());
            gameInfo.setPlayUidList(aidList);
            truthOrDareV2Redis.saveGameInfo(gameInfo);

            // 设置返回结果
            TruthDareV2GameVO vo = new TruthDareV2GameVO();
            vo.setSelectedUser(selectedUserInfo);
            // 设置倒计时自动选择话题, 然后再推送
            int selectEndTime = DateHelper.getNowSeconds() + SELECT_WAIT_TIME;
            truthOrDareV2Redis.setGameSelectTopicTimeOut(gameInfo.getGameId(), selectEndTime);
            // 续期游戏自动结束时间
            int endTime = DateHelper.getNowSeconds() + MAX_WAIT_TIME;
            truthOrDareV2Redis.setGameTimeOut(gameInfo.getGameId(), endTime);
            // 发送抽中用户推送消息
            sendStartGameMsg(reqDTO, gameInfo, vo);
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    sendPlayGameMqMsg(gameInfo.getRoomId(), gameInfo.getGameId(), aidList);
                }
            });
            return vo;
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("create turntable game error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
            throw e;
        } finally {
            lock.unlock();
        }
    }

    private RoomMicInfoObject selectUserForRegular(TruthOrDareV2Info gameInfo, List<RoomMicInfoObject> micUserList){
        List<TruthOrDareV2Info.SelectedUserInfo> selectedUserInfoList = gameInfo.getSelectedUserList() == null ? Collections.emptyList() : gameInfo.getSelectedUserList();
        int size = selectedUserInfoList.size();
        String lastRoundUid = size > 0 ? selectedUserInfoList.get(size - 1).getUid() : "";
        List<RoomMicInfoObject> micUserAfterList = micUserList.stream().filter(item -> !item.getUser().getAid().equals(lastRoundUid)).collect(Collectors.toList());
        Collections.shuffle(micUserAfterList);
        return micUserAfterList.get(new Random().nextInt(micUserAfterList.size()));
    }


    private void setOnMicUserGameStatus(String roomId, String gameId, List<String> aidList, int gameStatus){
        if (CollectionUtils.isEmpty(aidList)){
            return;
        }
        for (String aid : aidList) {
            if (gameStatus == TruthDareV2Constant.GAME_RUNNING){
                truthOrDareV2Redis.setUserInGameHValue(aid, roomId, gameId);
            }else {
                truthOrDareV2Redis.delUserInGameHValue(aid);
            }
        }
    }

    /**
     * 获取游戏信息
     */
    public TruthDareV2GameVO gameInfo(TruthDareV2DTO reqDTO) {
        TruthOrDareV2Info gameInfo = truthOrDareV2Redis.getGameInfoByRoomId(reqDTO.getRoomId());
        if (gameInfo == null) {
            logger.info("The game is not found. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoomId());
            throw new GameException(GameHttpCode.THE_ROOM_NOT_TRUTH_DARE_GAME);
        }
        TruthDareV2GameVO vo = new TruthDareV2GameVO();
        List<TruthOrDareV2Info.SelectedUserInfo> selectedUserInfoList = gameInfo.getSelectedUserList();
        if (gameInfo.getSelectedUserInfo() == null && !CollectionUtils.isEmpty(selectedUserInfoList)){
            gameInfo.setSelectedUserInfo(selectedUserInfoList.get(selectedUserInfoList.size() - 1));
        }
        gameInfo.setSelectedUserList(null);
        vo.setGameInfo(gameInfo);
        return vo;
    }

    private void sendStartGameMsg(TruthDareV2DTO reqDTO, TruthOrDareV2Info gameInfo, TruthDareV2GameVO vo){
        TruthDareThemeMsg msg = new TruthDareThemeMsg();
        msg.setGameIcon(TruthDareV2Constant.GAME_ICON);
        msg.setOpt(TruthDareV2Constant.GAME_START_MSG);
        TruthDareThemeInfoObject infoObject = new TruthDareThemeInfoObject();
        BeanUtils.copyProperties(gameInfo, infoObject);
        infoObject.setThemeId(TRUTH_DARE_THEME_ID);
        infoObject.setSelectedIndex(vo.getSelectedUser().getIndex());
        infoObject.setSelectedUid(vo.getSelectedUser().getUid());
        infoObject.setSelectedHead(vo.getSelectedUser().getHead());
        infoObject.setSelectedUserName(vo.getSelectedUser().getName());
        msg.setGameInfo(infoObject);
        roomWebSender.sendRoomWebMsg(reqDTO.getRoomId(), reqDTO.getUid(), msg, false, 862);
    }


    private void sendPlayGameMqMsg(String roomId, String gameId, List<String> aidList){
        if (CollectionUtils.isEmpty(aidList)){
            return;
        }
        for (String aid : aidList) {
            commonTaskService.sendCommonTaskMq(new CommonMqTopicData(aid, roomId, "", gameId, CommonMqTaskConstant.PLAY_TRUTH_DARE_V2_WHEEL, 1));
        }
    }

    /**
     * 真心话大冒险V2 选择话题
     */
    public TruthDareV2GameVO chooseTopic(TruthDareV2DTO reqDTO) {
        String uid = reqDTO.getUid();
        String roomId = reqDTO.getRoomId();
        String gameId = reqDTO.getGameId();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("The actor not found. uid={}", uid);
            throw new GameException(GameHttpCode.THE_ACTOR_NOT_FOUND);
        }

        DistributeLock lock = new DistributeLock(TRUTH_DARE_V2_LOCK_KEY + gameId);
        try {
            lock.lock();
            // 校验是否已经存在该转盘游戏了
            TruthOrDareV2Info gameInfo = truthOrDareV2Redis.getGameInfo(reqDTO.getGameId());
            if (gameInfo == null) {
                logger.error("The game is not found. uid={} gameId={}", reqDTO.getUid(), reqDTO.getGameId());
                throw new GameException(GameHttpCode.THE_GAME_IS_NOT_FOUND);
            }

            if (gameInfo.getStatus() != TruthDareV2Constant.GAME_RUNNING) {
                logger.info("The game status is error. uid={} gameInfo={}", reqDTO.getUid(), JSONObject.toJSONString(gameInfo));
                throw new GameException(GameHttpCode.THE_GAME_YOU_SELECTED_MODE);
            }
            if (reqDTO.getTopicType() == null){
                throw new GameException(GameHttpCode.PARAM_ERROR);
            }

            TruthOrDareV2Info.SelectedUserInfo selectedUserInfo = gameInfo.getSelectedUserInfo();
            if (selectedUserInfo == null || !selectedUserInfo.getUid().equals(uid)){
                logger.info("The game selectedUserInfo error. uid={} gameInfo={}", reqDTO.getUid(), JSONObject.toJSONString(gameInfo));
                throw new GameException(GameHttpCode.AUTH_ERROR);
            }

            gameInfo.setStatus(TruthDareV2Constant.GAME_WAITING);
            TruthDareTopicVO topicVO = genAndSendTopicInfo(gameInfo, selectedUserInfo, reqDTO, false);
            selectedUserInfo.setTopicId(topicVO.getId());
            selectedUserInfo.setTopicNameEn(topicVO.getTopicNameEn());
            selectedUserInfo.setTopicNameAr(topicVO.getTopicNameAr());
            selectedUserInfo.setTopicType(topicVO.getTopicType());

            List<TruthOrDareV2Info.SelectedUserInfo> selectedUserInfoList = gameInfo.getSelectedUserList() != null ? gameInfo.getSelectedUserList() : new ArrayList<>();
            selectedUserInfoList.add(selectedUserInfo);
            gameInfo.setSelectedUserInfo(null);
            int currentSize = selectedUserInfoList.size();
            int startIndex = Math.max(0, currentSize - MAX_SELECT_USER_SIZE);
            gameInfo.setSelectedUserList(selectedUserInfoList.subList(startIndex, currentSize));
            truthOrDareV2Redis.saveGameInfo(gameInfo);

            TruthDareV2GameVO vo = new TruthDareV2GameVO();
            vo.setSelectedTopic(topicVO);
            gameInfo.setSelectedUserList(null);
            vo.setGameInfo(gameInfo);
            return vo;
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("create turntable game error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
            throw e;
        } finally {
            lock.unlock();
        }
    }

    // 根据话题类型选取话题并推送
    private TruthDareTopicVO genAndSendTopicInfo(TruthOrDareV2Info gameInfo, TruthOrDareV2Info.SelectedUserInfo selectedUserInfo, TruthDareV2DTO reqDTO, boolean pushSelf) {
        ActorData selectActor = actorDao.getActorDataFromCache(selectedUserInfo.getUid());
        List<TruthDareTopicData> truthDareTopicDataList = truthDareTopicDao.selectAllTruthDareTopic();
        TruthDareTopicVO topicVO = new TruthDareTopicVO();
        TruthDareTopicData truthDareTopicData = null;
        if (gameInfo.getGameMode() == TruthDareV2Constant.MODE_RANDOM){
            List<TruthDareTopicData> topicList = truthDareTopicDataList;
            if (reqDTO != null){
                topicList = truthDareTopicDataList.stream().filter(item -> Objects.equals(item.getTopicType(), reqDTO.getTopicType())).collect(Collectors.toList());
            }
            truthDareTopicData = topicList.get(new Random().nextInt(topicList.size()));
        }else {
            List<Integer> topicIdList = gameInfo.getTopicList();
            List<TruthDareTopicData> topicList = truthDareTopicDataList.stream().filter(item -> topicIdList.contains(item.getId())).collect(Collectors.toList());
            if (reqDTO != null){
                topicList = topicList.stream().filter(item -> Objects.equals(item.getTopicType(), reqDTO.getTopicType())).collect(Collectors.toList());
            }
            truthDareTopicData = topicList.get(new Random().nextInt(topicList.size()));
        }
        int slang = reqDTO == null ? 1 : reqDTO.getSlang();
        topicVO.setId(truthDareTopicData.getId());
        topicVO.setTopicType(truthDareTopicData.getTopicType());
        topicVO.setName(slang == SLangType.ARABIC ? truthDareTopicData.getNameAr() : truthDareTopicData.getNameEn());
        topicVO.setTopicNameEn(truthDareTopicData.getNameEn());
        topicVO.setTopicNameAr(truthDareTopicData.getNameAr());
        topicVO.setSelected(1);
        topicVO.setSelectedIndex(selectedUserInfo.getIndex());
        topicVO.setSelectedUid(selectedUserInfo.getUid());
        topicVO.setSelectedHead(ImageUrlGenerator.generateRoomUserUrl(selectActor.getHead()));
        topicVO.setSelectedUserName(selectActor.getName());
        truthOrDareV2Redis.removeGameSelectTopicTime(gameInfo.getGameId());

        // 推送选择的话题
        TruthDareThemeMsg msg = new TruthDareThemeMsg();
        msg.setGameIcon(TruthDareV2Constant.GAME_ICON);
        msg.setOpt(TruthDareV2Constant.GAME_REMIND_MSG);
        TruthDareThemeInfoObject infoObject = new TruthDareThemeInfoObject();
        BeanUtils.copyProperties(gameInfo, infoObject);
        BeanUtils.copyProperties(topicVO, infoObject);
        infoObject.setThemeId(TRUTH_DARE_THEME_ID);
        msg.setGameInfo(infoObject);
        roomWebSender.sendRoomWebMsg(gameInfo.getRoomId(), pushSelf ? "" : selectedUserInfo.getUid(), msg, false, 862);
        return topicVO;
    }

    /**
     * 结束真心话大冒险V2转盘游戏
     */
    public TruthDareV2GameVO closeGame(TruthDareV2DTO reqDTO) {
        String uid = reqDTO.getUid();
        String roomId = reqDTO.getRoomId();
        String gameId = reqDTO.getGameId();
        boolean forceExit = reqDTO.isForceExit();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("The actor not found. uid={}", uid);
            throw new GameException(GameHttpCode.THE_ACTOR_NOT_FOUND);
        }

        // RoomRoleData roleData = roomMemberDao.getRoleData(roomId, uid);
        // if (!roleData.isAdmin()) {
        //     logger.info("Only room owner and admin can create the game. uid={}, roomId={}", uid, roomId);
        //     throw new GameException(GameHttpCode.OWNER_AND_ADMIN_CAN_CREATE);
        // }

        TruthOrDareV2Info gameInfo = truthOrDareV2Redis.getGameInfo(gameId);
        if (gameInfo == null) {
            logger.error("The game is not found. uid={} gameId={}", reqDTO.getUid(), reqDTO.getGameId());
            throw new GameException(GameHttpCode.THE_GAME_IS_NOT_FOUND);
        }

        DistributeLock lock = new DistributeLock(TRUTH_DARE_V2_LOCK_KEY + gameId);
        try {
            lock.lock();
            if (gameInfo.getStatus() == TruthDareV2Constant.GAME_END) {
                logger.info("The game is end. uid={} gameId={}", reqDTO.getUid(), reqDTO.getGameId());
                throw new GameException(GameHttpCode.THE_GAME_IS_END);
            }

            if (!forceExit && gameInfo.getStatus() == TruthDareV2Constant.GAME_RUNNING){
                logger.error("closeGame running. gameInfo={}", JSONObject.toJSONString(gameInfo));
                throw new GameException(GameHttpCode.THE_TRUTH_GAME_IS_RUNNING);
            }

            // 1、先设置状态
            gameInfo.setStatus(TruthDareV2Constant.GAME_END);
            truthOrDareV2Redis.saveGameInfo(gameInfo);
            truthOrDareV2Redis.removeGameTimerWaiting(gameId);
            truthOrDareV2Redis.removeGameId(gameInfo.getRoomId());


            // 2、后调用room_service接口 切换原来的麦位主题及背景
            RoomThemeDTO roomThemeDTO = new RoomThemeDTO();
            BeanUtils.copyProperties(reqDTO, roomThemeDTO);
            roomThemeDTO.setThemeId(gameInfo.getOriginMicTheme());
            roomThemeDTO.setNoCheckRole(true);
            ApiResult<OptMicThemeVO> apiResult = iRoomService.optMicTheme(roomThemeDTO);
            if (apiResult.isError()){
                logger.error("closeGame optMicTheme roomThemeDTO:{} error:{}", JSONObject.toJSONString(roomThemeDTO), JSONObject.toJSONString(apiResult));
                monitorSender.info("ustar_java_exception", "真心话大冒险主题游戏", String.format("注意更新主题【手动结束】报错, 参数: %s", JSONObject.toJSONString(roomThemeDTO)));
                throw new GameException(apiResult.getCode());
            }

            // 3、发送游戏状态消息
            boolean pushAll = reqDTO.isPushAll();
            TruthDareThemeMsg msg = new TruthDareThemeMsg();
            msg.setOpt_user(null);
            msg.setMsgEn(CLOSED_GAME_MSG);
            msg.setMsgAr(CLOSED_GAME_MSG_AR);
            msg.setGameIcon(TruthDareV2Constant.GAME_ICON);
            msg.setOpt(TruthDareV2Constant.GAME_CLOSED_MSG);
            TruthDareThemeInfoObject infoObject = new TruthDareThemeInfoObject();
            BeanUtils.copyProperties(gameInfo, infoObject);
            msg.setGameInfo(infoObject);
            roomWebSender.sendRoomWebMsg(gameInfo.getRoomId(), pushAll? "" : reqDTO.getUid(), msg, true, 862);
            TruthDareV2GameVO vo = new TruthDareV2GameVO();
            gameInfo.setSelectedUserList(null);
            vo.setGameInfo(gameInfo);
            vo.setMicThemeInfo(apiResult.getData());
            // 事件上报
            RoomRoleData roleData = roomMemberDao.getRoleData(roomId, uid);
            int exitCode;
            if (reqDTO.getExitCode() == null){
                exitCode = gameInfo.getCreateUid().equals(uid) ? TruthDareV2Constant.EXIT_CODE_0 : TruthDareV2Constant.EXIT_CODE_4;
            }else {
                exitCode = reqDTO.getExitCode();  //创建者下麦或被提下麦
            }
            reportTruthDareV2Event(gameInfo, roomId, uid, roleData.getReportRole(), exitCode);
            return vo;
        }catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("closeGame error. gameId={} {}", gameId, e.getMessage(), e);
            throw e;
        } finally {
            lock.unlock();
        }
    }

    public MatchingVO matching(String uid) {
        MatchingVO vo = new MatchingVO();
        List<TruthOrDareV2Info> truthOrDareV2InfoList = truthOrDareV2Redis.getAllTruthDareV2Game();
        for (TruthOrDareV2Info gameInfo : truthOrDareV2InfoList) {
            String roomId = gameInfo.getRoomId();
            String hostUid = RoomUtils.getRoomHostId(roomId);
            if (actorDao.getActorDataFromCache(hostUid).getRobot() == 1) {
                continue;
            }
            if (gameInfo.getStatus() != TruthDareV2Constant.GAME_WAITING || roomBlacklistDao.isBlock(roomId, uid) || roomKickRedis.isKick(roomId, uid)) {
                continue;
            }
            if (roomPwdRedis.hasPwdFromCache(roomId)) {
                continue;
            }
            RoomMicListVo roomMicVO = roomMicRedis.getRoomMicFromRedis(roomId);
            if (roomMicVO == null || CollectionUtils.isEmpty(roomMicVO.getList())){
                continue;
            }
            vo.setMatchedPlayers(roomMicVO.getList().stream().filter(item -> item.getStatus() == 1).map(roomMicInfo -> roomMicInfo.getUser().getAid()).collect(Collectors.toSet()));
            vo.setRoomId(roomId);
            vo.setGameId(gameInfo.getGameId());
            break;
        }
        Set<String> resultSet = new HashSet<>();
        for (int i = 0; i < 5; i++) {
            resultSet.add(baseInitData.generateRandomHead(2));
        }
        vo.setRecentlyPlayers(resultSet);
        return vo;
    }

    private void reportTruthDareV2Event(TruthOrDareV2Info gameInfo, String roomId, String actionUid, int role, int exitCode) {
        AppFeatureUsageEvent event = new AppFeatureUsageEvent();
        event.setUid(actionUid);
        event.setRoom_id(roomId);
        event.setAction(actionUid.equals(gameInfo.getCreateUid()) ? 1 : 0);
        event.setApp_feature_type(1);
        event.setApp_feature_name("Truth or Dare");
        event.setRoom_role(role);
        JSONObject descJson = new JSONObject();
        descJson.put("mode", gameInfo.getGameMode() == 0 ? "Truth" : "Dare");
        event.setApp_feature_desc(descJson.toJSONString());
        event.setDuration(DateHelper.getNowSeconds() - new ObjectId(gameInfo.getGameId()).getTimestamp());
        event.setExit_reason(exitCode);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

}
