package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.web.HttpResponseData;
import com.quhong.core.web.WebClient;
import com.quhong.data.dto.OrderListDTO;
import com.quhong.data.vo.OrderVO;
import com.quhong.data.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class TransactionService {
    private static final Logger logger = LoggerFactory.getLogger(TransactionService.class);
    private final String orderListUrl = ServerConfig.isProduct() ? "http://ustarinnerpay.linktoanywhere.net:21010/inner/pay_center/order_list" : "http://innerpay.cryptolinktoanywhere.com:21010/inner/pay_center/order_list";

    @Resource
    private WebClient webClient;

    public PageVO<OrderVO> queryOrderList(OrderListDTO dto) {
        HttpResponseData<String> responseData = webClient.sendRestfulPost(orderListUrl, JSON.toJSONString(dto), null, 3);
        if (responseData.isError() || null == responseData.getBody()) {
            logger.error("queryOrderList error. resp={}", JSON.toJSONString(responseData));
            return null;
        }
        String body = responseData.getBody();
        JSONObject jsonObject = JSON.parseObject(body);
        return JSON.parseObject(jsonObject.getString("data"), new TypeReference<PageVO<OrderVO>>() {
        });
    }

    /**
     * 订单记录
     * 充值中台网站调用
     */
    public PageVO<OrderVO> history(String uid, int startTime, int endTime, int page, int pageSize) {
        OrderListDTO dto = new OrderListDTO();
        dto.setProject("youstar");
        dto.setUid(uid);
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        dto.setPage(page);
        dto.setPageSize(pageSize);
        return queryOrderList(dto);
    }
}
