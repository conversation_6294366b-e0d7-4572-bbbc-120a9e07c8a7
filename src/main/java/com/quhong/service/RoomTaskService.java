package com.quhong.service;

import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.UserExposureCountEvent;
import com.quhong.constant.MatchConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.dao.NoticeNewDao;
import com.quhong.mongo.dao.OfficialDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.NoticeNewData;
import com.quhong.mongo.data.OfficialData;
import com.quhong.monitor.MonitorSender;
import com.quhong.mq.MqSenderService;
import com.quhong.mysql.dao.HotSearchHistoryDao;
import com.quhong.mysql.dao.RoomMicDao;
import com.quhong.mysql.dao.UidAidDevoteLogDao;
import com.quhong.mysql.dao.GreetUserDao;

import com.quhong.mysql.data.RoomMicData;
import com.quhong.redis.DataRedisBean;
import com.quhong.redis.NewGreetRedis;
import com.quhong.room.redis.MicFrameRedis;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 房间相关进程
 *
 * <AUTHOR>
 * @date 2022/11/10
 */
@Service
public class RoomTaskService {

    private static final Logger logger = LoggerFactory.getLogger(RoomTaskService.class);

    private static final int ROOM_ALLOWANCE_ATYPE = 202;
    private static final String ROOM_ALLOWANCE_DESC = "room allowance";
    private static final String ROOM_ALLOWANCE_TITLE = "room allowance";

    @Resource
    private UidAidDevoteLogDao uidAidDevoteLogDao;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private ActorDao actorDao;
    @Resource
    private OfficialDao officialDao;
    @Resource
    private NoticeNewDao noticeNewDao;
    @Resource
    private MicFrameRedis micFrameRedis;
    @Resource
    private RoomMicDao roomMicDao;
    @Resource
    private MongoRoomDao roomDao;
    @Resource
    private HotSearchHistoryDao hotSearchHistoryDao;
    @Resource
    private GreetUserDao greetUserDao;

    @Resource
    private NewGreetRedis newGreetRedis;
    @Autowired(required = false)
    private EventReport eventReport;

    public void flushRecommendFcmUser() {
        if (micFrameRedis.getMaleMicUserCount() > 0) {
            return;
        }
        List<String> fcmRoomList = roomMicDao.getFcmRoomList();
        int maleMicUserCount = 0;
        int femaleMicUserCount = 0;
        if (CollectionUtils.isEmpty(fcmRoomList)) {
            return;
        }
        for (String roomId : fcmRoomList) {
            MongoRoomData roomData = roomDao.findData(roomId);
            if (roomData.getVideo_switch() == 1 || !StringUtils.isEmpty(roomData.getPwd())) {
                continue;
            }
            List<RoomMicData> roomMicDataList = roomMicDao.getRoomMicDataList(roomId);
            if (!CollectionUtils.isEmpty(roomMicDataList)) {
                for (RoomMicData roomMicData : roomMicDataList) {
                    if (roomMicData.getFbGender() == 1) {
                        micFrameRedis.addMaleMicUser(roomId, roomMicData.getUid());
                        maleMicUserCount++;
                    } else {
                        micFrameRedis.addFemaleMicUser(roomId, roomMicData.getUid());
                        femaleMicUserCount++;
                    }
                }
            }
            if (maleMicUserCount >= 10 && femaleMicUserCount >= 10) {
                break;
            }
        }
    }

    /**
     * 房间返钻石
     */
    public void roomAllowance() {
        int nowTime = DateHelper.getNowSeconds();
        if (MatchConstant.IS_ROOM_ALLOWANCE
                && nowTime>= MatchConstant.ROOM_ALLOWANCE_START + (int) TimeUnit.HOURS.toSeconds(20)) {
            // 注意，这个要在新的有完整一天数据后，在开启
            logger.info("new room allowance is open. old allowance return");
            return;
        }

        Set<String> uidSet = getAllRoomAllowanceUid();
        if (CollectionUtils.isEmpty(uidSet)) {
            logger.info("need room allowance uidSet is empty.");
            return;
        }

        List<String> errorList = new ArrayList<>();
        int allUserNum = 0;
        int allTotalBeans = 0;
        for (String uid : uidSet) {
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            if (actorData == null) {
                logger.error("can not find actor. uid={}", uid);
                continue;
            }
            String roomId = RoomUtils.formatRoomId(uid);
            // 获取昨日房间产生的钻石总量
            int totalBeans = (int) uidAidDevoteLogDao.calculateRoomDevote(roomId, nowTime - 24 * 60 * 60);
            int changed = (int) (totalBeans * 0.03);
            if (totalBeans < 100000 && changed != 0) {
                if (!increaseBeans(roomId, uid, changed)) {
                    errorList.add(uid);
                }
                sendAllowanceBeansNotice(actorData);
            } else if (totalBeans >= 100000) {
                changed = (int) (totalBeans * 0.05);
                if (!increaseBeans(roomId, uid, changed)) {
                    errorList.add(uid);
                }
                sendAllowanceBeansNotice(actorData);
            }
            if (changed != 0) {
                allUserNum++;
                allTotalBeans += changed;
            }
            logger.info("roomAllowance. uid={}, totalBeans={}, changed={}", uid, totalBeans, changed);
        }
        monitorSender.info("diamonds", "房间提成返钻", "返钻总人数=" + allUserNum + " ,返钻总钻石=" + allTotalBeans);
        if (!errorList.isEmpty()) {
            monitorSender.info("ustar_java_exception", "房间提成返钻告警", "房间提成存在返钻异常 error_list:" + Arrays.asList(errorList.toArray()));
        }
    }

    /**
     * 社交用户曝光数据上报
     */
    public void socialUserExposureReport(){
        try{
            String yesterdayStr = DateHelper.ARABIAN.getYesterdayStr(new Date());
            Map<String, Integer> exposureMap = newGreetRedis.getGreetUserExposureAll(yesterdayStr);
            for (Map.Entry<String, Integer> entry : exposureMap.entrySet()) {
                UserExposureCountEvent event = new UserExposureCountEvent();
                event.setUid(entry.getKey());
                event.setDate(yesterdayStr);
                event.setScene("Social-social");
                event.setScene_detail("");
                event.setExposure_times(entry.getValue());
                event.setCtime(DateHelper.getNowSeconds());
                eventReport.track(new EventDTO(event));
            }
            clearOldGreetUserData();
        }catch (Exception e){
            logger.error("socialUserExposureReport error:{}", e.getMessage(), e);
        }
    }

    /**
     * 删除t_greet_user表中mtime早于7天的记录
     */
    public void clearOldGreetUserData() {
        try {
            int cutoffTime = DateHelper.getNowSeconds() - 7 * 24 * 60 * 60;
            int deleted = greetUserDao.deleteOldGreetUser(cutoffTime);
            logger.info("clearOldGreetUserData success. deletedRecords={}", deleted);
        } catch (Exception e) {
            logger.error("clearOldGreetUserData error: {}", e.getMessage(), e);
        }
    }

    /**
     * 更新房间总贡献记录
     */
//    public void statisticsDevoteTotalToMongo() {
//        String key = RoomDevoteRedis.ROOM_BEANS_DEVOTE_TOTAL_KEY;
//        Map<String, Integer> roomBeansDevoteMap = roomDevoteRedis.getAllRoomBeansDevoteTotal(key);
//        if (CollectionUtils.isEmpty(roomBeansDevoteMap)) {
//            logger.info("need update room beans devote total data list is empty.");
//            return;
//        }
//        for (Map.Entry<String, Integer> entry : roomBeansDevoteMap.entrySet()) {
//            String roomId = entry.getKey();
//            int totalBeans = entry.getValue();
//            logger.info("increase room beans devote total. roomId={} totalBeans={}", roomId, totalBeans);
////            RoomBeansDevoteTotalData data = roomBeansDevoteTotalDao.findDate(roomId);
////            if (data == null) {
////                data = new RoomBeansDevoteTotalData();
////                data.setRoom_id(roomId);
////                data.setDevote(totalBeans);
////            } else {
////                data.setDevote(data.getDevote() + totalBeans);
////            }
////            roomBeansDevoteTotalDao.save(data);
//            roomDevoteRedis.delRoomBeansDevoteTotal(roomId, key);
//        }
//    }

    /**
     * 更新房间个人贡献记录
     */
//    public void statisticsDevoteOneToMongo() {
//        String key = RoomDevoteRedis.ROOM_BEANS_DEVOTE_ONE_KEY;
//        Map<String, Integer> roomBeansDevoteMap = roomDevoteRedis.getAllRoomBeansDevoteOne(key);
//        if (CollectionUtils.isEmpty(roomBeansDevoteMap)) {
//            logger.info("need update room beans devote total data list is empty.");
//            return;
//        }
//        for (Map.Entry<String, Integer> entry : roomBeansDevoteMap.entrySet()) {
//            try {
//                String[] split = entry.getKey().split("_");
//                String roomId = split[0];
//                String uid = split[1];
//                int totalBeans = entry.getValue();
//                logger.info("increase room beans devote one. uid={} roomId={} totalBeans={}", uid, roomId, totalBeans);
////                RoomBeansDevoteOneData data = roomBeansDevoteOneDao.findData(uid, roomId);
////                if (data == null) {
////                    data = new RoomBeansDevoteOneData();
////                    data.setUid(uid);
////                    data.setRoom_id(roomId);
////                    data.setDevote(totalBeans);
////                } else {
////                    data.setDevote(data.getDevote() + totalBeans);
////                }
////                roomBeansDevoteOneDao.save(data);
//                roomDevoteRedis.delRoomBeansDevoteOne(uid, roomId, key);
//            } catch (Exception e) {
//                logger.error("increase room beans devote error. key={} value={} {}", entry.getKey(), entry.getValue(), e.getMessage(), e);
//            }
//        }
//    }

    /**
     * 更新房间内会员的贡献值
     */
//    public void refreshRoomMemberCost() {
//        Map<String, Integer> roomMemberCostMap = roomDevoteRedis.getAllRoomMemberCost();
//        if (CollectionUtils.isEmpty(roomMemberCostMap)) {
//            logger.info("need update room member cost data list is empty.");
//            return;
//        }
//        for (Map.Entry<String, Integer> entry : roomMemberCostMap.entrySet()) {
//            try {
//                String[] split = entry.getKey().split("_");
//                String roomId = split[0];
//                String uid = split[1];
//                int totalBeans = entry.getValue();
//                logger.info("increase room member beans devote. roomId={} uid={} totalBeans={}", roomId, uid, totalBeans);
////                RoomMemberData data = memberDao.findData(roomId, uid);
////                if (data != null) {
////                    data.setDevote(data.getDevote() + totalBeans);
////                    memberDao.save(data);
////                }
//                roomDevoteRedis.delRoomMemberCost(roomId, uid);
//            } catch (Exception e) {
//                logger.error("increase room member beans devote error. key={} value={} {}", entry.getKey(), entry.getValue(), e.getMessage(), e);
//            }
//        }
//    }
    private Boolean increaseBeans(String roomId, String uid, int changed) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setRoomId(roomId);
        moneyDetailReq.setAtype(RoomTaskService.ROOM_ALLOWANCE_ATYPE);
        moneyDetailReq.setChanged(changed);
        moneyDetailReq.setTitle(RoomTaskService.ROOM_ALLOWANCE_TITLE);
        moneyDetailReq.setDesc(RoomTaskService.ROOM_ALLOWANCE_DESC);
        moneyDetailReq.setMtime(DateHelper.getNowSeconds());
        mqSenderService.asyncChargeDiamonds(moneyDetailReq);
        return true;
    }

    private void sendAllowanceBeansNotice(ActorData actor) {
        String title = actor.getSlang() == 1 ? "Diamonds commission from gifts sent in self room" : "نسبة الماسات من الهدايا المرسلة في غرفتك";
        String body = actor.getSlang() == 1 ? "Congratulations！You got the room diamonds commission from the gifts sent in your room today, please check your wallet."
                : "تهانينا! لقد حصلت على نسبة الماسات من الهدايا المرسلة في غرفتك اليوم، يرجى التحقق من محفظتك.";
        // 写入Official表
        OfficialData officialData = new OfficialData(title, body, actor.getUid());
        officialDao.save(officialData);
        // 写入NoticeNew表
        noticeNewDao.save(new NoticeNewData(actor.getUid(), officialData.get_id().toString()));
    }

    private Set<String> getAllRoomAllowanceUid() {
        Set<String> uidSet = new HashSet<>();
        try {
            Set<Object> keys = clusterRedis.opsForHash().keys(getAllowanceKey());
            for (Object key : keys) {
                uidSet.add(String.valueOf(key));
            }
        } catch (Exception e) {
            logger.error("get all room allowance uid error. {}", e.getMessage(), e);
        }
        return uidSet;
    }

    private String getAllowanceKey() {
        return "room_allowance_uid";
    }


    public void clearOldHotSearch() {
        hotSearchHistoryDao.clearOldData();
    }
}
