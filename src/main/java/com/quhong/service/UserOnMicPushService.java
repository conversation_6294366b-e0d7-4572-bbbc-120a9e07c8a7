package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.FcmPushEvent;
import com.quhong.constant.FcmMsgTypeConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.PushMsgData;
import com.quhong.data.dto.SendFcmDTO;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.LogType;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.FollowRoomDao;
import com.quhong.mongo.dao.FriendsDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.data.FollowRoomData;
import com.quhong.mongo.data.FriendsData;
import com.quhong.mongo.data.MongoActorData;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mysql.dao.DAUDao;
import com.quhong.redis.FcmMsgRedis;
import com.quhong.redis.NewRookieRoomRedis;
import com.quhong.redis.PlayerStatusRedis;
import com.quhong.redis.RoomMicRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.vo.RoomMicListVo;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class UserOnMicPushService implements MqMessageImpl{

    private static final Logger logger = LoggerFactory.getLogger(UserOnMicPushService.class);
    private static final Logger msgLogger = LoggerFactory.getLogger(LogType.MESSAGE_LOG);
    private static final Integer FEMALE_ON_MIC_TIME = 20;
    private static final String USER_NAME = "user_name";
    private static final List<PushMsgData> SAME_COUNTRY_PUSH_MSG_LIST = new ArrayList<>();

    static {
        // 推送消息1：我正在房间上麦🎙️
        SAME_COUNTRY_PUSH_MSG_LIST.add(new PushMsgData("I'm on the Mic in the room🎙️", "أنا على المايك في الغرفة 🎙️", "Join the chat now!", "انضم للدردشة الآن!"));
        // 推送消息2：📣 @% is speaking
        SAME_COUNTRY_PUSH_MSG_LIST.add(new PushMsgData("📣 user_name is speaking", "📣 user_name يتحدث", "Have any questions? Join the voice room now.", "هل لديك أي أسئلة؟ انضم إلى غرفة الصوت الآن."));
        // 推送消息3：👋进来！
        SAME_COUNTRY_PUSH_MSG_LIST.add(new PushMsgData("👋Come in!", "👋 تفضل بالدخول!", "Let's talk about things that make your heart race😂", "دعنا ندردش عن الأشياء التي تجعل قلبك ينبض بسرعة 😂"));
        // 推送消息4：你最喜欢的人@%上线了
        SAME_COUNTRY_PUSH_MSG_LIST.add(new PushMsgData("Your favorite person user_name is online", "شخصك المفضل user_name متصل الآن", "🗣️Join our Voice Room, don't miss out!", "🗣️ انضم إلى غرفتنا الصوتية، لا تفوتها!"));
        // 推送消息5：快来和我们聊天吧！
        SAME_COUNTRY_PUSH_MSG_LIST.add(new PushMsgData("Come and chat with us!", "تعال ودردش معنا!", "The room is so interesting😄...", "غرفة ممتعة جدًا 😄..."));
    }
    @Resource
    private FcmMsgRedis fcmMsgRedis;
    @Resource
    private ActorDao actorDao;
    @Resource
    private FriendsDao friendsDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private FCMPushService fcmPushService;
    @Resource
    private DAUDao dauDao;
    @Resource
    private PlayerStatusRedis playerStatusRedis;
    @Resource
    private NewRookieRoomRedis newRookieRoomRedis;
    @Resource
    private EventReport eventReport;
    @Resource
    private RoomMicRedis roomMicRedis;
    @Resource
    private FollowRoomDao followRoomDao;

    @Override
    public void process(CommonMqTopicData mqData) {
        if (mqData == null || StringUtils.isEmpty(mqData.getUid()) || StringUtils.isEmpty(mqData.getRoomId())) {
            return;
        }
        String uid = mqData.getUid();
        String roomId = mqData.getRoomId();
        String item = mqData.getItem();

        if (CommonMqTaskConstant.ON_MIC_TIME.equals(item)) {
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            // MongoRoomData roomData = mongoRoomDao.getDataFromCache(roomId);
            newFriendOnMicPush(uid, roomId, actorData);
            // femaleOnMicPush(uid, roomId, actorData, roomData);
        }

        if (CommonMqTaskConstant.USER_UP_MIC.equals(item)){
            roomMicOrderNumberPush(roomId);
        }
    }

    /**
     * 7天内新加的好友在房间上麦推送
     */
    private void newFriendOnMicPush(String uid, String roomId, ActorData actorData) {
        try {
            int pushFriendUser = fcmMsgRedis.getPushFriendUserStatus(uid);
            if(pushFriendUser > 0){
                return;
            }
            long millis = System.currentTimeMillis();
            List<FriendsData> friendsDataList = friendsDao.findAllFriend(uid, false);
            int currentTime = DateHelper.getNowSeconds();
            int lastSevenTime = currentTime - 7 * 86400;
            List<String> friendUidList = friendsDataList.stream().filter(item -> item.getCtime() >= lastSevenTime).map(friend -> uid.equals(friend.getUidSecond()) ? friend.getUidFirst() : friend.getUidSecond()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(friendUidList)){
                return;
            }
            fcmMsgRedis.setPushFriendUserStatus(uid, 4);
            SendFcmDTO sendFcmDTO = new SendFcmDTO();
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put(FcmMsgTypeConstant.FCM_MESSAGE_ID_KEY, new ObjectId().toString());
            paramMap.put(FcmMsgTypeConstant.FCM_ORIGIN_KEY, FcmMsgTypeConstant.FCM_SYSTEM);
            paramMap.put(FcmMsgTypeConstant.FCM_SUB_TYPE_KEY, FcmMsgTypeConstant.FCM_SUB_4);
            paramMap.put(FcmMsgTypeConstant.FCM_TITLE_KEY, actorData.getName());
            paramMap.put(FcmMsgTypeConstant.STEP_ACTION_TYPE_KEY, FcmMsgTypeConstant.VOICE_ROOM);
            JSONObject jsonObject2 = new JSONObject();
            jsonObject2.put(FcmMsgTypeConstant.ACTION_VALUE_KEY, roomId);
            paramMap.put(FcmMsgTypeConstant.ACTION_CONFIG_KEY, JSONObject.toJSONString(jsonObject2));

            sendFcmDTO.setParamMap(paramMap);
            sendFcmDTO.setToUidSet(new HashSet<>(friendUidList));
            sendFcmDTO.setTitle(String.format("Your friend %s is online!", actorData.getName()));
            sendFcmDTO.setTitleAr(String.format("صديقك %s اونلاين!", actorData.getName()));
            sendFcmDTO.setBody("Come and chat with us!");
            sendFcmDTO.setBodyAr("تعال ودردش معنا!");
            sendFcmDTO.setImg(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            fcmPushService.sendFcmMsgByUidSet(sendFcmDTO);
            msgLogger.info("newFriendOnMicPush timeMillis={}",  System.currentTimeMillis() - millis);
        }catch (Exception e){
            logger.error("newFriendOnMicPush error:{}", e.getMessage(), e);
        }
    }

    private void femaleOnMicPush(String uid, String roomId,  ActorData actorData, MongoRoomData roomData) {
        try {

            if (ActorUtils.getRegDays(uid) > 7){
                return;
            }

            if (actorData.getFb_gender() != 2){
                return;
            }

            long millis = System.currentTimeMillis();
            int afterOnMicTime = fcmMsgRedis.incFemaleOnMicTime(uid, 1);
            if(afterOnMicTime % FEMALE_ON_MIC_TIME != 0){
                return;
            }
            msgLogger.info("femaleOnMicPush uid={} roomId={} afterOnMicTime={}", uid, roomId, afterOnMicTime);
            Set<String> activeUserSet = dauDao.getActiveUserSetNoCache(15, 2000);
            if (CollectionUtils.isEmpty(activeUserSet)){
                return;
            }
            Set<String> pushTokenSet = new HashSet<>();
            String fcmMessageId = new ObjectId().toString();
            PushMsgData pushMsg = SAME_COUNTRY_PUSH_MSG_LIST.get(new Random().nextInt(SAME_COUNTRY_PUSH_MSG_LIST.size()));
            List<MongoActorData>  actorDataList = actorDao.getActors(activeUserSet);
            for (MongoActorData aidActorData : actorDataList) {
                String aid = aidActorData.get_id().toString();
                // if(playerStatusRedis.getPlayerStatus(aid) > 0){
                //     continue;
                // }

                if (null == aidActorData.getPush() ){
                    continue;
                }

                String pushToken = (String) aidActorData.getPush().get("token");
                if(pushToken == null){
                    continue;
                }

                if(fcmMsgRedis.getPushMaleSameCountryUserStatus(aid) > 0){
                    continue;
                }
                fcmMsgRedis.setPushMaleSameCountryUserStatus(aid, 8*3600);
                pushTokenSet.add(pushToken);
                doFcmReportEvent(fcmMessageId, aid, FcmMsgTypeConstant.FCM_SYSTEM, roomData.getName(), FcmMsgTypeConstant.FCM_SUB_5);
            }

            if (CollectionUtils.isEmpty(pushTokenSet)){
                return;
            }

            SendFcmDTO sendFcmDTO = new SendFcmDTO();
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put(FcmMsgTypeConstant.FCM_MESSAGE_ID_KEY, fcmMessageId);
            paramMap.put(FcmMsgTypeConstant.FCM_ORIGIN_KEY, FcmMsgTypeConstant.FCM_SYSTEM);
            paramMap.put(FcmMsgTypeConstant.FCM_SUB_TYPE_KEY, FcmMsgTypeConstant.FCM_SUB_5);
            paramMap.put(FcmMsgTypeConstant.FCM_TITLE_KEY, roomData.getName());
            paramMap.put(FcmMsgTypeConstant.STEP_ACTION_TYPE_KEY, FcmMsgTypeConstant.VOICE_ROOM);
            JSONObject jsonObject2 = new JSONObject();
            jsonObject2.put(FcmMsgTypeConstant.ACTION_VALUE_KEY, roomId);
            paramMap.put(FcmMsgTypeConstant.ACTION_CONFIG_KEY, JSONObject.toJSONString(jsonObject2));

            sendFcmDTO.setParamMap(paramMap);
            sendFcmDTO.setToTokenSet(pushTokenSet);
            sendFcmDTO.setTitle(pushMsg.getTitleEn().contains(USER_NAME) ? pushMsg.getTitleEn().replace(USER_NAME, actorData.getName()) : pushMsg.getTitleEn());
            sendFcmDTO.setTitleAr(pushMsg.getTitleAr().contains(USER_NAME) ? pushMsg.getTitleAr().replace(USER_NAME, actorData.getName()) : pushMsg.getTitleAr());
            sendFcmDTO.setBody(pushMsg.getDescriptionEn());
            sendFcmDTO.setBodyAr(pushMsg.getDescriptionAr());
            sendFcmDTO.setImg(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            fcmPushService.sendFcmMsgByToken(sendFcmDTO);
            msgLogger.info("femaleOnMicPush timeMillis={}",  System.currentTimeMillis() - millis);
        }catch (Exception e){
            logger.error("femaleOnMicPush error:{}", e.getMessage(), e);
        }
    }

    /**
     * 房间上麦人数达到4个人推送
     */
    private void roomMicOrderNumberPush(String roomId){
        int pushStatus = fcmMsgRedis.getPushFollowRoomStatus(roomId);
        if (pushStatus > 0){
            return;
        }
        RoomMicListVo roomMicVO = roomMicRedis.getRoomMicFromRedis(roomId);
        if (roomMicVO == null || CollectionUtils.isEmpty(roomMicVO.getList())){
            return;
        }
        long onMicUserNum = roomMicVO.getList().stream().filter(item -> item.getStatus() == 1).count();
        if (onMicUserNum < 4) {
            return;
        }

        fcmMsgRedis.setPushFollowRoomStatus(roomId, 12);
        MongoRoomData roomData = mongoRoomDao.getDataFromCache(roomId);
        onMicUserNumPush(roomId, roomData);
    }

    public void onMicUserNumPush(String roomId, MongoRoomData roomData) {
        SendFcmDTO sendFcmDTO = new SendFcmDTO();
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put(FcmMsgTypeConstant.FCM_MESSAGE_ID_KEY, new ObjectId().toString());
        paramMap.put(FcmMsgTypeConstant.FCM_ORIGIN_KEY, FcmMsgTypeConstant.FCM_SYSTEM);
        paramMap.put(FcmMsgTypeConstant.FCM_SUB_TYPE_KEY, FcmMsgTypeConstant.FCM_SUB_1);
        paramMap.put(FcmMsgTypeConstant.FCM_TITLE_KEY, roomData.getName());
        paramMap.put(FcmMsgTypeConstant.STEP_ACTION_TYPE_KEY, FcmMsgTypeConstant.VOICE_ROOM);
        JSONObject jsonObject2 = new JSONObject();
        jsonObject2.put(FcmMsgTypeConstant.ACTION_VALUE_KEY, roomId);
        paramMap.put(FcmMsgTypeConstant.ACTION_CONFIG_KEY, JSONObject.toJSONString(jsonObject2));
        sendFcmDTO.setParamMap(paramMap);
        sendFcmDTO.setToUidSet(new HashSet<>(followRoomDao.selectPageByRoomId(roomId, 0, 100).stream().map(FollowRoomData::getUid).collect(Collectors.toList())));
        sendFcmDTO.setTitle(roomData.getName());
        sendFcmDTO.setTitleAr(roomData.getName());
        sendFcmDTO.setBody("The room you are following is discussing an interesting topic. Have a look!");
        sendFcmDTO.setBodyAr("الغرفة التي تتابعها تدردش موضوعًا شيقًا. ألقي نظرة!");
        sendFcmDTO.setImg(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
        fcmPushService.sendFcmMsgByUidSet(sendFcmDTO);
    }



    private void doFcmReportEvent(String fcmMessageId, String uid, String fcmType, String fcmTitle, String fcmSubType) {
        FcmPushEvent event = new FcmPushEvent();
        event.setCtime(DateHelper.getNowSeconds());
        event.setUid(uid);
        event.setFcm_message_id(fcmMessageId);
        event.setFcm_type(fcmType);
        event.setFcm_title(fcmTitle);
        event.setFcm_subtype(fcmSubType);
        event.setFcm_push_status(1);
        event.setFcm_action(1001);
        eventReport.track(new EventDTO(event));
    }

}
