package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.VipActivateEvent;
import com.quhong.constant.VipFeatureConstant;
import com.quhong.constant.VipHttpCode;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.bo.VipV2BO;
import com.quhong.data.dto.RoomEventDTO;
import com.quhong.data.dto.VipCardActivateDTO;
import com.quhong.data.dto.VipDTO;
import com.quhong.data.vo.*;
import com.quhong.dto.VipV2BuyDTO;
import com.quhong.dto.VipV2ChargeDTO;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.HttpCode;
import com.quhong.enums.RoomConstant;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonException;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IRoomService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.ResourceConfigData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mongo.data.RoomMemberData;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.msg.push.CancelAdminPushMsg;
import com.quhong.msg.room.VipActivationMsg;
import com.quhong.msg.room.VipActivationPopupMessage;
import com.quhong.msg.room.VipCardNumMsg;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.*;
import com.quhong.redis.IdentifyRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
public class VipV2Service {

    private static final Logger logger = LoggerFactory.getLogger(VipV2Service.class);

    private static final List<Integer> MALE_VIP_ORDER_LIST = Arrays.asList(1, 2, 3, 4, 5, 6, 10);
    private static final List<Integer> FAMALE_VIP_ORDER_LIST = Arrays.asList(1, 2, 3, 4, 10, 5, 6);
    private static final List<String> SWITCH_VIP_VERSION_ICON_LIST = Arrays.asList("https://cdn3.qmovies.tv/youstar/op_1755597748_old_switch.png", "https://cdn3.qmovies.tv/youstar/op_1755597748_new_switch.png");
    public static final List<Integer> VIP_LEVEL_JUDGE_LIST = Arrays.asList(VipFeatureConstant.VIP_LEVEL_5, VipFeatureConstant.VIP_LEVEL_6, VipFeatureConstant.VIP_LEVEL_QUEEN);
    private static final int BUY_VIP_MONEY_TYPE = 100;
    public static final int PAGE_SIZE = 20;
    private static final int VIP_GIVE_MONEY_TYPE = 104;
    private static final int VIP_GIFT_GIVE_VIP_CARD_DAY = 30;
    private static final String BUY_VIP_TITLE = "Charge Vip";
    public static final String OLD_VIP_RESOURCE_KEY = "oldVipResource";
    public static final String LOW_BUY_VIP_TIPS_EN = "You've already opened %s, so you can't buy a lower-level VIP";
    public static final String LOW_BUY_VIP_TIPS_AR = "لا يمكنك شراء VIP من مستوى أدنى لأنك فتحت مستوى %s";
    public static final String BUY_QUEEN_VIP_TIPS_EN = "Exclusive privilege for female users";
    public static final String BUY_QUEEN_VIP_TIPS_AR = "امتياز حصري للمستخدمين الإناث";
    private static final String VIP_RULE_URL = ServerConfig.isProduct() ? "https://static.youstar.live/vip_rules/?fullScreen=1&webVersion=12" : "https://test2.qmovies.tv/vip_rules/?fullScreen=1";
    private static final String VIP_RECORD_URL = ServerConfig.isProduct() ? "https://static.youstar.live/vip_record/?fullScreen=1&webVersion=23" : "https://test2.qmovies.tv/vip_record/?fullScreen=1&webVersion=23";
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private VipRecordDao vipRecordDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private VipConfigDao vipConfigDao;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private ActorConfigDao actorConfigDao;
    @Resource
    private UserResourceDao userResourceDao;
    @Resource
    private ResourceConfigDao resourceConfigDao;
    @Resource
    private IdentifyRedis identifyRedis;
    @Resource
    private RoomMemberDao roomMemberDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private IRoomService iRoomService;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private ActorCommonService actorCommonService;


    /**
     * 填充用户VIP基本信息
     */
    private void fillUserVipInfo(String uid, VipV2InfoVO vo) {
        // 填充性别
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        vo.setGender(actorData.getFb_gender() == 1 ? 1 : 2);
        vo.setVipVersion(actorCommonService.getVipVersion(uid));
        vo.setVipVersionSwitchList(SWITCH_VIP_VERSION_ICON_LIST);

        // 填充vip信息
        int currentTime = DateHelper.getNowSeconds();
        VipUserInfoData vipInfo = vipInfoDao.findVipInfoByUid(uid);
        if (vipInfo != null) {
            vo.setVipLevel(0);
            int vipEndTime = (int) (vipInfo.getVipEndTime().getTime() / 1000);
            int leftTime = vipEndTime - currentTime;
            if (leftTime <= 0) {
                vo.setVipLevel(0);
            }else {
                vo.setVipLevel(vipInfo.getVipLevel());
                vo.setExpireTime(vipEndTime);
            }
        }
    }


    /**
     * 填充VIP配置信息列表
     */
    private void fillVipConfigList(VipV2InfoVO vo, VipDTO dto) {
        int slang = dto.getSlang();
        // 获取所有VIP配置
        List<VipConfigData> configList = vipConfigDao.selectList();
        if (CollectionUtils.isEmpty(configList)) {
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        int vipVersion = actorCommonService.getVipVersion(dto.getUid());
        // 根据用户性别排序VIP等级
        List<Integer> orderList = vo.getGender() == 1 ? MALE_VIP_ORDER_LIST : FAMALE_VIP_ORDER_LIST;

        // 使用Java 8 Stream API创建VipLevel到VipConfigData的映射并排序
        Map<Integer, VipConfigData> configMap = configList.stream().collect(Collectors.toMap(VipConfigData::getVipLevel, Function.identity()));

        // 按照性别对应的顺序排序
        List<VipV2Item> itemList = new ArrayList<>();
        int currentVipLevel = vo.getVipLevel();
        int userGender = vo.getGender(); // 1:男 2:女
        for (Integer level : orderList) {
            VipConfigData config = configMap.get(level);
            if (config == null) {
                continue;
            }

            int targetLevel = config.getVipLevel();
            VipV2Item item = new VipV2Item();
            item.setVipLevel(targetLevel);
            item.setPrice(config.getPrice());
            item.setValidDay(config.getValidDay());
            item.setVipMedal(vipVersion == VipFeatureConstant.VIP_VERSION_0 && !StringUtils.isEmpty(config.getVipOldModel()) ? config.getVipOldModel() : config.getVipModel());

            // 根据规则设置是否可购买与提示
            // 1、不管男女，当前vip等级 <= 6，禁止购买比当前更低的等级
            // 2、不管男女，当前是女王VIP，禁止购买低于VIP5的等级（1-4）
            // 3、男性不可购买女王VIP
            int canBuy = 1;
            String tips = "";
            if (currentVipLevel > 0 && currentVipLevel <= VipFeatureConstant.VIP_LEVEL_6 && targetLevel < currentVipLevel) {
                canBuy = 0;
                tips = slang == SLangType.ARABIC ? String.format(LOW_BUY_VIP_TIPS_AR, "VIP" + currentVipLevel) : String.format(LOW_BUY_VIP_TIPS_EN, "VIP" + currentVipLevel);
            }
            if (currentVipLevel == VipFeatureConstant.VIP_LEVEL_QUEEN && targetLevel <= VipFeatureConstant.VIP_LEVEL_4) {
                canBuy = 0;
                tips = slang == SLangType.ARABIC ? String.format(LOW_BUY_VIP_TIPS_AR, "الملكة") : String.format(LOW_BUY_VIP_TIPS_EN, "Queen");
            }
            if (userGender == 1 && targetLevel == VipFeatureConstant.VIP_LEVEL_QUEEN) {
                canBuy = 0;
                tips = slang == SLangType.ARABIC ? BUY_QUEEN_VIP_TIPS_AR : BUY_QUEEN_VIP_TIPS_EN;
            }
            item.setCanBuy(canBuy);
            item.setTips(canBuy == 0 ? tips : "");
            // 解析特权信息
            List<VipV2Privilege> privilegeList = parsePrivilege(config.getPrivilege(), slang, vipVersion);
            item.setPrivilegeList(privilegeList);
            itemList.add(item);
        }

        // 设置到VO对象
        vo.setVipItemList(itemList);
        vo.setRuleUrl(VIP_RULE_URL);
        vo.setRecordUrl(VIP_RECORD_URL);
        long vipCardCount = actorConfigDao.getLongUserConfig(dto.getUid(), ActorConfigDao.VIP_CARD_COUNT, 0);
        vo.setVipCardUnread((int) vipCardCount);
    }

    /**
     * 解析特权信息JSON字符串
     *
     * @param privilegeJson 特权JSON字符串
     * @param slang         语言类型，1为英语，2为阿语
     * @return 特权列表
     */
    private List<VipV2Privilege> parsePrivilege(String privilegeJson, int slang, int vipVersion) {
        if (StringUtils.isEmpty(privilegeJson)) {
            return Collections.emptyList();
        }

        try {
            // 解析JSON为VipConfigMeta列表
            List<VipConfigMeta> metaList = JSON.parseArray(privilegeJson, VipConfigMeta.class);
            if (CollectionUtils.isEmpty(metaList)) {
                return new ArrayList<>();
            }
            // 转换为VipV2Privilege列表
            // 根据用户语言设置选择对应语言的标题和描述
            return metaList.stream().map(meta -> {
                VipV2Privilege privilege = new VipV2Privilege();
                BeanUtils.copyProperties(meta, privilege);
                // 根据用户语言设置选择对应语言的标题和描述
                privilege.setIcon(vipVersion == VipFeatureConstant.VIP_VERSION_0 && !StringUtils.isEmpty(meta.getIconOld()) ? meta.getIconOld() : meta.getIcon());
                privilege.setPreview(vipVersion == VipFeatureConstant.VIP_VERSION_0 && !StringUtils.isEmpty(meta.getIconOld()) ? meta.getIconOld() : meta.getPreview());
                privilege.setTitle(slang == SLangType.ARABIC ? meta.getTitleAr() : meta.getTitleEn());
                privilege.setDescription(slang == SLangType.ARABIC ? meta.getDescriptionAr() : meta.getDescriptionEn());
                return privilege;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("parsePrivilege error: {}", privilegeJson, e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取VIP信息
     */
    public VipV2InfoVO vipInfo(VipDTO dto) {
        String uid = dto.getUid();
        VipV2InfoVO vo = new VipV2InfoVO();
        fillUserVipInfo(uid, vo);
        fillVipConfigList(vo, dto);
        return vo;
    }

    /**
     * 校验是否允许购买该VIP等级（仅校验降级问题）
     *
     * @param buyVipLevel 要购买的VIP等级
     * @param vipInfo     用户当前VIP信息
     * @return currentVipLevel
     */
    private VipV2BO validateVipDowngrade(int buyVipLevel, VipUserInfoData vipInfo, ActorData actorData) {
        int userGender = actorData.getFb_gender(); // 1:男 2:女
        VipV2BO bo = new VipV2BO();
        bo.setVipSource(VipFeatureConstant.SOURCE_BUY_VIP);
        bo.setResourceDescription(VipFeatureConstant.DESCRIPTION_BUY_VIP);
        if (vipInfo == null) {
            return bo; // 没有VIP信息，不需要校验降级
        }
        int currentTime = DateHelper.getNowSeconds();
        int vipEndTime = (int) (vipInfo.getVipEndTime().getTime() / 1000);
        int leftTime = vipEndTime - currentTime;
        int currentVipLevel = leftTime > 0 ? vipInfo.getVipLevel() : 0;
        // 规则：
        // - 当前VIP有效时：
        //   * 若当前等级 ∈ {5,6,10}（VIP5/VIP6/女王），允许在 {5,6,10} 之间互相购买, VIP6不可以购买VIP5；禁止购买 ≤4 的等级
        //   * 若当前等级 ≤4，禁止购买比当前更低的等级（不允许降级）
        if (leftTime > 0) {
            if (currentVipLevel <= VipFeatureConstant.VIP_LEVEL_6 && buyVipLevel < currentVipLevel) {
                throw new CommonException(VipHttpCode.VIP_DOWN_GRADE, currentVipLevel);
            }

            if (currentVipLevel == VipFeatureConstant.VIP_LEVEL_QUEEN && buyVipLevel <= VipFeatureConstant.VIP_LEVEL_4) {
                throw new CommonException(VipHttpCode.VIP_DOWN_GRADE, currentVipLevel);
            }

            if (userGender == 1 && buyVipLevel == VipFeatureConstant.VIP_LEVEL_QUEEN) {
                throw new CommonException(VipHttpCode.VIP_DOWN_GRADE.getCode(), BUY_QUEEN_VIP_TIPS_EN);
            }
        }
        bo.setVipEndTime(vipEndTime);
        bo.setVipLevel(currentVipLevel);
        return bo;
    }

    /**
     * 验证VIP购买参数（不包括降级校验）
     *
     * @param uid         用户ID
     * @param buyVipLevel 要购买的VIP等级
     * @return VIP配置信息
     */
    private VipConfigData validateVipPurchaseParams(String uid, int buyVipLevel) {
        // 获取用户信息进行性别校验
        ActorData actorData = actorDao.getActorData(uid);
        if (actorData == null) {
            throw new CommonException(VipHttpCode.USER_NOT_EXIST);
        }

        // 性别校验：男性不可购买Queen VIP
        int userGender = actorData.getFb_gender(); // 1: 男性  2: 女性
        if (userGender == 1 && buyVipLevel == VipFeatureConstant.VIP_LEVEL_QUEEN) {
            // 男性不可购买Queen VIP
            throw new CommonException(VipHttpCode.MALE_NOT_BUY_VIP);
        }
        Map<Integer, VipConfigData> vipLevelConfigMap = vipConfigDao.selectList().stream().collect(Collectors.toMap(VipConfigData::getVipLevel, Function.identity()));
        VipConfigData vipConfig = vipLevelConfigMap.get(buyVipLevel);
        if (vipConfig == null) {
            throw new CommonException(VipHttpCode.NOT_FIND_VIP_CONFIG);
        }
        return vipConfig;
    }

    /**
     * 处理VIP扣费
     *
     * @param userId      用户ID
     * @param buyVipLevel 购买的VIP等级
     * @param costMoney   费用
     */
    private void processVipPayment(String userId, int buyVipLevel, int costMoney) {
        String recordDesc = buyVipLevel == VipFeatureConstant.VIP_LEVEL_QUEEN ? "charge queen vip" : "charge vip" + buyVipLevel;
        distributionService.deductDiamondsReward(userId, BUY_VIP_MONEY_TYPE, costMoney, BUY_VIP_TITLE, recordDesc);
        logger.info("VIP payment processed for user: {}, level: {}, cost: {}", userId, buyVipLevel, costMoney);
    }

    /**
     * 插入VIP记录
     *
     * @param uid         用户ID
     * @param vipLevel    VIP等级
     * @param vipDay      VIP天数
     * @param recordType  记录类型 0:购买 1:vipCard激活 2:到期结束 3: admin移除vip
     * @param currentTime 当前时间
     */
    public void insertVipRecord(String uid, int vipLevel, int vipDay, int recordType, int currentTime) {
        try {
            VipRecordData vipRecord = new VipRecordData();
            vipRecord.setUid(uid);
            vipRecord.setVipLevel(vipLevel);
            vipRecord.setVipDay(vipDay);
            vipRecord.setRecordType(recordType);
            vipRecord.setCtime(currentTime);
            vipRecord.setMtime(currentTime);
            vipRecordDao.insert(vipRecord);
            logger.info("VIP record inserted - uid: {}, vipLevel: {}, vipDay: {}, recordType: {}", uid, vipLevel, vipDay, recordType);
        } catch (Exception e) {
            logger.error("Failed to insert VIP record - uid: {}, vipLevel: {}, vipDay: {}, recordType: {}", uid, vipLevel, vipDay, recordType, e);
        }
    }

    /**
     * 发放VIP奖励
     *
     * @param uid         用户ID
     * @param buyVipLevel 购买的VIP等级
     * @param vipConfig   vip配置
     */
    private void distributeVipRewards(String uid, VipV2BO vipV2BO, int buyVipLevel, VipConfigData vipConfig) {
        int currentVipLevel = vipV2BO.getVipLevel();
        int vipSource = vipV2BO.getVipSource();
        String description = vipV2BO.getResourceDescription();
        try {
            // 升级vip将老等级的vip资源删除,
            if (currentVipLevel > 0 && currentVipLevel != buyVipLevel) {
                // 删除老等级的vip资源
                handleVipChangeRemoveResource(uid, currentVipLevel, description, vipV2BO.getVipEndTime());
            }
            // 下发新等级vip资源
            releaseVipResources(uid, buyVipLevel, vipConfig, vipV2BO);
            // 处理vip特权相关
            releaseVipPrivileges(uid, vipConfig);
            logger.info("VIP rewards distributed to uid: {}, level: {}", uid, buyVipLevel);
        } catch (Exception e) {
            logger.error("distributeVipRewards error. uid={} buyVipLevel={} vipConfig={} vipSource={}", uid, buyVipLevel, vipConfig, vipSource);
        }
    }

    /**
     * 处理vip特权相关
     *
     * @param uid             用户ID
     * @param vipConfig       VIP配置
     */
    private void releaseVipPrivileges(String uid, VipConfigData vipConfig) {
        // 解析特权信息
        List<VipV2Privilege> privilegeList = parsePrivilege(vipConfig.getPrivilege(), SLangType.ENGLISH, VipFeatureConstant.VIP_VERSION_1);
        for (VipV2Privilege privilege : privilegeList) {
            // 处理每个特权
            if (privilege.getType() == VipFeatureConstant.FEATURE_HIDE_VISITOR_IDENTITY) {
                actorDao.updateGeneralConf(uid, "invisible", 1);
            }
        }
    }

    /**
     * 下发vip等级的vip资源
     */
    private void releaseVipResources(String uid, int buyVipLevel, VipConfigData vipConfig, VipV2BO vipV2BO) {

        String resourceKey = vipConfig.getResourceKey();
        int validDay = vipConfig.getValidDay();
        ResourceKeyConfigData vipResourceConfigData = resourceKeyHandlerService.getConfigData(resourceKey);
        if (vipResourceConfigData == null) {
            logger.error("can not find vipResourceConfigData. resourceKey={}", resourceKey);
            return;
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        int vipSource = vipV2BO.getVipSource();
        String title = vipV2BO.getResourceDescription();
        String recordDesc = buyVipLevel == VipFeatureConstant.VIP_LEVEL_QUEEN ? title + "Queen" : title + buyVipLevel;
        for (ResourceKeyConfigData.ResourceMeta resMeta : vipResourceConfigData.getResourceMetaList()) {
            // 创建ResourceMeta副本避免修改缓存数据
            ResourceKeyConfigData.ResourceMeta resourceMeta = new ResourceKeyConfigData.ResourceMeta();
            BeanUtils.copyProperties(resMeta, resourceMeta);
            // 重新设置资源有效期
            resourceMeta.setResourceTime(validDay);
            if (vipSource == VipFeatureConstant.SOURCE_VIP_CARD && (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_DIAMOND || resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_BAG_GIFT)) {
                continue;
            }

            if (vipSource == VipFeatureConstant.SOURCE_UPGRADE_APP && (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_DIAMOND || resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_BAG_GIFT)) {
                continue;
            }

            if (vipSource == VipFeatureConstant.SOURCE_VIP_CHANGE_GENDER && (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_DIAMOND || resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_BAG_GIFT)) {
                continue;
            }

            if (vipSource == VipFeatureConstant.SOURCE_ADMIN_GIVE_VIP && vipV2BO.getFreeDiamond() <= 0 && resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_DIAMOND) {
                continue;
            }

            // 升级app版本不下发房间锁
            if (vipSource == VipFeatureConstant.SOURCE_UPGRADE_APP && resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_ROOM_LOCK) {
                continue;
            }
            resourceMeta.setAutoWare(0);
            resourceKeyHandlerService.sendOneResourceData(actorData, resourceMeta, VIP_GIVE_MONEY_TYPE, title, title, recordDesc, "", "", 6);
        }
    }

    /**
     * 发送VIP激活广播消息及弹窗
     *
     * @param actorData 用户ID
     * @param vipConfig VIP配置信息
     */
    private void sendVipActivationMsg(ActorData actorData, VipConfigData vipConfig, VipV2BuyVO vo) {
        String uid = actorData.getUid();
        try {
            // 发送VIP激活弹窗消息
            sendVipActivationPopupMsg(actorData, vipConfig, vo);

            // 判断是否为测试白名单用户，测试白名单用户不发送广播
            if (whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID)) {
                logger.info("User {} is in test whitelist, skip VIP activation broadcast", uid);
                return;
            }
            if (ObjectUtils.isEmpty(vipConfig.getVipMsgBackground())) {
                return;
            }

            // 构建VIP激活消息
            VipActivationMsg msg = new VipActivationMsg();
            msg.setUid(uid);
            msg.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            msg.setVipLevel(vipConfig.getVipLevel());
            // 设置VIP头像框和勋章
            msg.setVipFrame(vipConfig.getVipFrame());
            msg.setVipModel(vipConfig.getVipModel());
            msg.setVipMsgBackground(vipConfig.getVipMsgBackground());
            // 构建激活文案
            String userNickname = actorData.getName();
            String activationTextEn = String.format("Congratulations %s for activating %s", userNickname, vipConfig.getNameEn());
            String activationTextAr = String.format("تهانينا %s لتفعيل %s", userNickname, vipConfig.getNameAr());
            msg.setActivationTextEn(activationTextEn);
            msg.setActivationTextAr(activationTextAr);
            List<HighlightTextObject> list = getHighlightTextObjects(vipConfig, userNickname);
            msg.setHighlightTextEn(list);
            msg.setHighlightTextAr(list);
            // 发送全局广播消息
            roomWebSender.sendRoomWebMsg("all", "", msg, false);
            logger.info("VIP activation broadcast sent for user: {}, vipLevel: {}", uid, vipConfig.getVipLevel());
        } catch (Exception e) {
            logger.error("Failed to send VIP activation broadcast for user: {}, vipLevel: {}", uid, vipConfig.getVipLevel(), e);
        }
    }

    private static List<HighlightTextObject> getHighlightTextObjects(VipConfigData vipConfig, String userNickname) {
        List<HighlightTextObject> list = new ArrayList<>();
        HighlightTextObject object = new HighlightTextObject();
        object.setText(userNickname);
        object.setHighlightColor("#FFE200");
        list.add(object);
        object = new HighlightTextObject();
        object.setText(vipConfig.getNameEn());
        object.setHighlightColor("#FFE200");
        list.add(object);
        object = new HighlightTextObject();
        object.setText(vipConfig.getNameAr());
        object.setHighlightColor("#FFE200");
        list.add(object);
        return list;
    }

    /**
     * 购买VIP
     *
     * @param dto 购买请求参数
     * @return 购买结果
     */
    public VipV2BuyVO vipBuy(VipV2BuyDTO dto) {
        int buyVipLevel = dto.getVipLevel();
        String uid = dto.getUid();
        int currentTime = DateHelper.getNowSeconds();
        VipV2BuyVO vo = new VipV2BuyVO();
        vo.setNow(currentTime);
        // 获取用户当前VIP信息
        VipUserInfoData vipInfo = vipInfoDao.findVipInfoByUid(uid);
        ActorData actorData = actorDao.getActorData(uid);
        // 先校验是否允许降级
        VipV2BO vipV2BO = validateVipDowngrade(buyVipLevel, vipInfo, actorData);
        // 验证VIP购买参数（性别校验等，不包括降级校验）
        VipConfigData vipConfig = validateVipPurchaseParams(uid, buyVipLevel);
        // 处理VIP扣费
        processVipPayment(uid, buyVipLevel, vipConfig.getPrice());
        // 更新VIP信息
        activateVipAndUpdateInfo(uid, buyVipLevel, vipConfig, vipInfo, vo, vipV2BO.getVipSource());
        // 发放VIP奖励
        distributeVipRewards(uid, vipV2BO, buyVipLevel, vipConfig);
        //  插入VIP记录（记录类型为1表示VIP卡激活）
        insertVipRecord(uid, buyVipLevel, vipConfig.getValidDay(), VipFeatureConstant.RECORD_BUY_VIP, currentTime);
        // 发送VIP激活广播消息
        sendVipActivationMsg(actorData, vipConfig, vo);
        // 事件埋点
        doVipActiveReport(uid, buyVipLevel, VipFeatureConstant.SOURCE_BUY_VIP, vipConfig.getValidDay(), vipConfig.getPrice(), 0);
        logger.info("VIP purchase completed for uid: {}, level: {}", uid, buyVipLevel);
        return vo;
    }

    /**
     * admin赠送VIP
     * @param dto 请求参数
     * @return 结果
     */
    public VipV2BuyVO vipCharge(VipV2ChargeDTO dto) {
        int chargeVipLevel = dto.getVipLevel();
        String uid = dto.getUid();
        int currentTime = DateHelper.getNowSeconds();
        VipV2BuyVO vo = new VipV2BuyVO();
        vo.setNow(currentTime);
        // 获取用户当前VIP信息
        VipUserInfoData vipInfo = vipInfoDao.findVipInfoByUid(uid);
        ActorData actorData = actorDao.getActorData(uid);
        // 先校验是否允许降级
        VipV2BO vipV2BO = validateVipDowngrade(chargeVipLevel, vipInfo, actorData);
        vipV2BO.setVipSource(VipFeatureConstant.SOURCE_ADMIN_GIVE_VIP);
        vipV2BO.setResourceDescription(VipFeatureConstant.DESCRIPTION_ADMIN_GIVE_VIP);
        vipV2BO.setFreeDiamond(dto.getFreeDiamond());
        // 验证VIP购买参数（性别校验等，不包括降级校验）
        VipConfigData vipConfig = validateVipPurchaseParams(uid, chargeVipLevel);
        // 更新VIP信息
        activateVipAndUpdateInfo(uid, chargeVipLevel, vipConfig, vipInfo, vo, vipV2BO.getVipSource());
        // 发放VIP奖励
        distributeVipRewards(uid, vipV2BO, chargeVipLevel, vipConfig);
        // 事件埋点
        doVipActiveReport(uid, chargeVipLevel, VipFeatureConstant.SOURCE_ADMIN_GIVE_VIP, vipConfig.getValidDay(), vipConfig.getPrice(), 0);
        logger.info("vipCharge completed for uid: {}, level: {}", uid, chargeVipLevel);
        return vo;
    }

    /**
     * 获取VIP卡列表
     *
     * @param dto 请求参数
     * @return VIP卡列表
     */
    public VipCardListVO getVipCardList(VipDTO dto) {
        String uid = dto.getUid();
        int page = dto.getPage() != null ? dto.getPage() : 1;
        if (ObjectUtils.isEmpty(uid)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        VipCardListVO vo = new VipCardListVO();
        int currentTime = DateHelper.getNowSeconds();
        // 只有在获取第一页时才设置vipCardCount为0并推送VipCardNumMsg
        if (page == 1) {
            sendVipCardNumMsg(uid);
        }
        // 分页查询用户的VIP卡记录
        int start = (page - 1) * PAGE_SIZE;
        List<UserResourceData> vipCardList = userResourceDao.selectPage(uid, BaseDataResourcesConstant.TYPE_VIP_CARD, start, PAGE_SIZE);
        if (CollectionUtils.isEmpty(vipCardList)) {
            vo.setList(Collections.emptyList());
            vo.setNextUrl("");
            return vo;
        }

        // 获取VIP卡配置信息映射
        List<ResourceConfigData> resConfigDataList = resourceConfigDao.getResourceAllListFromDb(BaseDataResourcesConstant.TYPE_VIP_CARD);
        Map<Integer, ResourceConfigData> resConfigDataMap = resConfigDataList.stream().collect(Collectors.toMap(ResourceConfigData::getResourceId, Function.identity()));
        // 转换为VIP卡项目列表
        List<VipCardItemVO> itemList = new ArrayList<>();
        for (UserResourceData vipCard : vipCardList) {
            VipCardItemVO item = convertToVipCardItem(vipCard, resConfigDataMap, dto, currentTime);
            if (item == null) {
                continue;
            }
            itemList.add(item);
        }
        vo.setList(itemList);
        vo.setNextUrl(vipCardList.size() < PAGE_SIZE ? "" : String.valueOf(page + 1));
        vo.setVipLevel(vipInfoDao.getIntVipLevel(uid));
        return vo;
    }

    /**
     * 转换UserResourceData为VipCardItemVO
     */
    private VipCardItemVO convertToVipCardItem(UserResourceData vipCard, Map<Integer, ResourceConfigData> resConfigDataMap, VipDTO dto, int currentTime) {
        try {
            // 通过resourceId获取VIP等级
            ResourceConfigData resConfig = resConfigDataMap.get(vipCard.getResourceId());
            if (resConfig == null) {
                logger.error("Resource config not found for resourceId: {}", vipCard.getResourceId());
                return null;
            }
            VipCardItemVO item = new VipCardItemVO();
            item.setId(vipCard.getId());
            // 设置VIP等级卡名称
            item.setVipCardName(dto.getSlang() == SLangType.ARABIC ? resConfig.getNameAr() : resConfig.getName());
            // 设置VIP等级和天数描述
            item.setVipCardDay(vipCard.getResourceNumber());
            item.setVipCardIcon(dto.getSlang() == SLangType.ARABIC ? resConfig.getIconAr() : resConfig.getIcon());
            // 设置状态
            if (vipCard.getStatus() == 0 && vipCard.getEndTime() <= currentTime) {
                item.setStatus(2);
            }else {
                item.setStatus(vipCard.getStatus());
            }
            // 设置其他字段
            item.setVipLevel(resConfig.getRedundantField());
            item.setResourceId(vipCard.getResourceId());
            item.setExpireTime(vipCard.getEndTime());
            return item;
        } catch (Exception e) {
            logger.error("Error converting VIP card item for resourceId: {}", vipCard.getResourceId(), e);
            return null;
        }
    }

    /**
     * 发送VIP卡数量消息，设置vipCardCount为0
     * 参考VipCardResourcesHandler.sendVipCardNumMsg方法
     */
    private void sendVipCardNumMsg(String uid) {
        long vipCardCount = actorConfigDao.getLongUserConfig(uid, ActorConfigDao.VIP_CARD_COUNT, 0);
        // 只有当vipCardCount大于0时才设置为0并发送消息
        if (vipCardCount > 0) {
            actorConfigDao.updateUserConfig(uid, ActorConfigDao.VIP_CARD_COUNT, 0);
            VipCardNumMsg msg = new VipCardNumMsg();
            msg.setCardNum(0);
            logger.info("sendVipCardNumMsg uid: {}, original cardNum: {}, set to: 0", uid, vipCardCount);
            roomWebSender.sendPlayerWebMsg("", uid, uid, msg, false);
        } else {
            logger.debug("vipCardCount is already 0 for uid: {}, skip sending message", uid);
        }
    }

    /**
     * 发送VIP激活弹窗消息
     * 使用VipActivationPopupMessage发送个人弹窗消息
     *
     * @param actorData 用户
     * @param vipConfig VIP配置信息
     */
    private void sendVipActivationPopupMsg(ActorData actorData, VipConfigData vipConfig, VipV2BuyVO vo) {
        String uid = actorData.getUid();
        try {
            // 构建VIP激活弹窗消息
            VipActivationPopupMessage msg = new VipActivationPopupMessage();
            msg.setUid(uid);
            msg.setVipLevel(vipConfig.getVipLevel());
            // 设置弹窗背景图片
            msg.setPopupMedal(vipConfig.getVipModel());
            // 构建激活文案
            String userNickname = actorData.getName();
            // 获取VIP特权数量
            List<VipV2Privilege> vipPrivileges = parsePrivilege(vipConfig.getPrivilege(), actorData.getSlang(), VipFeatureConstant.VIP_VERSION_1);
            int privilegeCount = vipPrivileges != null ? vipPrivileges.size() : 0;

            String vipTitleEn = getVipTitleByVipLevel(vipConfig.getVipLevel(), SLangType.ENGLISH);
            String vipTitleAr = getVipTitleByVipLevel(vipConfig.getVipLevel(), SLangType.ARABIC);
            String activationTextEn = String.format("Congratulations, you are upgraded to become a valued %s user. Enjoy %s VIP privileges", vipTitleEn, privilegeCount);
            String activationTextAr = String.format("مبروكلقد تم ترقيتك لتصبح مستخدم %s مميز. استمتع بمزايا VIP بنسبة %s", vipTitleAr, privilegeCount);
            msg.setActivationTextEn(activationTextEn);
            msg.setActivationTextAr(activationTextAr);
            // 设置高亮文本（与广播消息保持一致）
            List<HighlightTextObject> highlightList = getHighlightTextObjects(vipConfig, userNickname);
            msg.setHighlightTextEn(highlightList);
            msg.setHighlightTextAr(highlightList);
            msg.setExpireTime(vo.getExpireTime());
            // 发送个人弹窗消息（参考sendVipActivationMsg的发送方式）
            roomWebSender.sendPlayerWebMsg("", uid, uid, msg, false);
            logger.info("VIP activation popup sent for user: {}, vipLevel: {}", uid, vipConfig.getVipLevel());

        } catch (Exception e) {
            logger.error("Failed to send VIP activation popup for user: {}, vipLevel: {}", uid, vipConfig.getVipLevel(), e);
        }
    }

    /**
     * vip自动佩戴资源
     *
     * @param dto 请求参数
     */
    public void vipAutoWearResource(VipDTO dto) {
        String uid = dto.getUid();
        int currentTime = DateHelper.getNowSeconds();
        try {
            // 获取用户VIP信息
            VipUserInfoData vipInfo = vipInfoDao.findVipInfoByUid(uid);
            if (vipInfo == null) {
                logger.debug("User {} has no VIP info", uid);
                return;
            }

            int vipLevel = vipInfo.getVipLevel();
            int vipEndTime = (int) (vipInfo.getVipEndTime().getTime() / 1000);
            int leftTime = vipEndTime - currentTime;
            if (leftTime <= 0) {
                logger.debug("VIP has expired for user: {}", uid);
                return;
            }

            // 获取VIP配置信息
            VipConfigData vipConfig = vipConfigDao.selectList().stream().filter(config -> config.getVipLevel() == vipLevel).findFirst().orElse(null);
            if (vipConfig == null) {
                logger.error("VIP config not found for level: {}", vipLevel);
                return;
            }
            // 获取资源key
            String resourceKey = vipConfig.getResourceKey();
            if (StringUtils.isEmpty(resourceKey)) {
                logger.error("Resource key not found for VIP level: {}", vipLevel);
                return;
            }
            // 佩戴资源
            resourceKeyHandlerService.wearResourceData(uid, resourceKey, "VIP Auto Wear");
            logger.info("VIP auto wear resource sent for user: {}, resourceKey: {}", uid, resourceKey);
        } catch (Exception e) {
            logger.error("Failed to send VIP auto wear resource for user: {}", uid, e);
        }
    }


    /**
     * 检查VIP过期弹窗
     *
     * @param dto 请求参数
     * @return VIP过期弹窗信息
     */
    public VipExpirePopupVO checkVipExpirePopup(VipDTO dto) {
        String uid = dto.getUid();
        int currentTime = DateHelper.getNowSeconds();
        VipExpirePopupVO vo = new VipExpirePopupVO();
        vo.setNow(currentTime);
        vo.setPopupType(0); // 默认无弹窗
        // 获取用户VIP信息
        VipUserInfoData vipInfo = vipInfoDao.findVipInfoByUid(uid);
        long vipUpgrade = actorConfigDao.getLongUserConfig(uid, ActorConfigDao.VIP_UPGRADE, 0);
        if (vipUpgrade <= 0) {
            handleVipResourceUpgrade(uid, vipInfo);
        }

        if (vipInfo == null) {
            return vo;
        }
        int vipLevel = vipInfo.getVipLevel();
        int vipEndTime = (int) (vipInfo.getVipEndTime().getTime() / 1000);
        int vipBuyTime = (int) (vipInfo.getVipBuyTime().getTime() / 1000);
        int leftTime = vipEndTime - currentTime;
        int leftDays = leftTime / 86400;

        // 获取VIP配置信息
        VipConfigData vipConfig = vipConfigDao.selectList().stream().filter(config -> config.getVipLevel() == vipLevel).findFirst().orElse(null);
        if (vipConfig == null) {
            logger.error("VIP config not found for level: {}", vipLevel);
            return vo;
        }
        // 设置基本信息
        vo.setVipLevel(vipLevel);
        vo.setExpireTime(vipEndTime);
        vo.setLeftDays(leftDays);
        vo.setVipBuyTime(vipBuyTime);
        // 判断弹窗类型
        if (leftTime <= 0) {
            // VIP已过期弹窗 - 检查是否已经弹过
            vo.setPopupType(2);
            String title = dto.getSlang() == SLangType.ARABIC ? String.format("انتهت صلاحية %s الخاص بك", vipConfig.getNameAr()) : String.format("Your %s has expired", vipConfig.getNameEn());
            String content = dto.getSlang() == SLangType.ARABIC ? String.format("لقد انتهت صلاحية %s الخاص بك، قم بالتفعيل الآن للاستمتاع بامتيازات VIP", vipConfig.getNameAr()) : String.format("Your %s has expired, activate now to enjoy VIP privileges", vipConfig.getNameEn());
            vo.setTitle(title);
            vo.setContent(content);
        } else if (leftDays <= 2 && leftDays >= 0) {
            // 检查是否为体验VIP（vipSource为2表示vipCard体验VIP，不触发即将过期弹窗）
            if (vipInfo.getVipSource() == 2) {
                logger.debug("Skip expire warning for trial VIP (vipSource=2), user: {}, vipLevel: {}", uid, vipLevel);
                return vo;
            }
            // 今天还没弹过即将过期弹窗，显示弹窗并记录
            vo.setPopupType(1);
            String title = dto.getSlang() == SLangType.ARABIC ? String.format("%s الخاص بك سينتهي قريباً", vipConfig.getNameAr()) : String.format("Your %s will expire soon", vipConfig.getNameEn());
            String content = dto.getSlang() == SLangType.ARABIC ? String.format("سينتهي %s الخاص بك قريباً، جدد للاستمتاع بامتيازات VIP", vipConfig.getNameAr()) : String.format("Your %s will expire soon, renew to enjoy VIP privileges", vipConfig.getNameEn());
            vo.setTitle(title);
            vo.setContent(content);
        }
        return vo;
    }

    /**
     * 移除VIP
     */
    public void vipRemove(VipV2BuyDTO dto) {
        String uid = dto.getUid();
        // 查询当前VIP信息
        VipUserInfoData vipInfo = vipInfoDao.findVipInfoByUid(uid);
        if (vipInfo == null) {
            logger.error("user has no vip info uid={}", uid);
            throw new CommonException(VipHttpCode.PARAM_ERROR, "User has no vip");
        }
        vipInfoDao.deleteByUid(uid);
        vipInfoDao.updateRedisVipGiftRemain(uid, 0);
        int vipEndTime = (int) (vipInfo.getVipEndTime().getTime() / 1000);
        handleVipChangeRemoveResource(uid, vipInfo.getVipLevel(), VipFeatureConstant.DESCRIPTION_REMOVE_VIP, vipEndTime);
    }


    /**
     * 处理vip升级新版本资源配置问题
     */
    private void handleVipResourceUpgrade(String uid, VipUserInfoData vipInfo) {
        actorConfigDao.updateUserConfig(uid, ActorConfigDao.VIP_UPGRADE, 1);
        if (vipInfo == null) {
            return;
        }

        // 删除老等级的vip资源
        // 下发新等级vip资源
        int currentTime = DateHelper.getNowSeconds();
        int vipEndTime = (int) (vipInfo.getVipEndTime().getTime() / 1000);
        int vipLevel = vipInfo.getVipLevel();
        if (vipEndTime <= currentTime) {
            return;
        }
        resourceKeyHandlerService.removeResourceData(uid, OLD_VIP_RESOURCE_KEY, VipFeatureConstant.DESCRIPTION_UPGRADE_VIP, vipEndTime);

        VipConfigData originalVipConfig = vipConfigDao.selectList().stream().filter(config -> config.getVipLevel() == vipLevel).findFirst().orElse(null);
        if (originalVipConfig == null) {
            logger.error("VIP config not found for level: {}", vipLevel);
            return;
        }
        int validDay = (vipEndTime - currentTime) / 86400;
        validDay = validDay > 0 ? validDay : 1;
        // 创建VipConfigData副本避免修改缓存数据
        VipConfigData vipConfig = new VipConfigData();
        BeanUtils.copyProperties(originalVipConfig, vipConfig);
        vipConfig.setValidDay(validDay);
        // 下发新版vip资源
        VipV2BO vipV2BO = new VipV2BO();
        vipV2BO.setVipLevel(vipLevel);
        vipV2BO.setVipSource(VipFeatureConstant.SOURCE_UPGRADE_APP);
        vipV2BO.setResourceDescription(VipFeatureConstant.DESCRIPTION_UPGRADE_VIP);
        releaseVipResources(uid, vipLevel, vipConfig, vipV2BO);
        // 佩戴资源
        resourceKeyHandlerService.wearResourceData(uid, vipConfig.getResourceKey(), "VIP Auto Wear");
    }


    /**
     * 查询最近三个月VIP记录
     *
     * @param uid  用户ID
     * @param page 页码
     * @return VIP记录列表
     */
    public VipRecordVO vipRecord(String uid, int page) {
        VipRecordVO recordVO = new VipRecordVO();
        int pageSize = 20;
        if (page < 1) {
            page = 1;
        }
        // 查询最近三个月的VIP记录
        List<VipRecordData> dataList = vipRecordDao.getRecords(uid, page, pageSize);
        int vipVersion = actorCommonService.getVipVersion(uid);
        List<VipRecordVO.VipRecord> recordList = new ArrayList<>();
        for (VipRecordData data : dataList) {
            VipRecordVO.VipRecord vipRecord = new VipRecordVO.VipRecord();
            vipRecord.setUid(data.getUid());
            vipRecord.setVipLevel(data.getVipLevel());
            vipRecord.setVipMedal(vipVersion == VipFeatureConstant.VIP_VERSION_0 ? ActorCommonService.OLD_VIP_MEDAL_MAP.get(data.getVipLevel()) : ActorCommonService.NEW_VIP_MEDAL_MAP.get(data.getVipLevel()));
            vipRecord.setVipDay(data.getVipDay());
            vipRecord.setRecordType(data.getRecordType());
            vipRecord.setCtime(data.getCtime());
            recordList.add(vipRecord);
        }
        recordVO.setList(recordList);
        if (dataList.size() < pageSize) {
            recordVO.setNextUrl("");
        } else {
            recordVO.setNextUrl(String.valueOf(page + 1));
        }
        return recordVO;
    }

    /**
     * 通过VIP卡激活VIP
     *
     * @param dto 激活请求参数
     * @return 激活结果
     */
    public VipV2BuyVO activateVipCard(VipCardActivateDTO dto) {
        String uid = dto.getUid();
        if (ObjectUtils.isEmpty(uid)) {
            throw new CommonH5Exception(VipHttpCode.USER_NOT_EXIST);
        }
        int id = dto.getId();
        int resourceId = dto.getResourceId();
        int currentTime = DateHelper.getNowSeconds();
        logger.info("Activating VIP card for uid: {}, resourceId: {}", uid, resourceId);
        if (id <= 0 || resourceId <= 0) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        VipV2BuyVO vo = new VipV2BuyVO();
        vo.setNow(currentTime);
        ActorData actorData = actorDao.getActorData(uid);
        // 验证VIP卡是否存在且可激活
        UserResourceData vipCard = validateVipCard(uid, id);
        // 获取VIP卡配置信息
        ResourceConfigData resConfig = getVipCardConfig(resourceId);
        // 验证性别限制
        validateGenderRestriction(actorData, resConfig.getRedundantField());
        // 获取用户当前VIP信息
        VipUserInfoData vipInfo = vipInfoDao.findVipInfoByUid(uid);
        // 激活VIP卡并更新VIP信息
        int activateVipLevel = resConfig.getRedundantField();
        int vipDays = vipCard.getResourceNumber();
        // 检查当前VIP状态，VIP卡激活允许降级
        VipV2BO vipV2BO = getCurrentVipLevel(vipInfo);
        // 验证VIP购买参数（性别校验等，不包括降级校验）
        VipConfigData originalVipConfig = validateVipPurchaseParams(uid, activateVipLevel);
        VipConfigData vipConfig = new VipConfigData();
        BeanUtils.copyProperties(originalVipConfig, vipConfig);
        vipConfig.setValidDay(vipDays);
        // 激活VIP卡并更新VIP信息
        activateVipAndUpdateInfo(uid, activateVipLevel, vipConfig, vipInfo, vo, vipV2BO.getVipSource());
        // 更新VIP卡状态为已激活
        userResourceDao.updateStatusById(uid, id, 1);
        //  插入VIP记录（记录类型为1表示VIP卡激活）
        insertVipRecord(uid, activateVipLevel, vipDays, VipFeatureConstant.RECORD_VIP_CARD, currentTime);
        // 发放VIP奖励 - 创建VipConfigData副本避免修改缓存数据
        distributeVipRewards(uid, vipV2BO, activateVipLevel, vipConfig);
        // 发送VIP激活广播消息
        sendVipActivationMsg(actorData, originalVipConfig, vo);
        // 事件埋点
        doVipActiveReport(uid, vipConfig.getVipLevel(), VipFeatureConstant.SOURCE_VIP_CARD, vipConfig.getValidDay(), 0, 0);
        logger.info("VIP card activation completed for uid: {}, resourceId: {}, activateVipLevel: {}, vipDays: {}", uid, resourceId, activateVipLevel, vipDays);
        return vo;
    }

    /**
     * 获取当前有效的VIP等级
     *
     * @param vipInfo VIP信息
     * @return 当前VIP等级，如果已过期返回0
     */
    private VipV2BO getCurrentVipLevel(VipUserInfoData vipInfo) {
        VipV2BO bo = new VipV2BO();
        bo.setVipSource(VipFeatureConstant.SOURCE_VIP_CARD);
        bo.setResourceDescription(VipFeatureConstant.DESCRIPTION_ACTIVATE_VIP_CARD);
        if (vipInfo == null) {
            return bo;
        }

        int currentTime = DateHelper.getNowSeconds();
        int vipEndTime = (int) (vipInfo.getVipEndTime().getTime() / 1000);
        int leftTime = vipEndTime - currentTime;
        bo.setVipLevel(leftTime > 0 ? vipInfo.getVipLevel() : 0);
        bo.setVipEndTime(vipEndTime);
        return bo;
    }

    /**
     * 处理VIP变化：移除当前VIP等级的资源和权限
     *
     * @param uid             用户ID
     * @param currentVipLevel 当前VIP等级
     */
    public void handleVipChangeRemoveResource(String uid, int currentVipLevel, String description, int vipEndTime) {
        VipConfigData oldVipConfigData = vipConfigDao.selectVipConfigByVipLevel(currentVipLevel);
        if (oldVipConfigData == null) {
            return;
        }
        try {
            String resourceKey = oldVipConfigData.getResourceKey();
            resourceKeyHandlerService.removeResourceData(uid, resourceKey, description, vipEndTime);

            // 解析特权信息并移除
            List<VipV2Privilege> privilegeList = parsePrivilege(oldVipConfigData.getPrivilege(), SLangType.ENGLISH, VipFeatureConstant.VIP_VERSION_1);
            for (VipV2Privilege privilege : privilegeList) {
                if (privilege.getType() == VipFeatureConstant.FEATURE_HIDE_VISITOR_IDENTITY) {
                    // 移除隐身访问权限
                    actorDao.updateGeneralConf(uid, "invisible", 0);
                    logger.info("Removed invisible privilege for uid: {}, currentVipLevel: {}", uid, currentVipLevel);
                }

                if (privilege.getType() == VipFeatureConstant.FEATURE_LIVE_ROOM) {
                    // 移除副房主权限
                    handleRoomModeSwitch(uid, currentVipLevel);
                    logger.info("Removed live room privilege for uid: {}, currentVipLevel: {}", uid, currentVipLevel);
                }

                if (privilege.getType() == VipFeatureConstant.FEATURE_DEPUTY_ROOM_OWNER) {
                    // 移除副房主权限
                    removeViceHostPrivileges(uid, currentVipLevel);
                    logger.info("Removed vice host privilege for uid: {}, currentVipLevel: {}", uid, currentVipLevel);
                }
            }

            logger.info("VIP privileges removed for uid: {}, currentVipLevel: {}", uid, currentVipLevel);
        } catch (Exception e) {
            logger.error("Failed to remove VIP privileges for uid: {}, currentVipLevel: {}", uid, currentVipLevel, e);
        }
    }

    /**
     * 验证VIP卡是否存在且可激活
     */
    private UserResourceData validateVipCard(String uid, int id) {
        UserResourceData vipCard = userResourceDao.selectUserResourceById(uid, id);
        if (vipCard == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "VIP card not found");
        }

        // 检查VIP卡类型
        if (vipCard.getResourceType() != BaseDataResourcesConstant.TYPE_VIP_CARD) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        // 检查VIP卡状态（0-待激活，1-已激活，2-已过期）
        if (vipCard.getStatus() != 0) {
            throw new CommonH5Exception(VipHttpCode.VIP_CARD_ALREADY_ACTIVATED);
        }

        // 检查VIP卡是否过期
        int currentTime = DateHelper.getNowSeconds();
        if (vipCard.getEndTime() <= currentTime) {
            throw new CommonH5Exception(VipHttpCode.VIP_CARD_EXPIRED);
        }

        return vipCard;
    }

    /**
     * 获取VIP卡配置信息
     */
    private ResourceConfigData getVipCardConfig(int resourceId) {
        ResourceConfigData resConfig = resourceConfigDao.getResourceDataFromDb(resourceId, BaseDataResourcesConstant.TYPE_VIP_CARD);
        if (resConfig == null) {
            throw new CommonException(HttpCode.PARAM_ERROR, "VIP card config not found");
        }
        return resConfig;
    }

    /**
     * 验证性别限制
     */
    private void validateGenderRestriction(ActorData actorData, int vipLevel) {
        if (actorData == null) {
            throw new CommonException(HttpCode.PARAM_ERROR, "User not found");
        }

        int userGender = actorData.getFb_gender(); // 1: 男性  2: 女性
        if (userGender == 1 && vipLevel == VipFeatureConstant.VIP_LEVEL_QUEEN) {
            // 男性不可激活Queen VIP
            throw new CommonException(VipHttpCode.NOT_BUY_VIP, "Male users cannot activate Queen VIP");
        }
    }

    /**
     * 激活VIP卡并更新VIP信息
     */
    private void activateVipAndUpdateInfo(String uid, int buyVipLevel, VipConfigData vipConfig, VipUserInfoData vipInfo, VipV2BuyVO vo, int vipSource) {
        int newVipEndTime;
        int vipTotal = 1;
        Date currentDate = new Date();
        int currentTime = vo.getNow();
        int vipDays = vipConfig.getValidDay();
        boolean isGiveVipGift = vipSource == VipFeatureConstant.SOURCE_BUY_VIP || vipSource == VipFeatureConstant.SOURCE_ADMIN_GIVE_VIP || (vipSource == VipFeatureConstant.SOURCE_VIP_CARD && vipDays >= VIP_GIFT_GIVE_VIP_CARD_DAY);
        synchronized (stringPool.intern(VipFeatureConstant.VIP_ACTIVE_KEY + uid)) {
            int vipGift = 0;
            if (vipInfo != null) {
                int currentVipLevel = vipInfo.getVipLevel();
                int vipEndTime = (int) (vipInfo.getVipEndTime().getTime() / 1000);
                int leftTime = vipEndTime - currentTime;
                if (leftTime > 0 && currentVipLevel == buyVipLevel) {
                    // 续费相同等级VIP
                    newVipEndTime = vipEndTime + vipDays * 86400;
                    if (isGiveVipGift) {
                        vipGift = vipInfo.getVipGiftRemain() + vipConfig.getVipGift();
                    } else {
                        // VIP卡续费，不增加VIP礼物
                        vipGift = vipInfo.getVipGiftRemain();
                    }
                } else {
                    // VIP卡激活：升级VIP、降级VIP或过期后重新激活
                    newVipEndTime = currentTime + vipDays * 86400;
                    if (isGiveVipGift) {
                        // 购买VIP续费，增加VIP礼物
                        vipGift = vipConfig.getVipGift();
                    }
                }
                vipTotal = vipInfo.getVipTimeTotal() + 1;
                int giftRemain = vipInfo.getVipGiftRemain();
                logger.info("Updating VIP info via card activation - uid: {}, vipEndTime: {}, vipTotal: {}, giftRemain: {}", uid, newVipEndTime, vipTotal, giftRemain);
                vipInfo.setVipEndTime(new Date(newVipEndTime * 1000L));
                vipInfo.setVipBuyTime(new Date(currentTime * 1000L));
                vipInfo.setVipTimeTotal(vipTotal);
                vipInfo.setVipLevel(buyVipLevel);
                vipInfo.setVipSource(vipSource);
                vipInfo.setVipGiftRemain(vipGift);
                vipInfo.setVipExpireStatus(0);
                vipInfoDao.update(vipInfo);
            } else {
                // 没有记录激活VIP
                if (isGiveVipGift) {
                    // 购买VIP续费，增加VIP礼物
                    vipGift = vipConfig.getVipGift();
                }
                newVipEndTime = currentTime + vipDays * 86400;
                vipInfo = new VipUserInfoData();
                vipInfo.setUid(uid);
                vipInfo.setVipLevel(buyVipLevel);
                vipInfo.setVipTimeTotal(vipTotal);
                vipInfo.setVipBuyTime(new Date(currentTime * 1000L));
                vipInfo.setVipEndTime(new Date(newVipEndTime * 1000L));
                vipInfo.setVipGiftUsed(0);
                vipInfo.setVipGoldTotal(0);
                vipInfo.setVipExpireStatus(0);
                vipInfo.setVipGiftRemain(vipGift);
                vipInfo.setVipSource(vipSource);
                vipInfo.setLastBenefitGetTime(currentDate);
                vipInfo.setCtime(currentDate);
                vipInfo.setMtime(currentDate);
                logger.info("Creating new VIP info via card activation - uid: {}, vipEndTime: {}, vipTotal: {}", uid, newVipEndTime, vipTotal);
                vipInfoDao.insert(vipInfo);
            }
            // 更新Redis中的VIP信息
            vipInfoDao.setVipLevelRedis(uid, buyVipLevel, newVipEndTime);
            vipInfoDao.updateRedisVipGiftRemain(uid, vipGift);
            vo.setBuyTime(currentTime);
            vo.setVipLevel(buyVipLevel);
            vo.setVipMedal(actorCommonService.getCommonVipMedal(uid, buyVipLevel));
            vo.setExpireTime(newVipEndTime);
        }
    }

    /**
     * 修改VIP
     *
     * @param dto 购买请求参数
     * @return 购买结果
     */
    public VipV2BuyVO vipChange(VipV2BuyDTO dto) {
        int newGender = dto.getNewGender();
        String uid = dto.getUid();
        int currentTime = DateHelper.getNowSeconds();
        VipV2BuyVO vo = new VipV2BuyVO();
        vo.setNow(currentTime);
        handleVipGenderChange(uid, newGender, vo);
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                // 处理性别变更时的VIP卡逻辑
                handleVipCardGenderChange(uid, newGender);
            }
        });
        return vo;
    }

    /**
     * 处理性别变更时的VIP相关逻辑
     */
    private void handleVipGenderChange(String uid, int newGender, VipV2BuyVO vo) {
        // 查询当前VIP信息
        VipUserInfoData vipInfo = vipInfoDao.findVipInfoByUid(uid);
        if (vipInfo == null) {
            // 用户没有VIP，只需要处理VIP卡
            return;
        }
        int currentVipLevel = vipInfo.getVipLevel();
        int currentTime = DateHelper.getNowSeconds();
        int vipEndTime = (int) (vipInfo.getVipEndTime().getTime() / 1000);

        // 检查VIP是否过期
        if (vipEndTime <= currentTime) {
            // VIP已过期，只需要处理VIP卡
            return;
        }
        int newVipLevel = currentVipLevel;
        if (newGender == 1 && currentVipLevel == VipFeatureConstant.VIP_LEVEL_QUEEN) {
            newVipLevel = VipFeatureConstant.VIP_LEVEL_5;
        }

        if (currentVipLevel == newVipLevel) {
            return;
        }
        VipV2BO vipV2BO = new VipV2BO();
        vipV2BO.setVipLevel(currentVipLevel);
        vipV2BO.setVipSource(VipFeatureConstant.SOURCE_VIP_CHANGE_GENDER);
        vipV2BO.setResourceDescription(VipFeatureConstant.DESCRIPTION_CHANGE_VIP);

        synchronized (stringPool.intern(VipFeatureConstant.VIP_ACTIVE_KEY + uid)) {
            int leftDay = (vipEndTime - currentTime) / 86400;
            leftDay = leftDay > 0 ? leftDay : 1;
            Map<Integer, VipConfigData> vipLevelConfigMap = vipConfigDao.selectList().stream().collect(Collectors.toMap(VipConfigData::getVipLevel, Function.identity()));
            VipConfigData originVipConfig = vipLevelConfigMap.get(newVipLevel);
            if (originVipConfig == null) {
                throw new CommonException(HttpCode.PARAM_ERROR);
            }
            VipConfigData vipConfig = new VipConfigData();
            BeanUtils.copyProperties(originVipConfig, vipConfig);
            vipConfig.setValidDay(leftDay);
            // 修改VIP等级
            vipInfo.setVipLevel(newVipLevel);
            vipInfoDao.update(vipInfo);
            vipInfoDao.setVipLevelRedis(uid, newVipLevel, vipEndTime);
            // 发放VIP奖励
            distributeVipRewards(uid, vipV2BO, newVipLevel, vipConfig);
            // resourceKeyHandlerService.wearResourceData(uid, vipConfig.getResourceKey(), VipFeatureConstant.DESCRIPTION_CHANGE_VIP);
        }
    }


    /**
     * 处理性别变更时的VIP卡逻辑
     */
    private void handleVipCardGenderChange(String aid, int newGender) {
        try {
            // 获取用户所有的VIP卡资源 - 分页获取所有VIP卡
            List<UserResourceData> vipCardList = userResourceDao.selectAllList(aid, BaseDataResourcesConstant.TYPE_VIP_CARD, 0);
            if (CollectionUtils.isEmpty(vipCardList)) {
                return;
            }

            // 获取VIP卡配置信息
            List<ResourceConfigData> resConfigDataList = resourceConfigDao.getResourceAllListFromDb(BaseDataResourcesConstant.TYPE_VIP_CARD);
            Map<Integer, ResourceConfigData> resConfigDataMap = resConfigDataList.stream().collect(Collectors.toMap(ResourceConfigData::getResourceId, Function.identity()));
            Map<Integer, ResourceConfigData> vipLevelResConfigDataMap = resConfigDataList.stream().collect(Collectors.toMap(ResourceConfigData::getRedundantField, Function.identity()));
            int currentTime = DateHelper.getNowSeconds();
            for (UserResourceData vipCard : vipCardList) {
                // 只处理未使用且未过期的VIP卡
                if (vipCard.getStatus() != 0 || vipCard.getEndTime() <= currentTime) {
                    continue;
                }

                ResourceConfigData resConfig = resConfigDataMap.get(vipCard.getResourceId());
                if (resConfig == null) {
                    continue;
                }

                int currentCardVipLevel = resConfig.getRedundantField();
                if (newGender == 1 && currentCardVipLevel == VipFeatureConstant.VIP_LEVEL_QUEEN) {
                    // 男性不可拥有Queen VIP卡，将其降级为VIP5
                    ResourceConfigData princeConfig = vipLevelResConfigDataMap.get(VipFeatureConstant.VIP_LEVEL_5);
                    if (princeConfig == null) {
                        logger.error("VIP card config not found for level 5");
                        continue;
                    }
                    userResourceDao.updateResourceIdById(vipCard.getUid(), vipCard.getId(), princeConfig.getResourceId());
                }
            }
        } catch (Exception e) {
            logger.error("Error handling VIP card gender change for aid: {}", aid, e);
            // 不抛出异常，允许性别修改继续进行
        }
    }

    /**
     * 根据VIP等级获取VIP名称
     */
    private String getVipTitleByVipLevel(int vipLevel, int slang) {
        if (vipLevel <= 4) {
            return "VIP " + vipLevel;
        } else if (vipLevel == 5) {
            return slang == SLangType.ARABIC ? "الأمير" : "Prince";
        } else if (vipLevel == 6) {
            return slang == SLangType.ARABIC ? "الملك" : "King";
        } else if (vipLevel == 10) {
            return slang == SLangType.ARABIC ? "الملكة" : "Queen";
        }
        return "";
    }

    /**
     * 移除副房主权限（VIP5及以上）
     *
     * @param uid      用户ID
     * @param vipLevel VIP等级
     */
    public void removeViceHostPrivileges(String uid, int vipLevel) {
        try {
            actorConfigDao.updateUserConfig(uid, ActorConfigDao.SHOW_VIDEO_IN_DETAIL, 0);
            String roomId = RoomUtils.formatRoomId(uid);
            // 取消副房主权限
            List<RoomMemberData> viceHostList = roomMemberDao.listViceHostManager(roomId);
            if (CollectionUtils.isEmpty(viceHostList)) {
                return;
            }
            for (RoomMemberData roomMemberdata : viceHostList) {
                String aid = roomMemberdata.getAid();
                roomMemberDao.cancelViceHost(roomId, aid);
                CancelAdminPushMsg msg = new CancelAdminPushMsg();
                msg.setRoomId(roomId);
                msg.setAid(aid);
                msg.setRole(1);
                msg.setNow_utype(2);
                msg.setAdmin(1);
                roomWebSender.sendPlayerWebMsg(roomId, uid, aid, msg, true);
            }
            logger.info("Removed vice host privileges for uid: {}, vipLevel: {}, count: {}", uid, vipLevel, viceHostList.size());
        } catch (Exception e) {
            logger.error("Failed to remove vice host privileges for uid: {}, vipLevel: {}", uid, vipLevel, e);
        }
    }

    /**
     * 处理直播间模式切换
     *
     * @param uid      用户ID
     * @param vipLevel VIP等级
     */
    public void handleRoomModeSwitch(String uid, int vipLevel) {
        try {
            // VIP2及以上才有直播间权限，过期后需要切换为语音间
            if (vipLevel >= 2) {
                MongoRoomData mongoRoomData = mongoRoomDao.findData(RoomUtils.formatRoomId(uid));
                if (mongoRoomData != null && mongoRoomData.getRoomMode() == RoomConstant.LIVE_ROOM_MODE) {
                    RoomEventDTO dto = new RoomEventDTO();
                    dto.setOs(0);
                    dto.setVersioncode(999);
                    dto.setUid(uid);
                    dto.setRoomId(RoomUtils.formatRoomId(uid));
                    dto.setType(RoomConstant.VOICE_ROOM_MODE);
                    iRoomService.switchRoomMode(dto);
                    logger.info("Switched room mode from live to voice for uid: {}, vipLevel: {}", uid, vipLevel);
                }
            }
        } catch (Exception e) {
            logger.error("Failed to switch room mode for uid: {}, vipLevel: {}", uid, vipLevel, e);
        }
    }

    /**
     * 使用vip特权修改资料时上报
     */
    private void doVipActiveReport(String uid, int vipLevel, int vipSource, int validDay, int costDiamonds, int discount) {
        VipActivateEvent logEvent = new VipActivateEvent();
        logEvent.setUid(uid);
        logEvent.setVip_level(vipLevel);
        logEvent.setVip_level_name(VipFeatureConstant.VIP_NAME_MAP.getOrDefault(vipLevel, ""));
        logEvent.setActivate_type(vipSource);
        logEvent.setItems_service_life(validDay);
        logEvent.setCost_diamonds(costDiamonds);
        logEvent.setIs_discount(discount);
        logEvent.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(logEvent));
    }


    /**
     * 刷新vip礼物数量
     */
    public void vipFlushGift() {
        List<VipUserInfoData>  activeVipUsers = vipInfoDao.selectAllActiveVipUsers();
        for (VipUserInfoData vipInfo : activeVipUsers) {
            String uid = vipInfo.getUid();
            int vipGiftRemain = vipInfoDao.getVipGiftRemain(uid);

            if (vipGiftRemain != vipInfo.getVipGiftRemain()) {
                logger.info("vipGiftRemain not match, uid: {}, vipGiftRemain: {}, vipInfo.getVipGiftRemain(): {}", uid, vipGiftRemain, vipInfo.getVipGiftRemain());
                vipInfo.setVipGiftRemain(vipGiftRemain);
                vipInfoDao.update(vipInfo);
            }
        }
    }

}
