package com.quhong.data;

public class CountBeansData {
    private String key;
    private int beans;
    private String name;
    private long sortScore;

    public CountBeansData() {
    }

    public CountBeansData(String uid, int beans, String name) {
        this(uid, beans, name, 0);
    }

    public CountBeansData(String key, int beans, String name, long sortScore) {
        this.key = key;
        this.beans = beans;
        this.name = name;
        this.sortScore = sortScore;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public int getBeans() {
        return beans;
    }

    public void setBeans(int beans) {
        this.beans = beans;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public long getSortScore() {
        return sortScore;
    }

    public void setSortScore(long sortScore) {
        this.sortScore = sortScore;
    }

    @Override
    public String toString() {
        return "CountBeansData{" +
                "key='" + key + '\'' +
                ", beans=" + beans +
                ", name='" + name + '\'' +
                ", sortScore=" + sortScore +
                '}';
    }
}
