package com.quhong.data;

/**
 * <AUTHOR>
 * @date 2023/8/9
 */
public class BaiShunGameData {

    private int gameId;
    private String gameName;
    private String gameType;
    private int version;
    private int webType;
    private String webUrl;
    private int reduceBeansActType;
    private String reduceBeansTitle;
    private int incBeansActType;
    private String incBeansTitle;

    public BaiShunGameData() {
    }

    public BaiShunGameData(int gameId, String gameName, String gameType, int version, int webType, String webUrl, int reduceBeansActType, String reduceBeansTitle, int incBeansActType, String incBeansTitle) {
        this.gameId = gameId;
        this.gameName = gameName;
        this.gameType = gameType;
        this.version = version;
        this.webType = webType;
        this.webUrl = webUrl;
        this.reduceBeansActType = reduceBeansActType;
        this.reduceBeansTitle = reduceBeansTitle;
        this.incBeansActType = incBeansActType;
        this.incBeansTitle = incBeansTitle;
    }

    public int getGameId() {
        return gameId;
    }

    public void setGameId(int gameId) {
        this.gameId = gameId;
    }

    public String getGameName() {
        return gameName;
    }

    public void setGameName(String gameName) {
        this.gameName = gameName;
    }

    public String getGameType() {
        return gameType;
    }

    public void setGameType(String gameType) {
        this.gameType = gameType;
    }

    public int getWebType() {
        return webType;
    }

    public void setWebType(int webType) {
        this.webType = webType;
    }

    public String getWebUrl() {
        return webUrl;
    }

    public void setWebUrl(String webUrl) {
        this.webUrl = webUrl;
    }

    public int getIncBeansActType() {
        return incBeansActType;
    }

    public void setIncBeansActType(int incBeansActType) {
        this.incBeansActType = incBeansActType;
    }

    public String getIncBeansTitle() {
        return incBeansTitle;
    }

    public void setIncBeansTitle(String incBeansTitle) {
        this.incBeansTitle = incBeansTitle;
    }

    public int getReduceBeansActType() {
        return reduceBeansActType;
    }

    public void setReduceBeansActType(int reduceBeansActType) {
        this.reduceBeansActType = reduceBeansActType;
    }

    public String getReduceBeansTitle() {
        return reduceBeansTitle;
    }

    public void setReduceBeansTitle(String reduceBeansTitle) {
        this.reduceBeansTitle = reduceBeansTitle;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }
}
