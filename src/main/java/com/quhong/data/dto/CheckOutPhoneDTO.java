package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;

public class CheckOutPhoneDTO extends HttpEnvData {

    private String account;
    private String pCountryCode;
    private String pNumber;
    private String tnMsg ;
    private String openId ;
    private String ip;
    private int fromType ; // 0 app调用 1内部接口调用
    private String shuMeiMsg ;

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getpCountryCode() {
        return pCountryCode;
    }

    public void setpCountryCode(String pCountryCode) {
        this.pCountryCode = pCountryCode;
    }

    public String getpNumber() {
        return pNumber;
    }

    public void setpNumber(String pNumber) {
        this.pNumber = pNumber;
    }

    public String getTnMsg() {
        return tnMsg;
    }

    public void setTnMsg(String tnMsg) {
        this.tnMsg = tnMsg;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public int getFromType() {
        return fromType;
    }

    public void setFromType(int fromType) {
        this.fromType = fromType;
    }

    public String getShuMeiMsg() {
        return shuMeiMsg;
    }

    public void setShuMeiMsg(String shuMeiMsg) {
        this.shuMeiMsg = shuMeiMsg;
    }
}
