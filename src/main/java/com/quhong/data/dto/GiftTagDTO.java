package com.quhong.data.dto;

import com.quhong.data.vo.ZipInfoVO;

import java.util.List;

public class GiftTagDTO {

    private int rid;
    private int price;
    private int gtype;
    private int gplatform;
    private int gptype;
    private int isFusionAnimation;
    private int activityStatus;
    private int downTime;
    private List<Integer> turntableGiftList;
    private List<Integer> newGiftList;
    private List<Integer> prankGiftList;
    private ZipInfoVO zipInfoVO;


    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public int getPrice() {
        return price;
    }

    public void setPrice(int price) {
        this.price = price;
    }

    public int getGtype() {
        return gtype;
    }

    public void setGtype(int gtype) {
        this.gtype = gtype;
    }

    public int getGplatform() {
        return gplatform;
    }

    public void setGplatform(int gplatform) {
        this.gplatform = gplatform;
    }

    public int getGptype() {
        return gptype;
    }

    public void setGptype(int gptype) {
        this.gptype = gptype;
    }

    public int getIsFusionAnimation() {
        return isFusionAnimation;
    }

    public void setIsFusionAnimation(int isFusionAnimation) {
        this.isFusionAnimation = isFusionAnimation;
    }

    public int getActivityStatus() {
        return activityStatus;
    }

    public void setActivityStatus(int activityStatus) {
        this.activityStatus = activityStatus;
    }

    public int getDownTime() {
        return downTime;
    }

    public void setDownTime(int downTime) {
        this.downTime = downTime;
    }

    public List<Integer> getTurntableGiftList() {
        return turntableGiftList;
    }

    public void setTurntableGiftList(List<Integer> turntableGiftList) {
        this.turntableGiftList = turntableGiftList;
    }

    public List<Integer> getNewGiftList() {
        return newGiftList;
    }

    public void setNewGiftList(List<Integer> newGiftList) {
        this.newGiftList = newGiftList;
    }

    public List<Integer> getPrankGiftList() {
        return prankGiftList;
    }

    public void setPrankGiftList(List<Integer> prankGiftList) {
        this.prankGiftList = prankGiftList;
    }

    public ZipInfoVO getZipInfoVO() {
        return zipInfoVO;
    }

    public void setZipInfoVO(ZipInfoVO zipInfoVO) {
        this.zipInfoVO = zipInfoVO;
    }
}
