package com.quhong.data.dto;

public class AppleConsumptionInformationDTO {
    /**
     * (Required) The age of the customer’s account.
     *
     * Possible Values
     * 0
     * Account age is undeclared.
     *
     * 1
     * Account age is between 0–3 days.
     *
     * 2
     * Account age is between 3–10 days.
     *
     * 3
     * Account age is between 10–30 days.
     *
     * 4
     * Account age is between 30–90 days.
     *
     * 5
     * Account age is between 90–180 days.
     *
     * 6
     * Account age is between 180–365 days.
     *
     * 7
     * Account age is over 365 days.
     */
    private Integer accountTenure;
    /**
     * (Required) The UUID of the in-app user account that completed the consumable in-app purchase transaction.
     * When a customer initiates an in-app purchase, you can optionally generate an appAccountToken(_:) and send it to the App Store. If you use the Original API for In-App Purchase, you may provide a UUID in the applicationUsername property. The App Store returns the same UUID in appAccountToken in the transaction information after the customer completes the purchase.
     *
     * The ConsumptionRequest response body requires that you set the appAccountToken value to a valid value of either a UUID or an empty string. Set the appAccountToken value to the value you received in the CONSUMPTION_REQUEST notification, or, if you choose not to provide this information, set the value to an empty string.
     *
     * If you receive a CONSUMPTION_REQUEST notification for a transaction, find its associated appAccountToken value as follows:
     *
     * If you receive App Store Server Notifications version 2, the appAccountToken value is in JWSTransactionDecodedPayload.
     *
     * If you receive App Store Server Notifications version 1, the appAccountToken value is in unified_receipt.Latest_receipt_info.
     *
     * The appAccountToken value may be an empty string if your app doesn’t use app account tokens.
     *
     * For more information about App Store Server Notifications versions, see App Store Server Notifications Changelog.
     */
    private String appAccountToken;
    /**
     * (Required) A value that indicates the extent to which the customer consumed the in-app purchase.
     * Possible Values
     * 0
     * The consumption status is undeclared.
     *
     * 1
     * The in-app purchase is not consumed.
     *
     * 2
     * The in-app purchase is partially consumed.
     *
     * 3
     * The in-app purchase is fully consumed.
     */
    private Integer consumptionStatus;
    /**
     * (Required) A Boolean value of true or false that indicates whether the customer consented to provide consumption data.
     * Possible Values
     */
    private Boolean customerConsented;
    /**
     * (Required) A value that indicates whether the app successfully delivered an in-app purchase that works properly.
     * Possible Values
     * 0
     * The app delivered the consumable in-app purchase and it’s working properly.
     *
     * 1
     * The app didn’t deliver the consumable in-app purchase due to a quality issue.
     *
     * 2
     * The app delivered the wrong item.
     *
     * 3
     * The app didn’t deliver the consumable in-app purchase due to a server outage.
     *
     * 4
     * The app didn’t deliver the consumable in-app purchase due to an in-game currency change.
     *
     * 5
     * The app didn’t deliver the consumable in-app purchase for other reasons.
     */
    private Integer deliveryStatus;
    /**
     *  (Required) A value that indicates the total amount, in USD, of in-app purchases the customer has made in your app, across all platforms.
     * 0
     * Lifetime purchase amount is undeclared.
     *
     * 1
     * Lifetime purchase amount is 0 USD.
     *
     * 2
     * Lifetime purchase amount is between 0.01–49.99 USD.
     *
     * 3
     * Lifetime purchase amount is between 50–99.99 USD.
     *
     * 4
     * Lifetime purchase amount is between 100–499.99 USD.
     *
     * 5
     * Lifetime purchase amount is between 500–999.99 USD.
     *
     * 6
     * Lifetime purchase amount is between 1000–1999.99 USD.
     *
     * 7
     * Lifetime purchase amount is over 2000 USD.
     */
    private Integer lifetimeDollarsPurchased;
    /**
     * (Required) A value that indicates the total amount, in USD, of refunds the customer has received, in your app, across all platforms.
     * Possible Values
     * 0
     * Lifetime refund amount is undeclared.
     *
     * 1
     * Lifetime refund amount is 0 USD.
     *
     * 2
     * Lifetime refund amount is between 0.01–49.99 USD.
     *
     * 3
     * Lifetime refund amount is between 50–99.99 USD.
     *
     * 4
     * Lifetime refund amount is between 100–499.99 USD.
     *
     * 5
     * Lifetime refund amount is between 500–999.99 USD.
     *
     * 6
     * Lifetime refund amount is between 1000–1999.99 USD.
     *
     * 7
     * Lifetime refund amount is over 2000 USD.
     */
    private Integer lifetimeDollarsRefunded;
    /**
     * (Required) A value that indicates the platform on which the customer consumed the in-app purchase.
     * Possible Values
     * 0
     * Undeclared.
     *
     * 1
     * An Apple platform.
     *
     * 2
     * Non-Apple platform.
     */
    private Integer platform;
    /**
     * (Required) A value that indicates the amount of time that the customer used the app.
     * Possible Values
     * 0
     * The engagement time is undeclared.
     *
     * 1
     * The engagement time is between 0–5 minutes.
     *
     * 2
     * The engagement time is between 5–60 minutes.
     *
     * 3
     * The engagement time is between 1–6 hours.
     *
     * 4
     * The engagement time is between 6–24 hours.
     *
     * 5
     * The engagement time is between 1–4 days.
     *
     * 6
     * The engagement time is between 4–16 days.
     *
     * 7
     * The engagement time is over 16 days.
     */
    private Integer playTime;
    /**
     * (Required) A Boolean value of true or false that indicates whether you provided, prior to its purchase, a free sample or trial of the content, or information about its functionality.
     */
    private Boolean sampleContentProvided;
    /**
     * (Required) The status of the customer’s account
     * Possible Values
     * 0
     * Account status is undeclared.
     *
     * 1
     * The customer’s account is active.
     *
     * 2
     * The customer’s account is suspended.
     *
     * 3
     * The customer’s account is terminated.
     *
     * 4
     * The customer’s account has limited access.
     */
    private Integer userStatus;

    public AppleConsumptionInformationDTO(){

    }

    public Integer getAccountTenure() {
        return accountTenure;
    }

    public void setAccountTenure(Integer accountTenure) {
        this.accountTenure = accountTenure;
    }

    public String getAppAccountToken() {
        return appAccountToken;
    }

    public void setAppAccountToken(String appAccountToken) {
        this.appAccountToken = appAccountToken;
    }

    public Integer getConsumptionStatus() {
        return consumptionStatus;
    }

    public void setConsumptionStatus(Integer consumptionStatus) {
        this.consumptionStatus = consumptionStatus;
    }

    public Integer getDeliveryStatus() {
        return deliveryStatus;
    }

    public void setDeliveryStatus(Integer deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    public Integer getLifetimeDollarsPurchased() {
        return lifetimeDollarsPurchased;
    }

    public Boolean getCustomerConsented() {
        return customerConsented;
    }

    public void setCustomerConsented(Boolean customerConsented) {
        this.customerConsented = customerConsented;
    }

    public void setLifetimeDollarsPurchased(Integer lifetimeDollarsPurchased) {
        this.lifetimeDollarsPurchased = lifetimeDollarsPurchased;
    }

    public Integer getLifetimeDollarsRefunded() {
        return lifetimeDollarsRefunded;
    }

    public void setLifetimeDollarsRefunded(Integer lifetimeDollarsRefunded) {
        this.lifetimeDollarsRefunded = lifetimeDollarsRefunded;
    }

    public Integer getPlatform() {
        return platform;
    }

    public void setPlatform(Integer platform) {
        this.platform = platform;
    }

    public Integer getPlayTime() {
        return playTime;
    }

    public void setPlayTime(Integer playTime) {
        this.playTime = playTime;
    }

    public Boolean getSampleContentProvided() {
        return sampleContentProvided;
    }

    public void setSampleContentProvided(Boolean sampleContentProvided) {
        this.sampleContentProvided = sampleContentProvided;
    }

    public Integer getUserStatus() {
        return userStatus;
    }

    public void setUserStatus(Integer userStatus) {
        this.userStatus = userStatus;
    }
}
