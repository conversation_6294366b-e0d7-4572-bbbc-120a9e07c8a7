package com.quhong.data.dto;

import com.alibaba.fastjson.JSON;
import com.quhong.handler.HttpEnvData;
import org.springframework.util.StringUtils;

public class UserListPageDTO extends HttpEnvData {
    private int page = 1;
    private String key;

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

}
