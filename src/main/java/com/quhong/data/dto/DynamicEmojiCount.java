package com.quhong.data.dto;

/**
 * 用户私信动效表情统计
 *
 * <AUTHOR>
 * @date 2025/8/5 16:29
 */
public class DynamicEmojiCount {
    /**
     * 发送者uid
     */
    private String fromUid;
    /**
     * 发送次数
     */
    private String count;

    public String getFromUid() {
        return fromUid;
    }

    public void setFromUid(String fromUid) {
        this.fromUid = fromUid;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }
}
