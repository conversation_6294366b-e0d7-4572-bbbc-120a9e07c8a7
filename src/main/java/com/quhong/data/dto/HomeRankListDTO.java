package com.quhong.data.dto;

import com.alibaba.fastjson.JSON;
import com.quhong.handler.HttpEnvData;
import org.springframework.util.StringUtils;

public class HomeRankListDTO extends HttpEnvData {
    private int ttype; // 4 小时榜，1日榜，2周榜，3月榜

    public int getTtype() {
        return ttype;
    }

    public void setTtype(int ttype) {
        this.ttype = ttype;
    }

    public boolean isParmsValid() {
        if (ttype > 0 && ttype <= 4 ) {
            return true;
        }
        return false;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

}
