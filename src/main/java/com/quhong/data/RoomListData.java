package com.quhong.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.MongoRoomData;
import org.springframework.util.StringUtils;

import java.util.Set;

public class RoomListData {

    private String aid; // 房主uid
    private String country; // 国家
    private String roomId; // 房间id
    private String room_head; // 房间头像
    private String room_name; // 房间名字
    private String announce; // 房间公告
    private String room_frame; // popular房间列表装饰
    private int viplevel; // 房主vip等级
    private String vipMedal; // vip勋章
    private int online; // 房间在线人数
    private int visitorNum; // 房间访客人数
    private int pwd; // 无密码0，有秘码1
    private int recreationTag; // 娱乐活动标签 1Youtube 2幸运转盘 3Ludo 4真心话 5Live 6carrom游戏 10Bumper Blaster 12 UMO 15: 房间活动tag 17 克罗姆游戏 18 巴洛特游戏
    private String recreationValue; // 基于recreationTag设置 值
    private int tag; // 房间的标签
    @Deprecated
    private int room_type; // 房间类型，客户端已弃用？
    @Deprecated
    private int roomStatus; // 客户端已弃用，房间当前状态，1: Party, 2: Live, 3: Game, 4: Watch
    private int isOfficial; // 是官方房间 0否 1是
    private int aliveTime; // 房间最近一次人数0-->1变化的时间
    @JSONField(serialize = false)
    private int micCount; // 麦位置人数
    private int ctime;

    // 859 社交版本增加字段
    private int socialWeight; // social推荐权重 100000以上为迎新房 80000-100000为同国家 40000-80000为同地区  0-40000为非同地区
    private int popularWeight; // 人气值  -排序用
    private int rateWeight;    // 评价分数 -排序用
    private String micUserAid; // 推荐上麦用户uid
    private int micUserGender; // 推荐上麦用户性别
    private int allNewWeight; // 859 all-new推荐权重
    private int allNewMeetWeight; // 859 meet-all 社交页推荐权重
    private int newRookieRoomWeight; // 859 all-new 新用户 迎新房内部权重 （只有迎新房有）
    private int roomRecommendType; // 房间推荐类型 1 迎新房 2 优质房 3 大R房

    @JSONField(serialize = false)
    private int isCheckMicList; // 是否检查了麦位列表 1是 0没有
    @JSONField(serialize = false)
    private int validMicCount; // 空余可上麦座位数
    @JSONField(serialize = false)
    private int isHangUpRoom; // 是否为挂机房间 1是 0不是
    @JSONField(serialize = false)
    private int isKickOutRoom; // 是否为踢人房间 1是 0不是
    @JSONField(serialize = false)
    private Set<String> onMicUserSet; // 麦上新用户

    public void copyFromMongoRoomData(MongoRoomData mongoRoomData) {
        this.roomId = mongoRoomData.getRid();
        this.country = mongoRoomData.getCountry();
        this.room_head = ImageUrlGenerator.generateRoomUrl(mongoRoomData.getHead());
        this.room_name = mongoRoomData.getName();
        this.announce = null == mongoRoomData.getAnnounce() ? "" : mongoRoomData.getAnnounce();
        this.room_type = mongoRoomData.getRoom_type();
        this.pwd = StringUtils.isEmpty(mongoRoomData.getPwd()) ? 0 : 1;
        this.tag = mongoRoomData.getTag();
        this.aliveTime = mongoRoomData.getAliveTime();
        this.ctime = mongoRoomData.getCtime();
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getRoom_head() {
        return room_head;
    }

    public void setRoom_head(String room_head) {
        this.room_head = room_head;
    }

    public String getRoom_name() {
        return room_name;
    }

    public void setRoom_name(String room_name) {
        this.room_name = room_name;
    }

    public String getAnnounce() {
        return announce;
    }

    public void setAnnounce(String announce) {
        this.announce = announce;
    }

    public String getRoom_frame() {
        return room_frame;
    }

    public void setRoom_frame(String room_frame) {
        this.room_frame = room_frame;
    }

    public int getViplevel() {
        return viplevel;
    }

    public void setViplevel(int viplevel) {
        this.viplevel = viplevel;
    }

    public String getVipMedal() {
        return vipMedal;
    }

    public void setVipMedal(String vipMedal) {
        this.vipMedal = vipMedal;
    }

    public int getOnline() {
        return online;
    }

    public void setOnline(int online) {
        this.online = online;
    }

    public int getRoom_type() {
        return room_type;
    }

    public void setRoom_type(int room_type) {
        this.room_type = room_type;
    }

    public int getPwd() {
        return pwd;
    }

    public void setPwd(int pwd) {
        this.pwd = pwd;
    }

    public int getRoomStatus() {
        return roomStatus;
    }

    public void setRoomStatus(int roomStatus) {
        this.roomStatus = roomStatus;
    }

    public int getTag() {
        return tag;
    }

    public void setTag(int tag) {
        this.tag = tag;
    }

    public int getRecreationTag() {
        return recreationTag;
    }

    public void setRecreationTag(int recreationTag) {
        this.recreationTag = recreationTag;
    }

    public String getRecreationValue() {
        return recreationValue;
    }

    public void setRecreationValue(String recreationValue) {
        this.recreationValue = recreationValue;
    }

    public int getIsOfficial() {
        return isOfficial;
    }

    public void setIsOfficial(int isOfficial) {
        this.isOfficial = isOfficial;
    }

    public int getAliveTime() {
        return aliveTime;
    }

    public void setAliveTime(int aliveTime) {
        this.aliveTime = aliveTime;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public int getMicCount() {
        return micCount;
    }

    public void setMicCount(int micCount) {
        this.micCount = micCount;
    }

    public int getVisitorNum() {
        return visitorNum;
    }

    public void setVisitorNum(int visitorNum) {
        this.visitorNum = visitorNum;
    }

    public int getSocialWeight() {
        return socialWeight;
    }

    public void setSocialWeight(int socialWeight) {
        this.socialWeight = socialWeight;
    }

    public int getPopularWeight() {
        return popularWeight;
    }

    public void setPopularWeight(int popularWeight) {
        this.popularWeight = popularWeight;
    }

    public int getRateWeight() {
        return rateWeight;
    }

    public void setRateWeight(int rateWeight) {
        this.rateWeight = rateWeight;
    }

    public String getMicUserAid() {
        return micUserAid;
    }

    public void setMicUserAid(String micUserAid) {
        this.micUserAid = micUserAid;
    }

    public int getMicUserGender() {
        return micUserGender;
    }

    public void setMicUserGender(int micUserGender) {
        this.micUserGender = micUserGender;
    }

    public int getAllNewWeight() {
        return allNewWeight;
    }

    public void setAllNewWeight(int allNewWeight) {
        this.allNewWeight = allNewWeight;
    }

    public int getIsHangUpRoom() {
        return isHangUpRoom;
    }

    public void setIsHangUpRoom(int isHangUpRoom) {
        this.isHangUpRoom = isHangUpRoom;
    }

    public int getIsCheckMicList() {
        return isCheckMicList;
    }

    public void setIsCheckMicList(int isCheckMicList) {
        this.isCheckMicList = isCheckMicList;
    }

    public int getAllNewMeetWeight() {
        return allNewMeetWeight;
    }

    public void setAllNewMeetWeight(int allNewMeetWeight) {
        this.allNewMeetWeight = allNewMeetWeight;
    }

    public int getValidMicCount() {
        return validMicCount;
    }

    public void setValidMicCount(int validMicCount) {
        this.validMicCount = validMicCount;
    }

    public int getIsKickOutRoom() {
        return isKickOutRoom;
    }

    public void setIsKickOutRoom(int isKickOutRoom) {
        this.isKickOutRoom = isKickOutRoom;
    }

    public int getNewRookieRoomWeight() {
        return newRookieRoomWeight;
    }

    public void setNewRookieRoomWeight(int newRookieRoomWeight) {
        this.newRookieRoomWeight = newRookieRoomWeight;
    }

    public Set<String> getOnMicUserSet() {
        return onMicUserSet;
    }

    public void setOnMicUserSet(Set<String> onMicUserSet) {
        this.onMicUserSet = onMicUserSet;
    }

    public int getRoomRecommendType() {
        return roomRecommendType;
    }

    public void setRoomRecommendType(int roomRecommendType) {
        this.roomRecommendType = roomRecommendType;
    }
}
