package com.quhong.data;

public class RedisFruitPoolRewardData {
    private long rewardNum; // 奖池累计钻石
    private int loop; // 当前轮次
    private int validLoop; // 有效的累计轮次
    private int mTime;  //更新时间
    private long totalProfit; // 平台总盈利

    public RedisFruitPoolRewardData() {
    }

    public RedisFruitPoolRewardData(long rewardNum, int loop, int validLoop, int mTime) {
        this.rewardNum = rewardNum;
        this.loop = loop;
        this.validLoop = validLoop;
        this.mTime = mTime;
    }

    public long getRewardNum() {
        return rewardNum;
    }

    public void setRewardNum(long rewardNum) {
        this.rewardNum = rewardNum;
    }

    public int getLoop() {
        return loop;
    }

    public void setLoop(int loop) {
        this.loop = loop;
    }

    public int getValidLoop() {
        return validLoop;
    }

    public void setValidLoop(int validLoop) {
        this.validLoop = validLoop;
    }

    public int getmTime() {
        return mTime;
    }

    public void setmTime(int mTime) {
        this.mTime = mTime;
    }

    public long getTotalProfit() {
        return totalProfit;
    }

    public void setTotalProfit(long totalProfit) {
        this.totalProfit = totalProfit;
    }

    @Override
    public String toString() {
        return "RedisFruitPoolRewardData{" +
                "rewardNum=" + rewardNum +
                ", loop=" + loop +
                ", validLoop=" + validLoop +
                ", mTime=" + mTime +
                ", totalProfit=" + totalProfit +
                '}';
    }
}
