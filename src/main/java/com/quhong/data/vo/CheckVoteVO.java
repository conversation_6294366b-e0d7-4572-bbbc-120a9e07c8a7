package com.quhong.data.vo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/3
 */
public class CheckVoteVO {

    /**
     * id
     */
    private Integer id;

    /**
     * 房间id
     */
    private String roomId;

    /**
     * 投票标题
     */
    private String title;

    /**
     * 投票类型 0礼物投票 1问答投票
     */
    private Integer type;

    /**
     * 选项类型 0单选 1多选（1问答投票）
     */
    private Integer optionType;

    /**
     * 礼物id（礼物投票才有）
     */
    private Integer giftId;

    /**
     * 礼物图标（礼物投票才有）
     */
    private String giftIcon;

    /**
     * 投票持续时间 单位：分钟 (0)
     */
    private Integer duration;

    /**
     * 投票结束时间
     */
    private Integer endTime;

    /**
     * 投票费用类型 0金币 1钻石 （问题投票才有）
     */
    private Integer feeType;

    /**
     * 花费的钻石数（问题投票才有）
     */
    private Integer costBeans;

    /**
     * 投票用户数
     */
    private Integer votingUserNum;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Integer ctime;

    /**
     * 选项
     */
    private List<VoteOption> optionList;

    /**
     * 是否有权限创建 0否 1是
     */
    private Integer canCreate;

    /**
     * 投票配置
     */
    private VoteConfig voteConfig;

    /**
     * 状态 0创建 1进行中 2手动结束 3自动结束
     */
    private Integer status;

    public static class VoteConfig {

        private int feeType;
        private Integer[] costRange;
        private int defaultGiftId;
        private String defaultGiftIcon;

        public VoteConfig() {
        }

        public VoteConfig(int feeType, Integer[] costRange, int defaultGiftId, String defaultGiftIcon) {
            this.feeType = feeType;
            this.costRange = costRange;
            this.defaultGiftId = defaultGiftId;
            this.defaultGiftIcon = defaultGiftIcon;
        }

        public int getFeeType() {
            return feeType;
        }

        public void setFeeType(int feeType) {
            this.feeType = feeType;
        }

        public Integer[] getCostRange() {
            return costRange;
        }

        public void setCostRange(Integer[] costRange) {
            this.costRange = costRange;
        }

        public int getDefaultGiftId() {
            return defaultGiftId;
        }

        public void setDefaultGiftId(int defaultGiftId) {
            this.defaultGiftId = defaultGiftId;
        }

        public String getDefaultGiftIcon() {
            return defaultGiftIcon;
        }

        public void setDefaultGiftIcon(String defaultGiftIcon) {
            this.defaultGiftIcon = defaultGiftIcon;
        }
    }

    public static class VoteOption {

        private Integer id;

        /**
         * 投票id
         */
        private Integer voteId;

        /**
         * 选项顺序
         */
        private Integer optionOrder;

        /**
         * 选项内容
         */
        private String optionContent;

        /**
         * 票数
         */
        private Integer votesNum;

        /**
         * 用户id（礼物投票才有）
         */
        private String aid;

        /**
         * 礼物图标（礼物投票才有）
         */
        private String giftIcon;

        /**
         * 用户姓名（礼物投票才有）
         */
        private String userName;

        /**
         * 用户头像（礼物投票才有）
         */
        private String userHead;

        /**
         * 是否是自己 0否 1是（礼物投票才有）
         */
        private int isSelf;

        /**
         * 是否自己已投票 0否 1是 (问答投票才有)
         */
        private Integer selfVoted;

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getGiftIcon() {
            return giftIcon;
        }

        public void setGiftIcon(String giftIcon) {
            this.giftIcon = giftIcon;
        }

        public Integer getVoteId() {
            return voteId;
        }

        public void setVoteId(Integer voteId) {
            this.voteId = voteId;
        }

        public Integer getOptionOrder() {
            return optionOrder;
        }

        public void setOptionOrder(Integer optionOrder) {
            this.optionOrder = optionOrder;
        }

        public String getOptionContent() {
            return optionContent;
        }

        public void setOptionContent(String optionContent) {
            this.optionContent = optionContent;
        }

        public Integer getVotesNum() {
            return votesNum;
        }

        public void setVotesNum(Integer votesNum) {
            this.votesNum = votesNum;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public String getUserHead() {
            return userHead;
        }

        public void setUserHead(String userHead) {
            this.userHead = userHead;
        }

        public int getIsSelf() {
            return isSelf;
        }

        public void setIsSelf(int isSelf) {
            this.isSelf = isSelf;
        }

        public Integer getSelfVoted() {
            return selfVoted;
        }

        public void setSelfVoted(Integer selfVoted) {
            this.selfVoted = selfVoted;
        }

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getOptionType() {
        return optionType;
    }

    public void setOptionType(Integer optionType) {
        this.optionType = optionType;
    }

    public Integer getGiftId() {
        return giftId;
    }

    public void setGiftId(Integer giftId) {
        this.giftId = giftId;
    }

    public String getGiftIcon() {
        return giftIcon;
    }

    public void setGiftIcon(String giftIcon) {
        this.giftIcon = giftIcon;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public Integer getCostBeans() {
        return costBeans;
    }

    public void setCostBeans(Integer costBeans) {
        this.costBeans = costBeans;
    }

    public Integer getVotingUserNum() {
        return votingUserNum;
    }

    public void setVotingUserNum(Integer votingUserNum) {
        this.votingUserNum = votingUserNum;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public List<VoteOption> getOptionList() {
        return optionList;
    }

    public void setOptionList(List<VoteOption> optionList) {
        this.optionList = optionList;
    }

    public Integer getFeeType() {
        return feeType;
    }

    public void setFeeType(Integer feeType) {
        this.feeType = feeType;
    }

    public VoteConfig getVoteConfig() {
        return voteConfig;
    }

    public void setVoteConfig(VoteConfig voteConfig) {
        this.voteConfig = voteConfig;
    }

    public Integer getCanCreate() {
        return canCreate;
    }

    public void setCanCreate(Integer canCreate) {
        this.canCreate = canCreate;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
