package com.quhong.data.vo;


import com.quhong.mongo.data.ResourceKeyConfigData;

import java.util.List;

public class AICustomizedGiftVO extends OtherRankConfigVO {

    private List<OtherRankingListVO> rankingList; //
    private OtherRankingListVO myRank;//  我的排名，当月有数据
    private Integer headStatus; //  -1 未上传过图片 0 待审核 1 审核通过 2 审核不通过
    private Integer uploadHeadStatus; // 0 不可上传 1 可以上传

    public List<OtherRankingListVO> getRankingList() {
        return rankingList;
    }

    public void setRankingList(List<OtherRankingListVO> rankingList) {
        this.rankingList = rankingList;
    }

    public OtherRankingListVO getMyRank() {
        return myRank;
    }

    public void setMyRank(OtherRankingListVO myRank) {
        this.myRank = myRank;
    }

    public Integer getHeadStatus() {
        return headStatus;
    }

    public void setHeadStatus(Integer headStatus) {
        this.headStatus = headStatus;
    }

    public Integer getUploadHeadStatus() {
        return uploadHeadStatus;
    }

    public void setUploadHeadStatus(Integer uploadHeadStatus) {
        this.uploadHeadStatus = uploadHeadStatus;
    }
}
