package com.quhong.data.vo;


import java.util.List;

public class PlantTreeVO extends OtherRankConfigVO {
    private int userRid;
    private boolean openTree;
    private int waterNum;
    private int treeStatus;
    private int waterProcess;
    private boolean signStatus;
    private int signPush;
    private String joinRoom;
    private boolean shareMoment;

    private List<SignTable> signTableList;
    private List<HardWordVO> rankingList;
    private HardWordVO myRank;

    public static class SignTable{
        private String signDate;
        private boolean signStatus;

        public String getSignDate() {
            return signDate;
        }

        public void setSignDate(String signDate) {
            this.signDate = signDate;
        }

        public boolean isSignStatus() {
            return signStatus;
        }

        public void setSignStatus(boolean signStatus) {
            this.signStatus = signStatus;
        }
    }


    public static class HardWordVO{
        private String name;
        private String head;
        private int score;
        private int rank;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public int getScore() {
            return score;
        }

        public void setScore(int score) {
            this.score = score;
        }

        public int getRank() {
            return rank;
        }

        public void setRank(int rank) {
            this.rank = rank;
        }
    }

    public int getUserRid() {
        return userRid;
    }

    public void setUserRid(int userRid) {
        this.userRid = userRid;
    }

    public boolean isOpenTree() {
        return openTree;
    }

    public void setOpenTree(boolean openTree) {
        this.openTree = openTree;
    }

    public int getWaterNum() {
        return waterNum;
    }

    public void setWaterNum(int waterNum) {
        this.waterNum = waterNum;
    }

    public int getTreeStatus() {
        return treeStatus;
    }

    public void setTreeStatus(int treeStatus) {
        this.treeStatus = treeStatus;
    }

    public int getWaterProcess() {
        return waterProcess;
    }

    public void setWaterProcess(int waterProcess) {
        this.waterProcess = waterProcess;
    }

    public int getSignPush() {
        return signPush;
    }

    public void setSignPush(int signPush) {
        this.signPush = signPush;
    }

    public boolean isSignStatus() {
        return signStatus;
    }

    public void setSignStatus(boolean signStatus) {
        this.signStatus = signStatus;
    }

    public String getJoinRoom() {
        return joinRoom;
    }

    public void setJoinRoom(String joinRoom) {
        this.joinRoom = joinRoom;
    }

    public boolean isShareMoment() {
        return shareMoment;
    }

    public void setShareMoment(boolean shareMoment) {
        this.shareMoment = shareMoment;
    }

    public List<SignTable> getSignTableList() {
        return signTableList;
    }

    public void setSignTableList(List<SignTable> signTableList) {
        this.signTableList = signTableList;
    }

    public List<HardWordVO> getRankingList() {
        return rankingList;
    }

    public void setRankingList(List<HardWordVO> rankingList) {
        this.rankingList = rankingList;
    }

    public HardWordVO getMyRank() {
        return myRank;
    }

    public void setMyRank(HardWordVO myRank) {
        this.myRank = myRank;
    }
}
