package com.quhong.data.vo;

import com.quhong.room.RoomTags;

import java.util.List;

public class PreCreateRoomVO {

    private String roomName; // 当前房间名字
    private int tag = -1; // 当前房间标签，-1为首次创建房间
    private String tagName; // 当前房间名字
    private int roomMode; // 房间模式，1Voice、2Live
    private int liveRoomRequirement; // 创建直播房间权限，可能的值: 0所有用户均可创建直播房 1VIP2及以上 2用户等级5级及以上
    private String roomHead; // 房间封面
    private String roomAnnounce; // 房间公告

    // 844弃用
    @Deprecated
    private Integer voiceOnline;
    @Deprecated
    private Integer liveOnline;
    @Deprecated
    private List<RoomTags.RoomTag> tagList;
    @Deprecated
    private Integer roomType = 2; // 房间类型 1直播房 2语聊房

    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    public int getTag() {
        return tag;
    }

    public void setTag(int tag) {
        this.tag = tag;
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public int getRoomMode() {
        return roomMode;
    }

    public void setRoomMode(int roomMode) {
        this.roomMode = roomMode;
    }

    public int getLiveRoomRequirement() {
        return liveRoomRequirement;
    }

    public void setLiveRoomRequirement(int liveRoomRequirement) {
        this.liveRoomRequirement = liveRoomRequirement;
    }

    public Integer getVoiceOnline() {
        return voiceOnline;
    }

    public void setVoiceOnline(Integer voiceOnline) {
        this.voiceOnline = voiceOnline;
    }

    public Integer getLiveOnline() {
        return liveOnline;
    }

    public void setLiveOnline(Integer liveOnline) {
        this.liveOnline = liveOnline;
    }

    public List<RoomTags.RoomTag> getTagList() {
        return tagList;
    }

    public void setTagList(List<RoomTags.RoomTag> tagList) {
        this.tagList = tagList;
    }

    public Integer getRoomType() {
        return roomType;
    }

    public void setRoomType(Integer roomType) {
        this.roomType = roomType;
    }

    public String getRoomHead() {
        return roomHead;
    }

    public void setRoomHead(String roomHead) {
        this.roomHead = roomHead;
    }

    public String getRoomAnnounce() {
        return roomAnnounce;
    }

    public void setRoomAnnounce(String roomAnnounce) {
        this.roomAnnounce = roomAnnounce;
    }
}
