package com.quhong.data.vo;

import com.quhong.data.dto.RoomDTO;
import com.quhong.enums.RoomConstant;
import com.quhong.enums.RoomType;
import com.quhong.handler.HttpEnvData;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.utils.AppVersionUtils;
import com.quhong.vo.RoomMicListVo;
import org.springframework.util.ObjectUtils;

public class RoomVO {
    private static final ZegoConfigVO ZEGO_CONFIG_VO = new ZegoConfigVO();
    // 房主信息
    private RoomOwnerVO roomOwner = new RoomOwnerVO();
    // 用户信息
    private RoomVisitorVO roomVisitor = new RoomVisitorVO();
    // 房间配置信息
    private RoomConfigVO roomConfig = new RoomConfigVO();
    // 即构配置
    private ZegoConfigVO zegoConfig = ZEGO_CONFIG_VO;
    // 麦位信息
    private RoomMicListVo roomMic = new RoomMicListVo();
    // 房间用户信息
    private RoomActorVO roomActor = new RoomActorVO();


    public void copyFromActorData(RoomActorDetailData ownerData) {
        roomOwner.setDesc(ownerData.getDesc());
        roomOwner.setGender(ownerData.getGender() == 1 ? 1 : 2);
        roomOwner.setAge(ownerData.getAge());
        roomOwner.setRid(ownerData.getRid());
        roomOwner.setOs(String.valueOf(ownerData.getOs()));
        roomOwner.setValid(ownerData.getValid());
        roomOwner.setAid(ownerData.getAid());
        roomOwner.setRidData(ownerData.getRidData());
    }

    public void copyFromMongoRoomData(MongoRoomData roomData, HttpEnvData req) {
        roomConfig.setAnnounce(roomData.getAnnounce());
        roomConfig.setPrivilege(roomData.getPrivi());
        roomConfig.setFee(roomData.getFee());
        roomConfig.setFeeType(roomData.getFeetype());
        roomConfig.setComp(roomData.getComp() == RoomType.COMP ? RoomType.COMP : 0);
        roomConfig.setPwd(ObjectUtils.isEmpty(roomData.getPwd()) ? 0 : 1);
        roomConfig.setRoomId(roomData.getRid());
        roomConfig.setOnline(roomData.getOnline());
        roomConfig.setRoomName(roomData.getName());
        roomConfig.setRoomPk(roomData.getRoom_pk());
        roomConfig.setChatLocked(roomData.getChat_locked() > 0 ? 1 : 0);
        roomConfig.setVoiceChatLocked(roomData.getChat_locked());
        roomConfig.setPicLocked(roomData.getPic_locked());
        roomConfig.setRoomType(roomData.getRoom_type());
        roomConfig.setRoomMode(roomData.getRoomMode() == 0 ? RoomConstant.VOICE_ROOM_MODE : roomData.getRoomMode());
        roomConfig.setRtcType(roomData.getRtc_type());
        roomConfig.setMicTheme(roomData.getMic_theme() == 0 ? 1 : roomData.getMic_theme());
        roomConfig.setCreateGame(roomData.getCreate_game());
        roomConfig.setTextLimit(null == roomData.getTextLimit() ? -1 : roomData.getTextLimit());
        // 直播房间的micTheme设置为0
        if (roomData.getRoom_type() == RoomType.COMP) {
            roomConfig.setMicTheme(0);
        }
        if (AppVersionUtils.versionCheck(844, req) && RoomConstant.LIVE_ROOM_MODE == roomData.getRoomMode()) {
            roomConfig.setMicTheme(0);
        }
    }

    public RoomOwnerVO getRoomOwner() {
        return roomOwner;
    }

    public void setRoomOwner(RoomOwnerVO roomOwner) {
        this.roomOwner = roomOwner;
    }

    public RoomVisitorVO getRoomVisitor() {
        return roomVisitor;
    }

    public void setRoomVisitor(RoomVisitorVO roomVisitor) {
        this.roomVisitor = roomVisitor;
    }

    public RoomConfigVO getRoomConfig() {
        return roomConfig;
    }

    public void setRoomConfig(RoomConfigVO roomConfig) {
        this.roomConfig = roomConfig;
    }

    public RoomMicListVo getRoomMic() {
        return roomMic;
    }

    public void setRoomMic(RoomMicListVo roomMic) {
        this.roomMic = roomMic;
    }

    public RoomActorVO getRoomActor() {
        return roomActor;
    }

    public void setRoomActor(RoomActorVO roomActor) {
        this.roomActor = roomActor;
    }

    public ZegoConfigVO getZegoConfig() {
        return zegoConfig;
    }

    public void setZegoConfig(ZegoConfigVO zegoConfig) {
        this.zegoConfig = zegoConfig;
    }
}
