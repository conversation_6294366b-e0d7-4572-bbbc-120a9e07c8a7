package com.quhong.data.vo;

public class BeautifulRidSetVO {
    private int rid;
    private int left_days;

    private String strRid; // 展示的id值
    /**
     * 靓号等级颜色划分：
     * 红色(5)：1-2位数、字母+数字。例如：11、UAE、UAE12
     * 咖色(4)：3位数字。例如：333、456、321
     * 玫红(3)：4位数字。例如：3456、5435、67859
     * 蓝色(2)：5位数字。例如：34567、54355、67859
     * 绿色(1)：6位数字。例如：345356、456454、3458935
     * 0 非靓号
     */
    private int alphaLevel;
    private int uniqueEndTime; // 靓号的结束时间戳
    private int uniqueGetType; // 靓号的获取方式   0 官方赠送  1 商店购买

    private Integer uniqueGetTime; // 靓号获取时间戳

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public int getLeft_days() {
        return left_days;
    }

    public void setLeft_days(int left_days) {
        this.left_days = left_days;
    }

    public String getStrRid() {
        return strRid;
    }

    public void setStrRid(String strRid) {
        this.strRid = strRid;
    }

    public int getAlphaLevel() {
        return alphaLevel;
    }

    public void setAlphaLevel(int alphaLevel) {
        this.alphaLevel = alphaLevel;
    }

    public int getUniqueEndTime() {
        return uniqueEndTime;
    }

    public void setUniqueEndTime(int uniqueEndTime) {
        this.uniqueEndTime = uniqueEndTime;
    }

    public int getUniqueGetType() {
        return uniqueGetType;
    }

    public void setUniqueGetType(int uniqueGetType) {
        this.uniqueGetType = uniqueGetType;
    }

    public Integer getUniqueGetTime() {
        return uniqueGetTime;
    }

    public void setUniqueGetTime(Integer uniqueGetTime) {
        this.uniqueGetTime = uniqueGetTime;
    }
}
