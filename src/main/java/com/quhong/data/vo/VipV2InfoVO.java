package com.quhong.data.vo;

import java.util.List;

public class VipV2InfoVO {
    /**
     * 用户当前vip等级
     */
    private int vipLevel;
    /**
     * 用户当前vip过期时间
     */
    private int expireTime;
    /**
     * 用户性别
     */
    private int gender;
    /**
     * vip说明规则Url
     */
    private String ruleUrl;
    /**
     * vip记录Url
     */
    private String recordUrl;
    /**
     * vip卡未读数
     */
    private int vipCardUnread;
    /**
     * vip版本样式: 0:旧版 1:新版
     */
    private int vipVersion;
    /**
     * vip版本切换图标列表
     */
    private List<String> vipVersionSwitchList;
    /**
     * vip特权信息列表
     */
    private List<VipV2Item> vipItemList;


    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public int getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(int expireTime) {
        this.expireTime = expireTime;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public int getVipVersion() {
        return vipVersion;
    }

    public void setVipVersion(int vipVersion) {
        this.vipVersion = vipVersion;
    }

    public List<String> getVipVersionSwitchList() {
        return vipVersionSwitchList;
    }

    public void setVipVersionSwitchList(List<String> vipVersionSwitchList) {
        this.vipVersionSwitchList = vipVersionSwitchList;
    }

    public List<VipV2Item> getVipItemList() {
        return vipItemList;
    }

    public void setVipItemList(List<VipV2Item> vipItemList) {
        this.vipItemList = vipItemList;
    }
    public String getRuleUrl() {
        return ruleUrl;
    }

    public void setRuleUrl(String ruleUrl) {
        this.ruleUrl = ruleUrl;
    }

    public String getRecordUrl() {
        return recordUrl;
    }

    public void setRecordUrl(String recordUrl) {
        this.recordUrl = recordUrl;
    }

    public int getVipCardUnread() {
        return vipCardUnread;
    }

    public void setVipCardUnread(int vipCardUnread) {
        this.vipCardUnread = vipCardUnread;
    }
}
