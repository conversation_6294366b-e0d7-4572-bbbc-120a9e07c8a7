package com.quhong.data.vo;

import com.quhong.data.SudGameSimpleInfo;

import java.util.List;
import java.util.Map;

/**
 * 房间配置信息
 */
public class RoomConfigVO {
    private String roomId; // 房间id
    private String roomName; // 房间名字
    private String roomHead; // 房间头像
    private int pwd; // 房间密码
    private long devotes; // 房间贡献值
    private int roomMode; // 房间模式，1Voice、2Live
    private int comp; // 是否是直播房
    private String devotesStr; // 房间贡献值
    private int dirtyVersion; // 脏话版本号
    private String streamId; // 流id
    private int rtcType; // rtc类型
    private String zegoToken; // 即构token
    private String agoraToken; // 声网RTC授权
    private String agoraRtmToken; // 声网RTM授权
    private String announce; // 房间公告
    private int memberCount; // 房间会员数量
    private int privilege; // 1所有人都可以上麦 2要花钱才能上 3需要申请
    private int fee; // 房间会员费用
    private int feeType;  // 房间会员费用类型，金币或钻石
    private int online;   // 当前在线人数
    private int roomPk; // 1为默认开启状态，2 为不开启状态
    private int picLocked; // 房间是否禁止发图片 1为禁止
    private int textLimit; // 房间发消息，限制用户等级
    private int roomType; // 房间类型 1 直播 2 普通 3 音乐房间
    private int tag; // 房间标签
    private String tagName; // 房间标签
    private String followWord; // 关注房间引导语
    private int followTime; // x秒后展示关注房间提示语
    private List<String> guideList; // 随机5条引导语
    private VipRoomConfigVO vipRoomConfig; // 房间vip配置
    private RoomThemeVO roomTheme; // 房间背景
    private int musicSwitch; // 卡拉OK是否开启
    private int topicSwitch; // 话题是否开启
    private int videoSwitch; // 视频是否开启
    private int greetSwitch; // 欢迎语是否开启
    private int sendGiftExpire; // 未送礼过期时间，单位 秒
    private int allMute; // 所有麦位是否禁止 0 否 1 是
    private int voiceChatLocked; // 房间禁止公屏聊天
    private int chatLocked; // 房间是否禁止公屏聊天 1为禁止
    private int videoVip; // 需要多少等级才能开启该video
    private int isPtg; // 是否partyGirl 0否 1是
    private int matchFlag; // 是否开启匹配 0否 1是
    private int httpSendRoomMsg = 1; // 是否通过http发送房间消息 0否 1是
    private int httpHeartSwitch; // 是否通过http发送心跳 0否 1是
    private int micTheme; // 麦位主题
    private int hideMicThemeConfig = 0; // 1隐藏麦位主题配置
    private int emojiSwitch; // 表情控制开关 0:关  1: 开
    private int welcome; // 是否显示welcome按钮 1:显示
    private int msgLimitNum = 200; // 房间人数超过200人开始限制
    private int msgLimitTime = 500; // msg发送最小间隔，毫秒
    private int gameLimitTime = 1000; // 骰子和幸运数字最小发送间隔，毫秒
    private int onMicTipTime = 120; // 上麦提示引导，秒
    private int sayHelloTime = 30; // 打招呼提示引导，秒
    private int createGame; //创建游戏权限
    private Map<String, Object> actorRoomConfig; // 用户在房间的相关设置
    private int uploadLog; // 是否上传客户端日志
    private int lowModels; // 是否是低端机型，1低端机型，需要弹窗提示
    private int newBgShelfTime; // 新房间背景上架时间
    private String activityRoomPolicy; // 房间运营政策功能展示URL，仅对白名单用户下发
    private RocketInfoVO rocketInfo; // 房间火箭信息
    private RoomRocketV2VO rocketInfoV2; // 房间火箭信息v2
    private RoomEventVO roomEvent; // 房间活动信息
    private int repeatMsgCost; // 发送一条重复消息的费用
    private String hotRank; //hot列表当前房间排名
    private List<String> devoteUserList; // 当前房间贡献榜前三名的用户头像
    private int liveRoomRequirement; // 创建直播房间权限，可能的值: 0所有用户均可创建直播房 1VIP2及以上 2用户等级5级及以上
    private long roomLikesNum; // 房间点赞数
    private int roomLevel;//房间等级
    private List<String> likeIconUrlList;// 点赞图标
    private List<String> welcomeWord; // 随机欢迎语
    private RoomGameConfigVO roomGameConfig; // 新版游戏房相关配置
    private TruthOrDareV2VO truthOrDareGameInfo;
    private SudGameSimpleInfo sudGameInfo;  // 房间在玩的sud游戏类型

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    public String getRoomHead() {
        return roomHead;
    }

    public void setRoomHead(String roomHead) {
        this.roomHead = roomHead;
    }

    public int getPwd() {
        return pwd;
    }

    public void setPwd(int pwd) {
        this.pwd = pwd;
    }

    public long getDevotes() {
        return devotes;
    }

    public void setDevotes(long devotes) {
        this.devotes = devotes;
    }

    public int getComp() {
        return comp;
    }

    public void setComp(int comp) {
        this.comp = comp;
    }

    public String getDevotesStr() {
        return devotesStr;
    }

    public void setDevotesStr(String devotesStr) {
        this.devotesStr = devotesStr;
    }

    public int getDirtyVersion() {
        return dirtyVersion;
    }

    public void setDirtyVersion(int dirtyVersion) {
        this.dirtyVersion = dirtyVersion;
    }

    public String getStreamId() {
        return streamId;
    }

    public void setStreamId(String streamId) {
        this.streamId = streamId;
    }

    public int getRtcType() {
        return rtcType;
    }

    public void setRtcType(int rtcType) {
        this.rtcType = rtcType;
    }

    public String getZegoToken() {
        return zegoToken;
    }

    public void setZegoToken(String zegoToken) {
        this.zegoToken = zegoToken;
    }

    public String getAgoraToken() {
        return agoraToken;
    }

    public void setAgoraToken(String agoraToken) {
        this.agoraToken = agoraToken;
    }

    public String getAgoraRtmToken() {
        return agoraRtmToken;
    }

    public void setAgoraRtmToken(String agoraRtmToken) {
        this.agoraRtmToken = agoraRtmToken;
    }

    public String getAnnounce() {
        return announce;
    }

    public void setAnnounce(String announce) {
        this.announce = announce;
    }

    public int getMemberCount() {
        return memberCount;
    }

    public void setMemberCount(int memberCount) {
        this.memberCount = memberCount;
    }

    public int getPrivilege() {
        return privilege;
    }

    public void setPrivilege(int privilege) {
        this.privilege = privilege;
    }

    public int getFee() {
        return fee;
    }

    public void setFee(int fee) {
        this.fee = fee;
    }

    public int getFeeType() {
        return feeType;
    }

    public void setFeeType(int feeType) {
        this.feeType = feeType;
    }

    public int getOnline() {
        return online;
    }

    public void setOnline(int online) {
        this.online = online;
    }

    public int getRoomPk() {
        return roomPk;
    }

    public void setRoomPk(int roomPk) {
        this.roomPk = roomPk;
    }

    public int getChatLocked() {
        return chatLocked;
    }

    public void setChatLocked(int chatLocked) {
        this.chatLocked = chatLocked;
    }

    public int getPicLocked() {
        return picLocked;
    }

    public void setPicLocked(int picLocked) {
        this.picLocked = picLocked;
    }

    public int getTextLimit() {
        return textLimit;
    }

    public void setTextLimit(int textLimit) {
        this.textLimit = textLimit;
    }

    public int getRoomType() {
        return roomType;
    }

    public void setRoomType(int roomType) {
        this.roomType = roomType;
    }

    public int getTag() {
        return tag;
    }

    public void setTag(int tag) {
        this.tag = tag;
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public String getFollowWord() {
        return followWord;
    }

    public void setFollowWord(String followWord) {
        this.followWord = followWord;
    }

    public int getFollowTime() {
        return followTime;
    }

    public void setFollowTime(int followTime) {
        this.followTime = followTime;
    }

    public List<String> getGuideList() {
        return guideList;
    }

    public void setGuideList(List<String> guideList) {
        this.guideList = guideList;
    }

    public VipRoomConfigVO getVipRoomConfig() {
        return vipRoomConfig;
    }

    public void setVipRoomConfig(VipRoomConfigVO vipRoomConfig) {
        this.vipRoomConfig = vipRoomConfig;
    }

    public RoomThemeVO getRoomTheme() {
        return roomTheme;
    }

    public void setRoomTheme(RoomThemeVO roomTheme) {
        this.roomTheme = roomTheme;
    }

    public int getMusicSwitch() {
        return musicSwitch;
    }

    public void setMusicSwitch(int musicSwitch) {
        this.musicSwitch = musicSwitch;
    }

    public int getTopicSwitch() {
        return topicSwitch;
    }

    public void setTopicSwitch(int topicSwitch) {
        this.topicSwitch = topicSwitch;
    }

    public int getVideoSwitch() {
        return videoSwitch;
    }

    public void setVideoSwitch(int videoSwitch) {
        this.videoSwitch = videoSwitch;
    }

    public int getGreetSwitch() {
        return greetSwitch;
    }

    public void setGreetSwitch(int greetSwitch) {
        this.greetSwitch = greetSwitch;
    }

    public int getSendGiftExpire() {
        return sendGiftExpire;
    }

    public void setSendGiftExpire(int sendGiftExpire) {
        this.sendGiftExpire = sendGiftExpire;
    }

    public int getAllMute() {
        return allMute;
    }

    public void setAllMute(int allMute) {
        this.allMute = allMute;
    }

    public int getVoiceChatLocked() {
        return voiceChatLocked;
    }

    public void setVoiceChatLocked(int voiceChatLocked) {
        this.voiceChatLocked = voiceChatLocked;
    }

    public int getVideoVip() {
        return videoVip;
    }

    public void setVideoVip(int videoVip) {
        this.videoVip = videoVip;
    }

    public int getIsPtg() {
        return isPtg;
    }

    public void setIsPtg(int isPtg) {
        this.isPtg = isPtg;
    }

    public int getMatchFlag() {
        return matchFlag;
    }

    public void setMatchFlag(int matchFlag) {
        this.matchFlag = matchFlag;
    }

    public int getHttpSendRoomMsg() {
        return httpSendRoomMsg;
    }

    public void setHttpSendRoomMsg(int httpSendRoomMsg) {
        this.httpSendRoomMsg = httpSendRoomMsg;
    }

    public int getHttpHeartSwitch() {
        return httpHeartSwitch;
    }

    public void setHttpHeartSwitch(int httpHeartSwitch) {
        this.httpHeartSwitch = httpHeartSwitch;
    }

    public int getMicTheme() {
        return micTheme;
    }

    public void setMicTheme(int micTheme) {
        this.micTheme = micTheme;
    }

    public int getHideMicThemeConfig() {
        return hideMicThemeConfig;
    }

    public void setHideMicThemeConfig(int hideMicThemeConfig) {
        this.hideMicThemeConfig = hideMicThemeConfig;
    }

    public int getEmojiSwitch() {
        return emojiSwitch;
    }

    public void setEmojiSwitch(int emojiSwitch) {
        this.emojiSwitch = emojiSwitch;
    }

    public int getWelcome() {
        return welcome;
    }

    public void setWelcome(int welcome) {
        this.welcome = welcome;
    }

    public int getMsgLimitNum() {
        return msgLimitNum;
    }

    public void setMsgLimitNum(int msgLimitNum) {
        this.msgLimitNum = msgLimitNum;
    }

    public int getMsgLimitTime() {
        return msgLimitTime;
    }

    public void setMsgLimitTime(int msgLimitTime) {
        this.msgLimitTime = msgLimitTime;
    }

    public int getGameLimitTime() {
        return gameLimitTime;
    }

    public void setGameLimitTime(int gameLimitTime) {
        this.gameLimitTime = gameLimitTime;
    }

    public int getOnMicTipTime() {
        return onMicTipTime;
    }

    public void setOnMicTipTime(int onMicTipTime) {
        this.onMicTipTime = onMicTipTime;
    }

    public int getSayHelloTime() {
        return sayHelloTime;
    }

    public void setSayHelloTime(int sayHelloTime) {
        this.sayHelloTime = sayHelloTime;
    }

    public int getCreateGame() {
        return createGame;
    }

    public void setCreateGame(int createGame) {
        this.createGame = createGame;
    }

    public Map<String, Object> getActorRoomConfig() {
        return actorRoomConfig;
    }

    public void setActorRoomConfig(Map<String, Object> actorRoomConfig) {
        this.actorRoomConfig = actorRoomConfig;
    }

    public int getUploadLog() {
        return uploadLog;
    }

    public void setUploadLog(int uploadLog) {
        this.uploadLog = uploadLog;
    }

    public int getLowModels() {
        return lowModels;
    }

    public void setLowModels(int lowModels) {
        this.lowModels = lowModels;
    }

    public int getNewBgShelfTime() {
        return newBgShelfTime;
    }

    public void setNewBgShelfTime(int newBgShelfTime) {
        this.newBgShelfTime = newBgShelfTime;
    }

    public String getActivityRoomPolicy() {
        return activityRoomPolicy;
    }

    public void setActivityRoomPolicy(String activityRoomPolicy) {
        this.activityRoomPolicy = activityRoomPolicy;
    }

    public RocketInfoVO getRocketInfo() {
        return rocketInfo;
    }

    public void setRocketInfo(RocketInfoVO rocketInfo) {
        this.rocketInfo = rocketInfo;
    }

    public RoomEventVO getRoomEvent() {
        return roomEvent;
    }

    public void setRoomEvent(RoomEventVO roomEvent) {
        this.roomEvent = roomEvent;
    }

    public int getRepeatMsgCost() {
        return repeatMsgCost;
    }

    public void setRepeatMsgCost(int repeatMsgCost) {
        this.repeatMsgCost = repeatMsgCost;
    }

    public int getRoomMode() {
        return roomMode;
    }

    public void setRoomMode(int roomMode) {
        this.roomMode = roomMode;
    }

    public String getHotRank() {
        return hotRank;
    }

    public void setHotRank(String hotRank) {
        this.hotRank = hotRank;
    }

    public List<String> getDevoteUserList() {
        return devoteUserList;
    }

    public void setDevoteUserList(List<String> devoteUserList) {
        this.devoteUserList = devoteUserList;
    }

    public int getLiveRoomRequirement() {
        return liveRoomRequirement;
    }

    public void setLiveRoomRequirement(int liveRoomRequirement) {
        this.liveRoomRequirement = liveRoomRequirement;
    }

    public long getRoomLikesNum() {
        return roomLikesNum;
    }

    public void setRoomLikesNum(long roomLikesNum) {
        this.roomLikesNum = roomLikesNum;
    }

    public int getRoomLevel() {
        return roomLevel;
    }

    public void setRoomLevel(int roomLevel) {
        this.roomLevel = roomLevel;
    }

    public List<String> getLikeIconUrlList() {
        return likeIconUrlList;
    }

    public void setLikeIconUrlList(List<String> likeIconUrlList) {
        this.likeIconUrlList = likeIconUrlList;
    }

    public List<String> getWelcomeWord() {
        return welcomeWord;
    }

    public void setWelcomeWord(List<String> welcomeWord) {
        this.welcomeWord = welcomeWord;
    }

    public RoomRocketV2VO getRocketInfoV2() {
        return rocketInfoV2;
    }

    public void setRocketInfoV2(RoomRocketV2VO rocketInfoV2) {
        this.rocketInfoV2 = rocketInfoV2;
    }

    public RoomGameConfigVO getRoomGameConfig() {
        return roomGameConfig;
    }

    public void setRoomGameConfig(RoomGameConfigVO roomGameConfig) {
        this.roomGameConfig = roomGameConfig;
    }

    public TruthOrDareV2VO getTruthOrDareGameInfo() {
        return truthOrDareGameInfo;
    }

    public void setTruthOrDareGameInfo(TruthOrDareV2VO truthOrDareGameInfo) {
        this.truthOrDareGameInfo = truthOrDareGameInfo;
    }

    public SudGameSimpleInfo getSudGameInfo() {
        return sudGameInfo;
    }

    public void setSudGameInfo(SudGameSimpleInfo sudGameInfo) {
        this.sudGameInfo = sudGameInfo;
    }
}
