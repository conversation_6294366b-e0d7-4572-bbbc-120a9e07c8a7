package com.quhong.data.vo;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * <AUTHOR>
 * @date 2022/9/22
 */
public class BubbleGuideVO {

    private String name;

    private String head;

    @J<PERSON><PERSON>ield(name = "bubble_id")
    private Integer bubbleId;

    @JSO<PERSON>ield(name = "bubble_url")
    private String bubbleUrl;

    @JSONField(name = "price_1")
    private Integer price1;

    @JSONField(name = "price_30")
    private Integer price30;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public Integer getBubbleId() {
        return bubbleId;
    }

    public void setBubbleId(Integer bubbleId) {
        this.bubbleId = bubbleId;
    }

    public String getBubbleUrl() {
        return bubbleUrl;
    }

    public void setBubbleUrl(String bubbleUrl) {
        this.bubbleUrl = bubbleUrl;
    }

    public Integer getPrice1() {
        return price1;
    }

    public void setPrice1(Integer price1) {
        this.price1 = price1;
    }

    public Integer getPrice30() {
        return price30;
    }

    public void setPrice30(Integer price30) {
        this.price30 = price30;
    }

    @Override
    public String toString() {
        return "BubbleGuideVO{" +
                "name='" + name + '\'' +
                ", head='" + head + '\'' +
                ", bubbleId=" + bubbleId +
                ", bubbleUrl='" + bubbleUrl + '\'' +
                ", price1=" + price1 +
                ", price30=" + price30 +
                '}';
    }
}
