package com.quhong.data.vo;



import java.util.List;

public class RewardInfoVO {
    String day;
    int loop;
    int hit_type;
    List<TopThreeBeanVO> top3_list;
    String msg;
    int win_status; // 0 没加入且没有中， 1加入且中 2 加入不中
    int win_beans;

    public String getDay() {
        return day;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public int getLoop() {
        return loop;
    }

    public void setLoop(int loop) {
        this.loop = loop;
    }

    public int getHit_type() {
        return hit_type;
    }

    public void setHit_type(int hit_type) {
        this.hit_type = hit_type;
    }

    public List<TopThreeBeanVO> getTop3_list() {
        return top3_list;
    }

    public void setTop3_list(List<TopThreeBeanVO> top3_list) {
        this.top3_list = top3_list;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getWin_status() {
        return win_status;
    }

    public void setWin_status(int win_status) {
        this.win_status = win_status;
    }

    public int getWin_beans() {
        return win_beans;
    }

    public void setWin_beans(int win_beans) {
        this.win_beans = win_beans;
    }

    public static class TopThreeBeanVO {
        private String uid;
        private String name;
        private String beans;
        private int rank;
        private String head;

        public TopThreeBeanVO(String uid, String name, String beans, int rank, String head) {
            this.uid = uid;
            this.name = name;
            this.beans = beans;
            this.rank = rank;
            this.head = head;
        }

        public TopThreeBeanVO() {
        }

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getBeans() {
            return beans;
        }

        public void setBeans(String beans) {
            this.beans = beans;
        }

        public int getRank() {
            return rank;
        }

        public void setRank(int rank) {
            this.rank = rank;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        @Override
        public String toString() {
            return "TopThreeBean{" +
                    "uid='" + uid + '\'' +
                    ", name='" + name + '\'' +
                    ", beans='" + beans + '\'' +
                    ", rank=" + rank +
                    ", head='" + head + '\'' +
                    '}';
        }
    }

}
