package com.quhong.data.vo;


import java.util.ArrayList;
import java.util.List;

public class ApplyMicVO {

    private List<ApplyActorVO> list = new ArrayList<>();
    private String nextUrl;
    private int applyCount; // 申请数量

    /**
     * 用户头像、昵称、VIP勋章、等级、性别年龄、佩戴勋章、VIP头像框
     */
    public static class ApplyActorVO {
        private String aid;
        private String name;
        private String head;
        private String micFrame;
        private int vipLevel;
        private String vipMedal;
        private int userLevel;
        private int gender;
        private int age;
        private List<String> badgeList;

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public String getMicFrame() {
            return micFrame;
        }

        public void setMicFrame(String micFrame) {
            this.micFrame = micFrame;
        }

        public int getVipLevel() {
            return vipLevel;
        }

        public void setVipLevel(int vipLevel) {
            this.vipLevel = vipLevel;
        }

        public String getVipMedal() {
            return vipMedal;
        }

        public void setVipMedal(String vipMedal) {
            this.vipMedal = vipMedal;
        }

        public int getUserLevel() {
            return userLevel;
        }

        public void setUserLevel(int userLevel) {
            this.userLevel = userLevel;
        }

        public int getGender() {
            return gender;
        }

        public void setGender(int gender) {
            this.gender = gender;
        }

        public List<String> getBadgeList() {
            return badgeList;
        }

        public void setBadgeList(List<String> badgeList) {
            this.badgeList = badgeList;
        }

        public int getAge() {
            return age;
        }

        public void setAge(int age) {
            this.age = age;
        }
    }

    public List<ApplyActorVO> getList() {
        return list;
    }

    public void setList(List<ApplyActorVO> list) {
        this.list = list;
    }

    public String getNextUrl() {
        return nextUrl;
    }

    public void setNextUrl(String nextUrl) {
        this.nextUrl = nextUrl;
    }

    public int getApplyCount() {
        return applyCount;
    }

    public void setApplyCount(int applyCount) {
        this.applyCount = applyCount;
    }
}
