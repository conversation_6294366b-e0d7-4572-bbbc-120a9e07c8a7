package com.quhong.data.vo;

import com.quhong.data.UserBasicInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/13
 */
public class FriendDetailVO {

    /**
     * 下一页页数
     */
    private String nextUrl;

    /**
     * 新好友数量
     */
    private int new_friends;

    private List<FriendDetailVO.FriendInfo> list;

    public static class FriendInfo extends UserBasicInfo {

        private int os;
        private int login_status;
        private int accept_talk;
        private int vchat_status;
        private String roomId;
        private int sstatus;
        private int identify;

        public int getOs() {
            return os;
        }

        public void setOs(int os) {
            this.os = os;
        }

        public int getLogin_status() {
            return login_status;
        }

        public void setLogin_status(int login_status) {
            this.login_status = login_status;
        }

        public int getAccept_talk() {
            return accept_talk;
        }

        public void setAccept_talk(int accept_talk) {
            this.accept_talk = accept_talk;
        }

        public int getVchat_status() {
            return vchat_status;
        }

        public void setVchat_status(int vchat_status) {
            this.vchat_status = vchat_status;
        }

        public String getRoomId() {
            return roomId;
        }

        public void setRoomId(String roomId) {
            this.roomId = roomId;
        }

        public int getSstatus() {
            return sstatus;
        }

        public void setSstatus(int sstatus) {
            this.sstatus = sstatus;
        }

        public int getIdentify() {
            return identify;
        }

        public void setIdentify(int identify) {
            this.identify = identify;
        }
    }

    public String getNextUrl() {
        return nextUrl;
    }

    public void setNextUrl(String nextUrl) {
        this.nextUrl = nextUrl;
    }

    public void setList(List<FriendInfo> list) {
        this.list = list;
    }

    public List<FriendInfo> getList() {
        return list;
    }

    public int getNew_friends() {
        return new_friends;
    }

    public void setNew_friends(int new_friends) {
        this.new_friends = new_friends;
    }
}
