package com.quhong.data.vo;


import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.vo.PageVO;

import java.util.List;

public class HappySaudiVO extends OtherRankConfigVO {
    private Integer totalDiamond;// 总的发送钻石数
    private Integer travelLevel; // 旅游等级 0-5
    private int chanceNum; // 剩余抽奖次数
    private Integer shareCount; // 分享次数
    private List<DrawUserVO> drawUserList; // 抽奖用户列表(大R)
    private List<LuckyOneVO> luckyOneList; // 幸运玩家列表
    private List<DrawUserVO> recentSendGiftList; // 最近发送礼物列表
    private List<String> shareHeadList; // 分享头像列表


    private PageVO<FriendVO> pageVO; // 朋友列表记录


    public static class LuckyOneVO extends ResourceKeyConfigData.ResourceMeta {
        // 幸运玩家栏
        private String dayStr; // 抽奖日期
        private String luckyUserId; // 幸运中奖用户id
        private String luckyUserIcon; // 幸运中奖用户头像
        private Integer todayDiamond; // 当日发送钻石数
        private Integer dayCount; // 当日可抽奖用户人数

        // 抽奖弹窗
        private Integer chanceNum; // 剩余抽奖次数
        private List<DrawUserVO> drawUserList; // 抽奖用户列表

        public Integer getTodayDiamond() {
            return todayDiamond;
        }

        public void setTodayDiamond(Integer todayDiamond) {
            this.todayDiamond = todayDiamond;
        }

        public String getLuckyUserId() {
            return luckyUserId;
        }

        public void setLuckyUserId(String luckyUserId) {
            this.luckyUserId = luckyUserId;
        }

        public String getLuckyUserIcon() {
            return luckyUserIcon;
        }

        public void setLuckyUserIcon(String luckyUserIcon) {
            this.luckyUserIcon = luckyUserIcon;
        }

        public String getDayStr() {
            return dayStr;
        }

        public void setDayStr(String dayStr) {
            this.dayStr = dayStr;
        }

        public Integer getDayCount() {
            return dayCount;
        }

        public void setDayCount(Integer dayCount) {
            this.dayCount = dayCount;
        }

        public Integer getChanceNum() {
            return chanceNum;
        }

        public void setChanceNum(Integer chanceNum) {
            this.chanceNum = chanceNum;
        }

        public List<DrawUserVO> getDrawUserList() {
            return drawUserList;
        }

        public void setDrawUserList(List<DrawUserVO> drawUserList) {
            this.drawUserList = drawUserList;
        }
    }

    public static class DrawUserVO {
        private String aid;
        private String head;
        private Integer drawCount; // 抽奖次数

        // 轮播用
        private Integer sendCount; // 发送数量
        private String giftIcon; // 礼物图标

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public Integer getDrawCount() {
            return drawCount;
        }

        public void setDrawCount(Integer drawCount) {
            this.drawCount = drawCount;
        }

        public Integer getSendCount() {
            return sendCount;
        }

        public void setSendCount(Integer sendCount) {
            this.sendCount = sendCount;
        }

        public String getGiftIcon() {
            return giftIcon;
        }

        public void setGiftIcon(String giftIcon) {
            this.giftIcon = giftIcon;
        }
    }

    public static class FriendVO {
        private String uid;
        private Integer status; // 0 未分享 1 已分享
        private String userRid;
        private String userName;
        private String userHead;

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public String getUserRid() {
            return userRid;
        }

        public void setUserRid(String userRid) {
            this.userRid = userRid;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public String getUserHead() {
            return userHead;
        }

        public void setUserHead(String userHead) {
            this.userHead = userHead;
        }
    }

    public Integer getTotalDiamond() {
        return totalDiamond;
    }

    public void setTotalDiamond(Integer totalDiamond) {
        this.totalDiamond = totalDiamond;
    }

    public Integer getTravelLevel() {
        return travelLevel;
    }

    public void setTravelLevel(Integer travelLevel) {
        this.travelLevel = travelLevel;
    }

    public int getChanceNum() {
        return chanceNum;
    }

    public void setChanceNum(int chanceNum) {
        this.chanceNum = chanceNum;
    }

    public List<DrawUserVO> getDrawUserList() {
        return drawUserList;
    }

    public void setDrawUserList(List<DrawUserVO> drawUserList) {
        this.drawUserList = drawUserList;
    }

    public Integer getShareCount() {
        return shareCount;
    }

    public void setShareCount(Integer shareCount) {
        this.shareCount = shareCount;
    }

    public List<LuckyOneVO> getLuckyOneList() {
        return luckyOneList;
    }

    public void setLuckyOneList(List<LuckyOneVO> luckyOneList) {
        this.luckyOneList = luckyOneList;
    }

    public PageVO<FriendVO> getPageVO() {
        return pageVO;
    }

    public void setPageVO(PageVO<FriendVO> pageVO) {
        this.pageVO = pageVO;
    }

    public List<DrawUserVO> getRecentSendGiftList() {
        return recentSendGiftList;
    }

    public void setRecentSendGiftList(List<DrawUserVO> recentSendGiftList) {
        this.recentSendGiftList = recentSendGiftList;
    }

    public List<String> getShareHeadList() {
        return shareHeadList;
    }

    public void setShareHeadList(List<String> shareHeadList) {
        this.shareHeadList = shareHeadList;
    }
}
