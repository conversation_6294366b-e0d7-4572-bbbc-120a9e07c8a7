package com.quhong.data.vo;


import com.quhong.mongo.data.ResourceKeyConfigData;

import java.util.List;

public class Eid2025VO extends OtherRankConfigVO {

    private int catchSleepNum; // 抓羊数量

    private int snapchatState; // 0 未完成 1 已完成

    private int chargeState;   //  0 未完成 1 已完成

    private int catchSleepChance;   //  剩余的抓羊机会

    public static class ResourceMetaTmp extends ResourceKeyConfigData.ResourceMeta {
        private int awardNum; // 弹窗用，资源抽中个数

        private int sheepType; // 历史记录用，羊的类型，0普通 1幸运
        private int ctime; // 历史记录用，抽奖时间


        public int getAwardNum() {
            return awardNum;
        }

        public void setAwardNum(int awardNum) {
            this.awardNum = awardNum;
        }

        public int getSheepType() {
            return sheepType;
        }

        public void setSheepType(int sheepType) {
            this.sheepType = sheepType;
        }

        public int getCtime() {
            return ctime;
        }

        public void setCtime(int ctime) {
            this.ctime = ctime;
        }

    }

    public static class Eid2025HistoryVO {
        private int nextPage;
        private List<ResourceMetaTmp> myHistoryList;

        public int getNextPage() {
            return nextPage;
        }

        public void setNextPage(int nextPage) {
            this.nextPage = nextPage;
        }

        public List<ResourceMetaTmp> getMyHistoryList() {
            return myHistoryList;
        }

        public void setMyHistoryList(List<ResourceMetaTmp> myHistoryList) {
            this.myHistoryList = myHistoryList;
        }
    }

    public static class Eid2025DrawVO {
        private int drawNum;
        private List<ResourceMetaTmp> myDrawyList;

        public int getDrawNum() {
            return drawNum;
        }

        public void setDrawNum(int drawNum) {
            this.drawNum = drawNum;
        }

        public List<ResourceMetaTmp> getMyDrawyList() {
            return myDrawyList;
        }

        public void setMyDrawyList(List<ResourceMetaTmp> myDrawyList) {
            this.myDrawyList = myDrawyList;
        }
    }


    public int getCatchSleepNum() {
        return catchSleepNum;
    }

    public void setCatchSleepNum(int catchSleepNum) {
        this.catchSleepNum = catchSleepNum;
    }

    public int getSnapchatState() {
        return snapchatState;
    }

    public void setSnapchatState(int snapchatState) {
        this.snapchatState = snapchatState;
    }

    public int getChargeState() {
        return chargeState;
    }

    public void setChargeState(int chargeState) {
        this.chargeState = chargeState;
    }

    public int getCatchSleepChance() {
        return catchSleepChance;
    }

    public void setCatchSleepChance(int catchSleepChance) {
        this.catchSleepChance = catchSleepChance;
    }
}
