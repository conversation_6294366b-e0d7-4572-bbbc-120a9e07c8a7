package com.quhong.data.vo;


import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.vo.PageVO;

import java.util.List;

public class FirstKingdomVO extends OtherRankConfigVO {
    private Integer teamType;// 阵营类型 1钢铁王国 2自然王国 3自由王国 0 未选择
    private Integer kingLevel; // 王国等级 0-3
    private Integer chanceNum; // 剩余可用锤子数
    private Integer kingMemberCount; // 我的王国成员数
    private Integer isGetLevel3Reward; // 是否已领取等级3奖励 0未领取 1已领取
    private Integer myUpdateLevelNum; // 我的升级次数
    private Integer myAttackNum; // 我的进攻所消耗的锤子数
    private List<TeamAttackVO> teamAttackList; // 阵营之间进攻信息列表
    private String name; // 用户名
    private String head; // 用户头像
    private List<ResourceMetaTmp> rollRecordList; // 滚屏列表

    private List<ResourceMetaTmp> awardList; // 结果弹窗奖励列表

    private List<TeamInfoListVO> teamInfoList;// 阵营信息列表，按繁荣度
    private  Integer teamInfoListIndex; // 我加入的阵营在信息列表索引
    private List<OtherRankingListVO> attackRankingList; // 进攻榜单
    private List<OtherRankingListVO> buildRankingList; // 建设榜单


    public static class ResourceMetaTmp extends ResourceKeyConfigData.ResourceMeta {
        // 弹窗用
        private Integer awardNum; // 弹窗用，资源抽中个数

        // 我的抽奖记录
        private Integer drawType; // 历史记录，滚屏共用，抽奖类型，1 建造本国堡垒 2 攻击他国堡垒
        private Integer costNum; //  消耗的锤子数
        private Integer ctime; // 历史记录用，抽奖时间

        // 滚屏
        private String aid;
        private String name; // 滚屏的名字
        private Integer socre; //  贡献的繁荣度

        public Integer getAwardNum() {
            return awardNum;
        }

        public void setAwardNum(Integer awardNum) {
            this.awardNum = awardNum;
        }

        public Integer getDrawType() {
            return drawType;
        }

        public void setDrawType(Integer drawType) {
            this.drawType = drawType;
        }

        public Integer getCtime() {
            return ctime;
        }

        public void setCtime(Integer ctime) {
            this.ctime = ctime;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getSocre() {
            return socre;
        }

        public void setSocre(Integer socre) {
            this.socre = socre;
        }

        public Integer getCostNum() {
            return costNum;
        }

        public void setCostNum(Integer costNum) {
            this.costNum = costNum;
        }

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }
    }

    public static class TeamInfoListVO{
        private Integer teamType;
        private Integer score;
        private List<OtherRankingListVO> rankingList;
        private OtherRankingListVO myRank;

        public Integer getTeamType() {
            return teamType;
        }

        public void setTeamType(Integer teamType) {
            this.teamType = teamType;
        }

        public Integer getScore() {
            return score;
        }

        public void setScore(Integer score) {
            this.score = score;
        }

        public List<OtherRankingListVO> getRankingList() {
            return rankingList;
        }

        public void setRankingList(List<OtherRankingListVO> rankingList) {
            this.rankingList = rankingList;
        }

        public OtherRankingListVO getMyRank() {
            return myRank;
        }

        public void setMyRank(OtherRankingListVO myRank) {
            this.myRank = myRank;
        }
    }

    public static class TeamAttackVO{
        private Integer teamType; // 当前阵营类型
        private Integer score; // 当前王国的繁荣度
        private Integer myAttackCost; // 我的王国攻击当前王国消耗的锤子数
        private Integer myDefendCost; // 当前王国攻击我的王国消耗的锤子数

        public Integer getTeamType() {
            return teamType;
        }

        public void setTeamType(Integer teamType) {
            this.teamType = teamType;
        }

        public Integer getScore() {
            return score;
        }

        public void setScore(Integer score) {
            this.score = score;
        }

        public Integer getMyAttackCost() {
            return myAttackCost;
        }

        public void setMyAttackCost(Integer myAttackCost) {
            this.myAttackCost = myAttackCost;
        }

        public Integer getMyDefendCost() {
            return myDefendCost;
        }

        public void setMyDefendCost(Integer myDefendCost) {
            this.myDefendCost = myDefendCost;
        }
    }

    public Integer getTeamType() {
        return teamType;
    }

    public void setTeamType(Integer teamType) {
        this.teamType = teamType;
    }

    public Integer getKingLevel() {
        return kingLevel;
    }

    public void setKingLevel(Integer kingLevel) {
        this.kingLevel = kingLevel;
    }

    public Integer getChanceNum() {
        return chanceNum;
    }

    public void setChanceNum(Integer chanceNum) {
        this.chanceNum = chanceNum;
    }

    public Integer getKingMemberCount() {
        return kingMemberCount;
    }

    public void setKingMemberCount(Integer kingMemberCount) {
        this.kingMemberCount = kingMemberCount;
    }

    public Integer getIsGetLevel3Reward() {
        return isGetLevel3Reward;
    }

    public void setIsGetLevel3Reward(Integer isGetLevel3Reward) {
        this.isGetLevel3Reward = isGetLevel3Reward;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public List<ResourceMetaTmp> getAwardList() {
        return awardList;
    }

    public void setAwardList(List<ResourceMetaTmp> awardList) {
        this.awardList = awardList;
    }

    public List<ResourceMetaTmp> getRollRecordList() {
        return rollRecordList;
    }

    public void setRollRecordList(List<ResourceMetaTmp> rollRecordList) {
        this.rollRecordList = rollRecordList;
    }


    public Integer getMyUpdateLevelNum() {
        return myUpdateLevelNum;
    }

    public void setMyUpdateLevelNum(Integer myUpdateLevelNum) {
        this.myUpdateLevelNum = myUpdateLevelNum;
    }

    public Integer getMyAttackNum() {
        return myAttackNum;
    }

    public void setMyAttackNum(Integer myAttackNum) {
        this.myAttackNum = myAttackNum;
    }

    public List<TeamAttackVO> getTeamAttackList() {
        return teamAttackList;
    }

    public void setTeamAttackList(List<TeamAttackVO> teamAttackList) {
        this.teamAttackList = teamAttackList;
    }

    public Integer getTeamInfoListIndex() {
        return teamInfoListIndex;
    }

    public void setTeamInfoListIndex(Integer teamInfoListIndex) {
        this.teamInfoListIndex = teamInfoListIndex;
    }

    public List<TeamInfoListVO> getTeamInfoList() {
        return teamInfoList;
    }

    public void setTeamInfoList(List<TeamInfoListVO> teamInfoList) {
        this.teamInfoList = teamInfoList;
    }

    public List<OtherRankingListVO> getAttackRankingList() {
        return attackRankingList;
    }

    public void setAttackRankingList(List<OtherRankingListVO> attackRankingList) {
        this.attackRankingList = attackRankingList;
    }

    public List<OtherRankingListVO> getBuildRankingList() {
        return buildRankingList;
    }

    public void setBuildRankingList(List<OtherRankingListVO> buildRankingList) {
        this.buildRankingList = buildRankingList;
    }
}
