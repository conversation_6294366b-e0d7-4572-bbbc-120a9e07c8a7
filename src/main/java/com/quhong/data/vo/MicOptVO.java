package com.quhong.data.vo;

import com.quhong.data.RspRoomMicData;

import java.util.List;

public class MicOptVO {

    private int mic_version;
    private int muteAll;
    private List<RspRoomMicData> mic_list;
    private Integer micThemeId;

    public MicOptVO() {
    }

    public MicOptVO(int mic_version, List<RspRoomMicData> mic_list) {
        this.mic_version = mic_version;
        this.mic_list = mic_list;
    }

    public int getMic_version() {
        return mic_version;
    }

    public void setMic_version(int mic_version) {
        this.mic_version = mic_version;
    }

    public int getMuteAll() {
        return muteAll;
    }

    public void setMuteAll(int muteAll) {
        this.muteAll = muteAll;
    }

    public List<RspRoomMicData> getMic_list() {
        return mic_list;
    }

    public void setMic_list(List<RspRoomMicData> mic_list) {
        this.mic_list = mic_list;
    }

    public Integer getMicThemeId() {
        return micThemeId;
    }

    public void setMicThemeId(Integer micThemeId) {
        this.micThemeId = micThemeId;
    }
}
