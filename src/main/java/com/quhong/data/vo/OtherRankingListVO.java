package com.quhong.data.vo;

import com.quhong.data.RidData;

import java.util.List;

public class OtherRankingListVO {
    private String uid;
    private String roomId;
    private String name;
    private String head;
    private Integer score;
    private String scoreStr;
    private Integer rank;
    private Integer vipLevel;
    private List<String> badgeList;
    private String countryFlag;
    private String rankDate;
    private RidData ridData;
    private List<OtherSupportUserVO> supportUserList;
    private List<String> honorTitleIconList; // 荣誉称号阿语图标

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public String getScoreStr() {
        return scoreStr;
    }

    public void setScoreStr(String scoreStr) {
        this.scoreStr = scoreStr;
    }

    public Integer getRank() {
        return rank;
    }

    public void setRank(Integer rank) {
        this.rank = rank;
    }

    public String getCountryFlag() {
        return countryFlag;
    }

    public void setCountryFlag(String countryFlag) {
        this.countryFlag = countryFlag;
    }

    public String getRankDate() {
        return rankDate;
    }

    public void setRankDate(String rankDate) {
        this.rankDate = rankDate;
    }

    public RidData getRidData() {
        return ridData;
    }

    public void setRidData(RidData ridData) {
        this.ridData = ridData;
    }

    public Integer getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(Integer vipLevel) {
        this.vipLevel = vipLevel;
    }

    public List<String> getBadgeList() {
        return badgeList;
    }

    public void setBadgeList(List<String> badgeList) {
        this.badgeList = badgeList;
    }

    public List<OtherSupportUserVO> getSupportUserList() {
        return supportUserList;
    }

    public void setSupportUserList(List<OtherSupportUserVO> supportUserList) {
        this.supportUserList = supportUserList;
    }

    public List<String> getHonorTitleIconList() {
        return honorTitleIconList;
    }

    public void setHonorTitleIconList(List<String> honorTitleIconList) {
        this.honorTitleIconList = honorTitleIconList;
    }
}
