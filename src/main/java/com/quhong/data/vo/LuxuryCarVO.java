package com.quhong.data.vo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/13
 */
public class LuxuryCarVO {

    private List<Task> taskList;

    public LuxuryCarVO() {
    }

    public LuxuryCarVO(List<Task> taskList) {
        this.taskList = taskList;
    }

    public static class Task {
        private int level; // 等级
        private int curValue; // 当前值
        private int limit; // 任务目标
        private int status; // 0未解锁 1已解锁
        private String rewardKey; // 资源key

        public Task() {
        }

        public Task(int level, int limit, String rewardKey) {
            this.level = level;
            this.limit = limit;
            this.rewardKey = rewardKey;
        }

        public int getLevel() {
            return level;
        }

        public void setLevel(int level) {
            this.level = level;
        }

        public int getCurValue() {
            return curValue;
        }

        public void setCurValue(int curValue) {
            this.curValue = curValue;
        }

        public int getLimit() {
            return limit;
        }

        public void setLimit(int limit) {
            this.limit = limit;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public String getRewardKey() {
            return rewardKey;
        }

        public void setRewardKey(String rewardKey) {
            this.rewardKey = rewardKey;
        }
    }

    public List<Task> getTaskList() {
        return taskList;
    }

    public void setTaskList(List<Task> taskList) {
        this.taskList = taskList;
    }
}
