package com.quhong.data.vo;


import com.quhong.mysql.dao.LuckyGiftRewardDao;
import com.quhong.mysql.data.LuckyGiftRewardData;

import java.util.List;

public class JackpotLuckyGiftInfoVO {
    private List<String> luckyGiftList; // 幸运礼物图标列表
    private List<LuckyGiftRewardData> winningUsers; // 最近中奖用户
    private List<LuckyGiftRewardDao.RankInfo> luckyUsers; // 最近中大奖用户
    private int jackpot; // 奖池钻石数量

    public List<String> getLuckyGiftList() {
        return luckyGiftList;
    }

    public void setLuckyGiftList(List<String> luckyGiftList) {
        this.luckyGiftList = luckyGiftList;
    }

    public List<LuckyGiftRewardData> getWinningUsers() {
        return winningUsers;
    }

    public void setWinningUsers(List<LuckyGiftRewardData> winningUsers) {
        this.winningUsers = winningUsers;
    }

    public List<LuckyGiftRewardDao.RankInfo> getLuckyUsers() {
        return luckyUsers;
    }

    public void setLuckyUsers(List<LuckyGiftRewardDao.RankInfo> luckyUsers) {
        this.luckyUsers = luckyUsers;
    }

    public int getJackpot() {
        return jackpot;
    }

    public void setJackpot(int jackpot) {
        this.jackpot = jackpot;
    }
}
