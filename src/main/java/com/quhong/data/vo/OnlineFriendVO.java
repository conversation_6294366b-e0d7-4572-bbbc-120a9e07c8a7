package com.quhong.data.vo;

import com.quhong.data.RidData;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/17
 */
public class OnlineFriendVO {

    private int friendNum;

    /**
     * 0在线好友列表 1推荐好友列表 (默认是0)
     */
    private int type;

    private List<FriendInfo> list;

    public static class FriendInfo {

        private String name;
        private String head;
        private Integer rid;
        private RidData ridData;
        private Integer gender;
        private String aid;
        private String inRoomId;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public Integer getRid() {
            return rid;
        }

        public void setRid(Integer rid) {
            this.rid = rid;
        }

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public String getInRoomId() {
            return inRoomId;
        }

        public void setInRoomId(String inRoomId) {
            this.inRoomId = inRoomId;
        }

        public Integer getGender() {
            return gender;
        }

        public void setGender(Integer gender) {
            this.gender = gender;
        }

        public RidData getRidData() {
            return ridData;
        }

        public void setRidData(RidData ridData) {
            this.ridData = ridData;
        }
    }

    public List<FriendInfo> getList() {
        return list;
    }

    public void setList(List<FriendInfo> list) {
        this.list = list;
    }

    public int getFriendNum() {
        return friendNum;
    }

    public void setFriendNum(int friendNum) {
        this.friendNum = friendNum;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}
