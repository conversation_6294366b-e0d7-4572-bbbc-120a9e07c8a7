package com.quhong.data.vo;

import java.util.List;

public class HalloweenVO extends OtherRankConfigVO{

    private String dateStr;    // 日期
    private Integer userTotalCandie;
    private Integer leftBeans;     // 剩余钻石数
    private Integer leftCandie;    // 剩余糖果数
    private Integer totalCandie;   // 总糖果数
    private Integer poolDiamond;   // 当日钻石数量

    // 滚屏记录、抓鬼记录、兑换记录
    private List<PrizeConfigV2VO> recordList;
    private Integer nextUrl;

    // 每日排行榜
    private List<DailyRankConfig> dailyRankConfigList;

    // 榜单
    public static class DailyRankConfig{
        private String dateStr;
        private Integer poolDiamond;
        private List<OtherRankingListVO> rankingListVOList;
        private OtherRankingListVO myRankVO;

        public String getDateStr() {
            return dateStr;
        }

        public void setDateStr(String dateStr) {
            this.dateStr = dateStr;
        }

        public Integer getPoolDiamond() {
            return poolDiamond;
        }

        public void setPoolDiamond(Integer poolDiamond) {
            this.poolDiamond = poolDiamond;
        }

        public List<OtherRankingListVO> getRankingListVOList() {
            return rankingListVOList;
        }

        public void setRankingListVOList(List<OtherRankingListVO> rankingListVOList) {
            this.rankingListVOList = rankingListVOList;
        }

        public OtherRankingListVO getMyRankVO() {
            return myRankVO;
        }

        public void setMyRankVO(OtherRankingListVO myRankVO) {
            this.myRankVO = myRankVO;
        }
    }

    public String getDateStr() {
        return dateStr;
    }

    public void setDateStr(String dateStr) {
        this.dateStr = dateStr;
    }

    public Integer getUserTotalCandie() {
        return userTotalCandie;
    }

    public void setUserTotalCandie(Integer userTotalCandie) {
        this.userTotalCandie = userTotalCandie;
    }

    public Integer getLeftBeans() {
        return leftBeans;
    }

    public void setLeftBeans(Integer leftBeans) {
        this.leftBeans = leftBeans;
    }

    public Integer getLeftCandie() {
        return leftCandie;
    }

    public void setLeftCandie(Integer leftCandie) {
        this.leftCandie = leftCandie;
    }

    public Integer getTotalCandie() {
        return totalCandie;
    }

    public void setTotalCandie(Integer totalCandie) {
        this.totalCandie = totalCandie;
    }

    public Integer getPoolDiamond() {
        return poolDiamond;
    }

    public void setPoolDiamond(Integer poolDiamond) {
        this.poolDiamond = poolDiamond;
    }

    public List<PrizeConfigV2VO> getRecordList() {
        return recordList;
    }

    public void setRecordList(List<PrizeConfigV2VO> recordList) {
        this.recordList = recordList;
    }

    public Integer getNextUrl() {
        return nextUrl;
    }

    public void setNextUrl(Integer nextUrl) {
        this.nextUrl = nextUrl;
    }

    public List<DailyRankConfig> getDailyRankConfigList() {
        return dailyRankConfigList;
    }

    public void setDailyRankConfigList(List<DailyRankConfig> dailyRankConfigList) {
        this.dailyRankConfigList = dailyRankConfigList;
    }
}
