package com.quhong.data.vo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/3
 */
public class CheckpointPageVO {

    /**
     * 当前所在关卡号
     */
    private Integer currentCheckpointNo;

    /**
     * 金币余额
     */
    private Integer coinBalance;

    /**
     * 钻石余额
     */
    private Integer diamondBalance;

    /**
     * 兑换金币商品列表
     */
    private List<CoinProductVO> coinProductList;

    /**
     * 关卡列表
     */
    private List<CheckpointVO> checkpointList;

    public static class CheckpointVO {

        /**
         * 关卡
         */
        private Integer checkpointNo;

        /**
         * 是否解锁 0未解锁 1已解锁
         */
        private Integer unlocked;

        /**
         * 剩余答题次数
         */
        private Integer freeAnswerCount;

        /**
         * 答题总次数
         */
        private Integer answerCount;

        /**
         * 解锁当前关卡的用户数量
         */
        private Integer lockedUserNum;

        public Integer getCheckpointNo() {
            return checkpointNo;
        }

        public void setCheckpointNo(Integer checkpointNo) {
            this.checkpointNo = checkpointNo;
        }

        public Integer getUnlocked() {
            return unlocked;
        }

        public void setUnlocked(Integer unlocked) {
            this.unlocked = unlocked;
        }

        public Integer getFreeAnswerCount() {
            return freeAnswerCount;
        }

        public void setFreeAnswerCount(Integer freeAnswerCount) {
            this.freeAnswerCount = freeAnswerCount;
        }

        public Integer getLockedUserNum() {
            return lockedUserNum;
        }

        public void setLockedUserNum(Integer lockedUserNum) {
            this.lockedUserNum = lockedUserNum;
        }

        public Integer getAnswerCount() {
            return answerCount;
        }

        public void setAnswerCount(Integer answerCount) {
            this.answerCount = answerCount;
        }
    }

    public Integer getCurrentCheckpointNo() {
        return currentCheckpointNo;
    }

    public void setCurrentCheckpointNo(Integer currentCheckpointNo) {
        this.currentCheckpointNo = currentCheckpointNo;
    }

    public List<CheckpointVO> getCheckpointList() {
        return checkpointList;
    }

    public void setCheckpointList(List<CheckpointVO> checkpointList) {
        this.checkpointList = checkpointList;
    }

    public Integer getCoinBalance() {
        return coinBalance;
    }

    public void setCoinBalance(Integer coinBalance) {
        this.coinBalance = coinBalance;
    }

    public Integer getDiamondBalance() {
        return diamondBalance;
    }

    public void setDiamondBalance(Integer diamondBalance) {
        this.diamondBalance = diamondBalance;
    }

    public List<CoinProductVO> getCoinProductList() {
        return coinProductList;
    }

    public void setCoinProductList(List<CoinProductVO> coinProductList) {
        this.coinProductList = coinProductList;
    }
}
