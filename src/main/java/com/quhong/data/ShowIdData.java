package com.quhong.data;

import com.alibaba.fastjson.JSON;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/16
 */
public class ShowIdData {

    private String fromUid; // 准备登入账号的，注册时为null
    private int fromRid; // 准备登入账号的，注册时为null
    private String showId; // 返回客户端展示的
    private int rid; // 已经与该设备关联账号的
    private String uid; // 已经与该设备关联账号的

    public String getShowId() {
        return showId;
    }

    public void setShowId(String showId) {
        this.showId = showId;
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getFromUid() {
        return fromUid;
    }

    public void setFromUid(String fromUid) {
        this.fromUid = fromUid;
    }

    public int getFromRid() {
        return fromRid;
    }

    public void setFromRid(int fromRid) {
        this.fromRid = fromRid;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
