package com.quhong.constant;

import java.io.Serializable;

public class HttpResult implements Serializable {

    private int code;
    private String msg;
    private Object data;

    public HttpResult() {
    }

    public static HttpResult error(HttpCode httpCode) {
        HttpResult httpResult = new HttpResult();
        httpResult.code = httpCode.getCode();
        httpResult.msg = httpCode.getMsg();
        httpResult.data = null;
        return httpResult;
    }

    public static HttpResult error(String msg) {
        HttpResult httpResult = new HttpResult();
        httpResult.code = 1;
        httpResult.msg = msg;
        httpResult.data = null;
        return httpResult;
    }

    public static HttpResult ok(Object data) {
        HttpResult httpResult = new HttpResult();
        httpResult.code = HttpCode.SUCCESS.getCode();
        httpResult.msg = HttpCode.SUCCESS.getMsg();
        httpResult.data = data;
        return httpResult;
    }

    public static HttpResult ok() {
        HttpResult httpResult = new HttpResult();
        httpResult.code = HttpCode.SUCCESS.getCode();
        httpResult.msg = HttpCode.SUCCESS.getMsg();
        httpResult.data = null;
        return httpResult;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
