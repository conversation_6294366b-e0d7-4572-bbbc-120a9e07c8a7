package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.quhong.mysql.data.CoinProductData;

import com.quhong.mysql.mapper.ustar.CoinProductMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class CoinDao {
    private static final Logger logger = LoggerFactory.getLogger(CoinDao.class);

    @Resource
    private CoinProductMapper mapper;

    public CoinDao(){

    }

    public List<CoinProductData> getCoinsProducts(){
        QueryWrapper<CoinProductData> query = new QueryWrapper<>();
        query.eq("status", 1);

        return mapper.selectList(query);
    }

    public CoinProductData getCoinsProductByPid(int pid){
        QueryWrapper<CoinProductData> query = new QueryWrapper<>();
        query.eq("status", 1);
        query.eq("pid", pid);

        return mapper.selectOne(query);
    }

}
