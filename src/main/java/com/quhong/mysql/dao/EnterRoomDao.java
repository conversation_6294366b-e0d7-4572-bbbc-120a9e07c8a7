package com.quhong.mysql.dao;

import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.data.EnterRoomData;
import com.quhong.mysql.mapper.ustar_log.EnterRoomMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Component
public class EnterRoomDao extends MonthShardingDao<EnterRoomMapper> {

    private static final Logger logger = LoggerFactory.getLogger(EnterRoomDao.class);

    @Autowired
    private EnterRoomMapper enterRoomMapper;

    public EnterRoomDao() {
        super("s_enter_room");
    }

    /**
     * 获取进房历史人数
     *
     * @param startTime
     * @param endTime
     * @param os
     * @return
     */
    public List<String> joinRoomUserNum(int startTime, int endTime, Integer os) {
        List<String> tableSuffixArr = DateHelper.ARABIAN.getTableSuffixList(startTime, endTime);
        List<String> tableSuffixList = new ArrayList<>();
        for (String tableSuffix : tableSuffixArr) {
            if (checkExist(tableSuffix)) {
                tableSuffixList.add(tableSuffix);
            }
        }
        if (CollectionUtils.isEmpty(tableSuffixList)) {
            return new ArrayList<>();
        }
        return enterRoomMapper.joinRoomUserList(tableSuffixList, startTime, endTime, os);
    }

    /**
     * 更新在线时间
     *
     * @param uid
     * @param roomId
     * @param onlineTime
     */
    public int updateOnlineTime(String uid, String roomId, int onlineTime, int enterTime) {
        String suffix = DateHelper.ARABIAN.getTableSuffix(DateHelper.formatDate(enterTime));
        createTable(suffix);
        return enterRoomMapper.updateOnlineTime(suffix, uid, roomId, onlineTime, enterTime, DateHelper.getNowSeconds());
    }

    public EnterRoomData selectEnterRoomRecord(String uid, String roomId, int enterTime) {
        String suffix = DateHelper.ARABIAN.getTableSuffix(DateHelper.formatDate(enterTime));
        createTable(suffix);
        return enterRoomMapper.selectEnterRoomRecord(suffix, uid, roomId, enterTime);
    }

    public void insert(EnterRoomData data) {
        String suffix = DateHelper.ARABIAN.getTableSuffix(DateHelper.formatDate(data.getCtime()));
        createTable(suffix);
        enterRoomMapper.insert(suffix, data);
    }
}
