package com.quhong.mysql.mapper.ustar;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.data.vo.RewardListVO;
import com.quhong.mysql.data.MomentRewardData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


public interface MomentRewardMapper extends BaseMapper<MomentRewardData> {
    @Select("SELECT " +
            " t1.* " +
            "FROM " +
            " t_moment_reward t1 " +
            " JOIN ( SELECT uid, MAX( ctime ) AS max_ctime FROM t_moment_reward WHERE mid = #{mid} GROUP BY uid ) t2 ON t1.uid = t2.uid " +
            " AND t1.ctime = t2.max_ctime " +
            "WHERE " +
            " t1.mid = #{mid} " +
            "ORDER BY " +
            " t1.ctime DESC " +
            " LIMIT 3")
    List<MomentRewardData> getLastSendList(@Param("mid") String mid);

    @Deprecated
    @Select("SELECT uid as aid, sum(reward_num) as rewardNum FROM `t_moment_reward` WHERE mid=#{mid} GROUP BY uid order by rewardNum desc LIMIT #{offset},#{pageSize}")
    List<RewardListVO.GiftRewardVO> getRewardListByValue(@Param("mid") String mid, @Param("offset") int offset, @Param("pageSize") int pageSize);

    @Select("SELECT uid as aid, reward_num as rewardNum, gift_icon as giftIcon,reward_price as giftPrice FROM `t_moment_reward` WHERE mid=#{mid} order by id desc LIMIT #{offset},#{pageSize}")
    List<RewardListVO.GiftRewardVO> getRewardList(@Param("mid") String mid, @Param("offset") int offset, @Param("pageSize") int pageSize);

}
