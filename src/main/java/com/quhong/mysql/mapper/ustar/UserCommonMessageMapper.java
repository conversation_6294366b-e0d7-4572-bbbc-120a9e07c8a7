package com.quhong.mysql.mapper.ustar;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.mysql.data.UserCommonMessageData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * <AUTHOR>
 * @date 2022/6/9
 */
public interface UserCommonMessageMapper extends BaseMapper<UserCommonMessageData> {

    @Update("update t_user_common_message set status=#{status} where uid=#{uid} and aid=#{aid} and ntype=#{ntype}")
    void updateMessageStatus(@Param("uid") String uid, @Param("aid") String aid, @Param("ntype") Integer ntype, @Param("status") Integer status);
}
