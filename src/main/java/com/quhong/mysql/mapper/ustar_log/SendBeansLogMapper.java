package com.quhong.mysql.mapper.ustar_log;

import com.quhong.operation.share.mysql.SendBeanLog;
import com.quhong.operation.share.mysql.SendBeansStatData;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/17
 */
public interface SendBeansLogMapper {

    List<SendBeanLog> selectByUid (@Param("uid") String uid);

    Integer insertSendBeanLog (SendBeanLog log);

    /**
     * 通过发钻成功次数获取统计数据
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @param successNum 成功次数
     * @return
     */
    List<SendBeansStatData> selectStatListBySuccessNum(@Param("startTime") int startTime,
                                                       @Param("endTime") int endTime,
                                                       @Param("successNum") int successNum);

    /**
     * 查询发钻失败的统计数据
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @param failedNum 失败次数
     * @return
     */
    List<SendBeansStatData> selectFailStatList(@Param("startTime") int startTime,
                                               @Param("endTime") int endTime,
                                               @Param("failedNum") int failedNum);

}
