package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.MoneyDetail;
import com.quhong.mysql.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;


public interface MoneyDetailMapper extends ShardingMapper {

    @Insert("insert into t_money_detail_${tableSuffix} (`id`,`uid`,`changed`,`balance`,`title`,`desc`,`atype`,`mtime`,`ctime`) values (#{item.id},#{item.uid},#{item.changed},#{item.balance},#{item.title},#{item.desc},#{item.atype},#{item.mtime},#{item.ctime})")
    void insert(@Param("tableSuffix") String tableSuffix, @Param("item") MoneyDetail moneyDetail);
}
