package com.quhong.mysql.data;

/**
 * MySQL流水对象
 */
public class MoneyDetailStatData {

    // 唯一id，必填
//    private String id;
    // 用户uid
//    private String uid;
    // 记录钻石
    private Integer changed;
    // 当前余额钻石
//    private Long balance;
    // 记录标题
    private String title;
    // 记录描述
    private String desc;
    // 记录类型
//    private Integer atype;
    // 流水时间，需要设置
    private Integer mtime;
    // 记录生成时间戳，单位:秒（自动生成）
//    private Integer ctime;


    public Integer getChanged() {
        return changed;
    }

    public void setChanged(Integer changed) {
        this.changed = changed;
    }


    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        return "MoneyDetailStatData{" +
                "changed=" + changed +
                ", title='" + title + '\'' +
                ", desc='" + desc + '\'' +
                ", mtime=" + mtime +
                '}';
    }
}
