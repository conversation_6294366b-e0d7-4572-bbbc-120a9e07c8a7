package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

@TableName("t_google_pay")
public class GooglePayData {

    @TableId(type = IdType.AUTO)
    private Integer rid;
    private Integer gkind; // 1 subscription; 2 product
    @TableField("userId")
    private String userId;
    @TableField("orderId")
    private String orderId;
    private Integer fstatus; // 0 wait check; 1 check succeed; 2 check failed 3 refund
    @TableField("productId")
    private String productId;
    @TableField("purchaseToken")
    private String purchaseToken;
    @TableField("purchaseTimeMillis")
    private Long purchaseTimeMillis;
    @TableField("purchaseState")
    private Integer purchaseState;
    private String payload;
    private String fothers;
    private Date ctime;
    private Date mtime;
    private Integer innerAcc; // 是否为测试账号订单
    private Integer isAc; // 是否为活动购买

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public Integer getGkind() {
        return gkind;
    }

    public void setGkind(Integer gkind) {
        this.gkind = gkind;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Integer getFstatus() {
        return fstatus;
    }

    public void setFstatus(Integer fstatus) {
        this.fstatus = fstatus;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getPurchaseToken() {
        return purchaseToken;
    }

    public void setPurchaseToken(String purchaseToken) {
        this.purchaseToken = purchaseToken;
    }

    public Long getPurchaseTimeMillis() {
        return purchaseTimeMillis;
    }

    public void setPurchaseTimeMillis(Long purchaseTimeMillis) {
        this.purchaseTimeMillis = purchaseTimeMillis;
    }

    public Integer getPurchaseState() {
        return purchaseState;
    }

    public void setPurchaseState(Integer purchaseState) {
        this.purchaseState = purchaseState;
    }

    public String getPayload() {
        return payload;
    }

    public void setPayload(String payload) {
        this.payload = payload;
    }

    public String getFothers() {
        return fothers;
    }

    public void setFothers(String fothers) {
        this.fothers = fothers;
    }

    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }

    public Integer getInnerAcc() {
        return innerAcc;
    }

    public void setInnerAcc(Integer innerAcc) {
        this.innerAcc = innerAcc;
    }

    public Integer getIsAc() {
        return isAc;
    }

    public void setIsAc(Integer isAc) {
        this.isAc = isAc;
    }
}
