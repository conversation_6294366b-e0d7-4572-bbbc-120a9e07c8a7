package com.quhong.mysql.slave_mapper.ustar_log;

import com.quhong.mysql.mapper.ShardingMapper;
import com.quhong.operation.share.data.AggStatData;
import com.quhong.operation.share.mysql.RoomMic;
import com.quhong.operation.share.tool.TotalVO;
import com.quhong.operation.share.vo.RoomMicStatVO;
import com.quhong.operation.share.vo.RoomOnlineStatVO;
import com.quhong.operation.share.vo.UpMicTimeVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/8/3
 */
public interface RoomMicMapper extends ShardingMapper {

    /**
     * 主持人在自己房间麦时长
     *
     * @param tableSuffix 表前缀
     * @param startTime   过滤的开始时间
     * @param endTime     过滤的结束时间
     * @param userId      主持人uid
     * @param roomId      主持人本人房间rid
     * @return 时长
     */
    Integer compereInMicTime(@Param("tableSuffix") String tableSuffix,
                             @Param("startTime") Integer startTime,
                             @Param("endTime") Integer endTime,
                             @Param("userId") String userId,
                             @Param("roomId") String roomId);

    /**
     * 新用户在指定房间的上麦人数
     *
     * @param tableSuffix 表前缀
     * @param startTime   过滤的开始时间
     * @param endTime     过滤的结束时间
     * @param roomId      主持人本人房间rid
     * @return list 集合
     */
    List<String> newActorInMicPerson(@Param("tableSuffix") String tableSuffix,
                                     @Param("startTime") Integer startTime,
                                     @Param("endTime") Integer endTime,
                                     @Param("roomId") String roomId);

    /**
     * 新用户在指定房间的上麦人数
     *
     * @param tableSuffix 表前缀
     * @param startTime   过滤的开始时间
     * @param endTime     过滤的结束时间
     * @param roomId      主持人本人房间rid
     * @return 总数
     */
    Integer newActorInMicCount(@Param("tableSuffix") String tableSuffix,
                               @Param("startTime") Integer startTime,
                               @Param("endTime") Integer endTime,
                               @Param("roomId") String roomId);

    /**
     * 新用户平均上麦时长
     *
     * @param tableSuffix 表前缀
     * @param startTime   过滤的开始时间
     * @param endTime     过滤的结束时间
     * @param roomId      主持人本人房间rid
     * @return 平均时长
     */
    TotalVO newActorInMicTimeAvg(@Param("tableSuffix") String tableSuffix,
                                 @Param("startTime") Integer startTime,
                                 @Param("endTime") Integer endTime,
                                 @Param("roomId") String roomId);

    /**
     * 新用户上麦5分钟以上人数
     *
     * @param tableSuffix 表前缀
     * @param startTime   过滤的开始时间
     * @param endTime     过滤的结束时间
     * @param roomId      主持人本人房间rid
     * @return 人数
     */
    Integer newActorInMicTime5Minute(@Param("tableSuffix") String tableSuffix,
                                     @Param("startTime") Integer startTime,
                                     @Param("endTime") Integer endTime,
                                     @Param("roomId") String roomId);

    /**
     * 某用户时间段内上麦总时长
     *
     * @param tableSuffix 表前缀
     * @param startTime   过滤的开始时间
     * @param endTime     过滤的结束时间
     * @param userId      用户id
     * @return 时长
     */
    Integer actorAddUpMicTime(@Param("tableSuffix") String tableSuffix,
                              @Param("startTime") Integer startTime,
                              @Param("endTime") Integer endTime,
                              @Param("userId") String userId);

    List<UpMicTimeVO> actorAddUpMicTimeByUidSet(@Param("tableSuffixList") List<String> tableSuffixList,
                                                @Param("startTime") Integer startTime,
                                                @Param("endTime") Integer endTime,
                                                @Param("uidSet") Set<String> uidSet);

    /**
     * 某用户时间段内上麦次数
     *
     * @param tableSuffix 表前缀
     * @param startTime   过滤的开始时间
     * @param endTime     过滤的结束时间
     * @param userId      用户id
     * @return 次数
     */
    Integer actorMicCount(@Param("tableSuffix") String tableSuffix,
                          @Param("startTime") Integer startTime,
                          @Param("endTime") Integer endTime,
                          @Param("userId") String userId);

    /**
     * 统计某批用户某天上麦信息
     *
     * @param tableSuffix 表前缀
     * @param startTime   过滤的开始时间
     * @param endTime     过滤的结束时间
     * @param uidList     某批用户
     * @return 统计的信息
     */
    List<TotalVO> upMicTotalInfo(@Param("tableSuffix") String tableSuffix,
                                 @Param("startTime") Integer startTime,
                                 @Param("endTime") Integer endTime,
                                 @Param("roomList") List<String> roomList,
                                 @Param("uidList") List<String> uidList);

    /**
     * 获取（在注册当天，在迎新房）上麦超过或等于五分钟的用户信息
     *
     * @param tableSuffix 表前缀
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param startUid    uid生成时间
     * @param endUid      uid生成时间
     * @param rookie      null:全部；1：迎新房
     * @return 上麦5分钟信息集合
     */
    List<RoomMic> mic5MinutesInfo(@Param("tableSuffix") String tableSuffix,
                                  @Param("startTime") Integer startTime,
                                  @Param("endTime") Integer endTime,
                                  @Param("startUid") String startUid,
                                  @Param("endUid") String endUid,
                                  @Param("rookie") Integer rookie);

    RoomMicStatVO roomMicStat(@Param("tableSuffixList") List<String> tableSuffixList,
                              @Param("startTime") int startTime,
                              @Param("endTime") int endTime,
                              @Param("os") Integer os);

    List<RoomOnlineStatVO> userUpMicStat(@Param("tableSuffixList") List<String> tableSuffixList,
                                         @Param("startTime") Integer startTime, @Param("endTime") Integer endTime,
                                         @Param("os") String os, @Param("userType") String userType);

    List<String> selectGtMicTimeUsers(@Param("tableSuffix") String tableSuffix,
                                      @Param("startTime") int startTime,
                                      @Param("endTime") int endTime,
                                      @Param("uidSet") Set<String> uidSet,
                                      @Param("micGtTime") int micGtTime);

    List<String> micRookieRoomNewUsers(
            @Param("tableSuffix") String tableSuffix,
            @Param("ridSet") Set<String> ridSet,
            @Param("startTime") int startTime,
            @Param("endTime") int endTime
    );

    List<UpMicTimeVO> micRookieRoomNewUsersTime(
            @Param("tableSuffixList") List<String> tableSuffixList,
            @Param("startTime") int startTime,
            @Param("endTime") int endTime,
            @Param("ridSet") Set<String> ridSet,
            @Param("low") int low,
            @Param("hight") int hight
    );

}
