package com.quhong.mysql.slave_mapper.ustar_log;

import com.quhong.operation.share.data.FriendSwipeInfo;
import com.quhong.operation.share.data.FriendSwipeStat;
import com.quhong.operation.share.data.PvData;
import com.quhong.operation.share.mysql.FriendSwipeData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface CrushOpMapper {

    @Select("SELECT uid,type FROM t_friend_swipe_0 where " +
            "ctime >= #{startTime} AND ctime < #{endTime} " +
            "UNION ALL " +
            "SELECT uid,type FROM t_friend_swipe_1 where " +
            "ctime >= #{startTime} AND ctime < #{endTime} " +
            "UNION ALL " +
            "SELECT uid,type FROM t_friend_swipe_2 where " +
            "ctime >= #{startTime} AND ctime < #{endTime} " +
            "UNION ALL " +
            "SELECT uid,type FROM t_friend_swipe_3 where " +
            "ctime >= #{startTime} AND ctime < #{endTime} " +
            "UNION ALL " +
            "SELECT uid,type FROM t_friend_swipe_4 where " +
            "ctime >= #{startTime} AND ctime < #{endTime} " +
            "UNION ALL " +
            "SELECT uid,type FROM t_friend_swipe_5 where " +
            "ctime >= #{startTime} AND ctime < #{endTime} " +
            "UNION ALL " +
            "SELECT uid,type FROM t_friend_swipe_6 where " +
            "ctime >= #{startTime} AND ctime < #{endTime} " +
            "UNION ALL " +
            "SELECT uid,type FROM t_friend_swipe_7 where " +
            "ctime >= #{startTime} AND ctime < #{endTime} " +
            "UNION ALL " +
            "SELECT uid,type FROM t_friend_swipe_8 where " +
            "ctime >= #{startTime} AND ctime < #{endTime} " +
            "UNION ALL " +
            "SELECT uid,type FROM t_friend_swipe_9 where " +
            "ctime >= #{startTime} AND ctime < #{endTime}")
    List<FriendSwipeStat> friendSwipeStat(@Param("startTime") int startTime, @Param("endTime") int endTime);

    List<FriendSwipeInfo> friendSwipeList(@Param("startTime") int startTime, @Param("endTime") int endTim);

    @Select("SELECT uid,count FROM s_crush_uv_${tableSuffix} where date=#{dateStr}")
    List<PvData> crushPvStat(@Param("tableSuffix") String tableSuffix, @Param("dateStr") String dateStr);
}
