package com.quhong.mysql.slave_mapper.ustar_log;

import com.quhong.mysql.data.CountData;
import com.quhong.mysql.data.MoneyDetailStatData;
import com.quhong.mysql.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface MoneyDetailStatMapper extends ShardingMapper {
    /**
     * 获取子表中总共花费的钻石
     * @param uid
     * @param suffixList
     * @return
     */
    Integer getTotalCost(@Param("uid")String uid ,@Param("suffixList") List<String> suffixList);


    @Select({
            "<script>",
            "SELECT title,`desc`,changed,mtime FROM ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT title,`desc`,changed,mtime FROM t_money_detail_${tableSuffix} ",
            "WHERE uid=#{uid} ",
            "</foreach>",
            " ) AS stat",
            "ORDER BY mtime DESC ",
            "LIMIT #{size} ",
            "OFFSET #{start}",
            "</script>"
    })
    List<MoneyDetailStatData> getDetails( @Param("uid") String uid, @Param("start") int start,@Param("size") int size,@Param("suffixList") List<String> suffixList);


}
