package com.quhong.task;

import com.google.gson.JsonObject;
import com.quhong.analysis.DynamicEmojiSendCountEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.config.AsyncConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.dto.DynamicEmojiCount;
import com.quhong.mysql.dao.MysqlMsgRecordDao;
import com.quhong.utils.K8sUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 消息定时任务处理器
 *
 * <AUTHOR>
 * @date 2025/8/5 17:38
 */
@Component
public class MsgTask {
    private static final Logger logger = LoggerFactory.getLogger(MsgTask.class);
    @Resource
    private K8sUtils k8sUtils;
    @Resource
    private MysqlMsgRecordDao mysqlMsgRecordDao;
    @Resource
    private EventReport eventReport;


    /**
     * 定时获取用户前一天动态表情统计埋点，每天utc+3时间5点执行
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "0 0 5 * * ?",zone = "GMT+3")
    public void getDynamicEmojiCount() {
        if (k8sUtils.isMasterFromCache()) {
            List<DynamicEmojiCount> dynamicEmojiCountList = mysqlMsgRecordDao.getDynamicEmojiCount();
            if (CollectionUtils.isEmpty(dynamicEmojiCountList)) {
                logger.info("get dynamic emoji count = 0");
                return;
            }
            for (DynamicEmojiCount dynamicEmojiCount : dynamicEmojiCountList) {
                DynamicEmojiSendCountEvent event = new DynamicEmojiSendCountEvent();
                event.setUid(dynamicEmojiCount.getFromUid());
                event.setScene("special_effects_msg");
                event.setScene_desc("private chat page");
                event.setDate(DateHelper.ARABIAN.getYesterdayStr(new Date()));
                event.setMsg_count(dynamicEmojiCount.getCount());
                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty("ps",510);
                event.setOther_info(jsonObject.toString());
                eventReport.track(new EventDTO(event));
            }
            logger.info("get dynamic emoji count{}",dynamicEmojiCountList.size());
        }
    }
}
