package com.quhong.task.lostandfound.google;

import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.monitor.MonitorSender;
import com.quhong.mysql.data.GooglePayData;
import com.quhong.redis.DataRedisBean;
import com.quhong.service.GooglePayService;
import com.quhong.utils.K8sUtils;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.zip.ZipInputStream;

@Component
public class GoogleSaleReportService {
    private static final Logger logger = LoggerFactory.getLogger(GoogleSaleReportService.class);
    private Set<String> LOST_SET = new HashSet<>();
    private static final int PAY_STATUS_REFUND = 3;

    @Resource
    private GCSHelper gcsHelper;
    @Resource
    private GooglePayService googlePayService;
    @Autowired
    private MonitorSender monitorSender;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate mainCluster;
    @Resource
    private K8sUtils k8sUtils;

    @Scheduled(fixedDelay = 60 * 60 * 1000, initialDelay = 10000)
    public void loop() {
        if (ServerConfig.isNotProduct()) {
            return;
        }
        if (!k8sUtils.isMaster()) {
            return;
        }
        String suffix = DateHelper.UTC.formatDateInMonth2(new Date(System.currentTimeMillis() - 3600));
        String content = downLoadFile(suffix);
        if (StringUtils.isEmpty(content)) {
            return;
        }
        List<SaleReportOrderData> reportList = CSVParser.parseCSV(content, SaleReportOrderData.class);
        logger.info("report loop list. list.size={}", reportList.size());
        Map<String, SaleReportOrderData> rechargeMap = new HashMap<>();
        reportList.forEach(data -> {
            if (!data.getProduct_Type().equals("subscription")) {
                rechargeMap.put(data.getOrder_Number(), data);
            }
        });
        compareRecharge(rechargeMap);
        logger.info("report loop end.");
    }

    protected String downLoadFile(String suffix) {
        try {
            String bucketName = gcsHelper.getBucketName();
            byte[] bytes = gcsHelper.downloadBytes(bucketName, "sales/salesreport_" + suffix + ".zip");
            ByteBuf byteBuf = Unpooled.buffer();
            ZipInputStream zipInputStream = new ZipInputStream(new ByteArrayInputStream(bytes));
            while (zipInputStream.getNextEntry() != null) {
                while (byteBuf.writeBytes(zipInputStream, 1024) != -1) {
                }
            }
            return byteBuf.toString(StandardCharsets.UTF_8);
        } catch (IOException e) {
            logger.error("read sale report error. {}", e.getMessage(), e);
            monitorSender.info("ustar_pay", "read sale report error", e.getMessage());
        }
        return "";
    }

    private void compareRecharge(Map<String, SaleReportOrderData> rechargeMap) {
        List<GooglePayData> dataList = googlePayService.getListByOrders(rechargeMap.keySet());
        logger.info("compare recharge. dataList.size={} rechargeMap.size={}", dataList.size(), rechargeMap.size());
        Map<String, GooglePayData> payDataMap = new HashMap<>();
        dataList.forEach(data -> payDataMap.put(data.getOrderId(), data));
        rechargeMap.values().forEach(reportData -> {
            GooglePayData payData = payDataMap.get(reportData.getOrder_Number());
            if (payData == null) {
                if (needMonitor(reportData.getOrder_Number())) {
                    monitorLossOrder(reportData);
                }
            } else if (reportData.getFinancial_Status().equals("refund")) {
                if (payData.getFstatus() != PAY_STATUS_REFUND) {
                    if (needMonitor(reportData.getOrder_Number())) {
//                    int refundBeans = googlePayService.deductRefundBeans(payData.getUserId(), payData.getProductId(), "refund", "refund from google");
                        monitorRefundOrder(reportData, 0);
                    }
                }
            } else if (LOST_SET.contains(payData.getOrderId())) {
                monitorFindOrder(reportData);
            }
        });
    }

    private void monitorLossOrder(SaleReportOrderData reportOrderData) {
        String descBd = "发现漏单：" + "\n订单号：" + reportOrderData.getOrder_Number() +
                "\n支付日期：" + reportOrderData.getOrder_Charged_Date() +
                "\n补单url: " + getPatchUrl(reportOrderData) +
                "\n商品id:" + reportOrderData.getSKU_ID();
        logger.info("{}", descBd);
        monitorSender.info("ustar_pay", "谷歌订单监控", descBd);
        LOST_SET.add(reportOrderData.getOrder_Number());
    }

    private void monitorFindOrder(SaleReportOrderData reportOrderData) {
        String descBd = "漏单已补：" + "\n订单号：" + reportOrderData.getOrder_Number() +
                "\n支付日期：" + reportOrderData.getOrder_Charged_Date() +
                "\n商品id:" + reportOrderData.getSKU_ID();
        logger.info("{}", descBd);
        monitorSender.info("ustar_pay", "谷歌订单监控", descBd);
        LOST_SET.remove(reportOrderData.getOrder_Number());
    }

    private void monitorRefundOrder(SaleReportOrderData reportOrderData, int refundBeans) {
        String descBd = "发现退单：" + "\n订单号：" + reportOrderData.getOrder_Number() +
                "\n支付日期：" + reportOrderData.getOrder_Charged_Date() +
                "\n已扣钻石：" + refundBeans +
                "\n商品id:" + reportOrderData.getSKU_ID();
        logger.info("{}", descBd);
        monitorSender.info("ustar_pay", "谷歌订单监控", descBd);
    }

    private boolean needMonitor(String orderId) {
        boolean monitor;
        String value = mainCluster.opsForValue().get("str:googleMonitor:" + orderId);
        if (StringUtils.isEmpty(value)) {
            monitor = true;
            mainCluster.opsForValue().set("str:googleMonitor:" + orderId, DateHelper.getNowSeconds() + "", 32, TimeUnit.DAYS);
        } else {
            monitor = false;
        }
        return monitor;
    }

    private String getPatchUrl(SaleReportOrderData reportOrderData) {
        return "https://apiv2.qmovies.tv/pay/ggp/patch"
                + "?orderId=" + reportOrderData.getOrder_Number()
                + "&productId=" + reportOrderData.getSKU_ID()
                + "&packageName=" + reportOrderData.getProduct_ID()
                + "&purchaseToken=<purchaseToken>";
    }
}
