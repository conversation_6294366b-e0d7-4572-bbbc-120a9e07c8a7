package com.quhong.task.lostandfound.google;

import com.alibaba.fastjson.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class CSVParser {

    /**
     * 解析csv
     * @param content
     * @param clazz
     * @return
     * @param <D>
     */
    public static <D> List<D> parseCSV(String content, Class<D> clazz) {
        String[] arr = content.split("\n");
        String[] fieldArr = arr[0].split(",");
        for(int i = 0; i < fieldArr.length; i ++){
            fieldArr[i] = fieldArr[i].replace(" ", "_");
        }
        List<D> list = new ArrayList<>();
        for(int i = 1; i < arr.length; i ++){
            String[] valueArr = arr[i].split(",");
            JSONObject jsonObject = new JSONObject();
            for(int j = 0; j < valueArr.length; j ++) {
                jsonObject.put(fieldArr[j], valueArr[j]);
            }
            list.add(jsonObject.toJavaObject(clazz));
        }
        return list;
    }
}
