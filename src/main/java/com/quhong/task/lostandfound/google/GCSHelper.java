package com.quhong.task.lostandfound.google;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.auth.oauth2.UserCredentials;
import com.google.cloud.storage.Blob;
import com.google.cloud.storage.BlobId;
import com.google.cloud.storage.Storage;
import com.google.cloud.storage.StorageOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;

/**
 * google cloud storage helper
 */
@Component
public class GCSHelper {
    private static final Logger logger = LoggerFactory.getLogger(GCSHelper.class);


    private Storage storage;
    private String bucketName;

    @PostConstruct
    private void init() throws IOException {
        storage = StorageOptions.newBuilder().setCredentials(createUserCredential()).build().getService();
        bucketName = "pubsite_prod_7651888354270463787";
    }

    private GoogleCredentials createUserCredential() throws IOException {
        GoogleCredentials credentials;
        try (InputStream stream = new ClassPathResource("classpath:client_storage.json").getInputStream()) {
            credentials = UserCredentials.fromStream(stream);
        }
        return credentials;
    }

    public byte[] downloadBytes(String bucketName, String objectName) {
        Blob blob = storage.get(BlobId.of(bucketName, objectName));
        return blob.getContent();
    }

    public String getBucketName() {
        return bucketName;
    }
}
