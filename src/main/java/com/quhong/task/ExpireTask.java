package com.quhong.task;

import com.quhong.config.AsyncConfig;
import com.quhong.handler.UnlockGiftHandler;
import com.quhong.mysql.dao.LuckyGiftRewardDao;
import com.quhong.redis.CheatGiftRedis;
import com.quhong.redis.RoomMicRedis;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.service.CheatGiftService;
import com.quhong.utils.K8sUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

@Component
public class ExpireTask {
    private final static Logger logger = LoggerFactory.getLogger(ExpireTask.class);

    @Resource
    private K8sUtils k8sUtils;
    @Resource
    private CheatGiftRedis cheatGiftRedis;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private CheatGiftService cheatGiftService;
    @Resource
    private RoomMicRedis roomMicRedis;
    @Resource
    private UnlockGiftHandler unlockGiftHandler;
    @Resource
    private LuckyGiftRewardDao luckyGiftRewardDao;

    /**
     * 每15秒检查一次
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "0/15 * * * * ?")
    public void check() {
        if (k8sUtils.isMasterFromCache()) {
            // 新版整蛊礼物过期处理
            expireNewCheatGift();
        }
    }


    /**
     * 每6秒检查一次
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "0/6 * * * * ?")
    public void unLockGiftCheck() {
        if (k8sUtils.isMasterFromCache()) {
            // 解锁礼物消息推送
            unlockGiftHandler.unLockGiftCheck();
        }
    }

    private void expireNewCheatGift() {
        Set<String> allCheatSet = cheatGiftRedis.getAllCheatGiftSet();
        if (null == allCheatSet) {
            return;
        }
        for (String key : allCheatSet) {
            String[] split = key.split(CheatGiftRedis.DELIMITER);
            String aid = split[0];
            String cheatGiftType = split[1];
            logger.info("expire cheat gift uid={} cheatGiftType={}", aid, cheatGiftType);
            cheatGiftRedis.delCheatGift(aid, cheatGiftType);
            cheatGiftRedis.delCheatGiftTime(aid, cheatGiftType);
            String roomId = roomPlayerRedis.getActorRoomStatus(aid);
            if (!StringUtils.isEmpty(roomId)) {
                List<String> micList = roomMicRedis.getRoomMicList(roomId);
                if (micList.contains(aid)) {
                    cheatGiftService.sendCheatGiftChange(roomId, aid);
                }
            }
        }
    }

    /**
     * 每天02:01定期清理历史幸运礼物中奖记录（北京时间10:01）
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "0 01 02 * * ?")
    public void cleanUp() {
        if (k8sUtils.isMasterFromCache()) {
            luckyGiftRewardDao.cleanUp();
        }
    }
}
