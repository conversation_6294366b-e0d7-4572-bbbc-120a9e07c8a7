package com.quhong.task;

import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.pools.TaskPool;
import com.quhong.core.concurrency.tasks.ITask;
import org.springframework.stereotype.Component;

@Component
public class TaskFactory extends BaseTaskFactory {
    public static TaskFactory getFactory() {
        return (TaskFactory)factory;
    }

    public TaskFactory(){
        super(32, 64);
    }

    /**
     * 慢业务任务池
     */
    private TaskPool videoPool;

    @Override
    public void init() {
        super.init();
        videoPool = new TaskPool(waitingCount).init(mainThreadCount, 2 * mainThreadCount,  "video");
    }

    public void addVideo(ITask task){
        videoPool.add(task);
    }
}
