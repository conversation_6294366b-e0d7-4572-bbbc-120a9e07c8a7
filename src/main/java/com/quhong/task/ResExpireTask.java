package com.quhong.task;


import com.quhong.config.AsyncConfig;
import com.quhong.constant.DataResourcesConstant;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.service.ResourcesService;
import com.quhong.utils.K8sUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class ResExpireTask {

    @Resource
    private K8sUtils k8sUtils;
    @Resource
    private ResourcesService resourcesService;


    /**
     * 测试环境 每3分钟第0秒
     * <p>
     * 正式环境 每6个小时的第30分30秒  30 30 0/6 * * ?
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "30 30 0/6 * * ?")
    public void expireBadge() {
        if (k8sUtils.isMasterFromCache()) {
            ResourcesDTO resourcesDto = new ResourcesDTO();
            resourcesDto.setResType(DataResourcesConstant.TYPE_BADGE);
            resourcesDto.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE);
            resourcesService.dealRes(resourcesDto);
        }
    }

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "30 31 0/3 * * ?")
    public void expireMic() {
        if (k8sUtils.isMasterFromCache()) {
            ResourcesDTO resourcesDto = new ResourcesDTO();
            resourcesDto.setResType(DataResourcesConstant.TYPE_MIC);
            resourcesDto.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE);
            resourcesService.dealRes(resourcesDto);
        }
    }

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "30 32 0/6 * * ?")
    public void expireRide() {
        if (k8sUtils.isMasterFromCache()) {
            ResourcesDTO resourcesDto = new ResourcesDTO();
            resourcesDto.setResType(DataResourcesConstant.TYPE_RIDE);
            resourcesDto.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE);
            resourcesService.dealRes(resourcesDto);
        }
    }

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "30 33 0/3 * * ?")
    public void expireBuddle() {
        if (k8sUtils.isMasterFromCache()) {
            ResourcesDTO resourcesDto = new ResourcesDTO();
            resourcesDto.setResType(DataResourcesConstant.TYPE_BUDDLE);
            resourcesDto.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE);
            resourcesService.dealRes(resourcesDto);
        }
    }

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "30 34 0/6 * * ?")
    public void expireRipple() {
        if (k8sUtils.isMasterFromCache()) {
            ResourcesDTO resourcesDto = new ResourcesDTO();
            resourcesDto.setResType(DataResourcesConstant.TYPE_RIPPLE);
            resourcesDto.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE);
            resourcesService.dealRes(resourcesDto);
        }
    }

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "30 35 0/6 * * ?")
    public void expireRoomLock() {
        if (k8sUtils.isMasterFromCache()) {
            ResourcesDTO resourcesDto = new ResourcesDTO();
            resourcesDto.setResType(DataResourcesConstant.TYPE_ROOM_LOCK);
            resourcesDto.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE);
            resourcesService.dealRes(resourcesDto);
        }
    }

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "30 36 0/6 * * ?")
    public void expireFloatScreen() {
        if (k8sUtils.isMasterFromCache()) {
            ResourcesDTO resourcesDto = new ResourcesDTO();
            resourcesDto.setResType(DataResourcesConstant.TYPE_FLOAT_SCREEN);
            resourcesDto.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE);
            resourcesService.dealRes(resourcesDto);
        }
    }

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "30 37 0/6 * * ?")
    public void expireGiftBag() {
        if (k8sUtils.isMasterFromCache()) {
            ResourcesDTO resourcesDto = new ResourcesDTO();
            resourcesDto.setResType(DataResourcesConstant.TYPE_BAG_GIFT);
            resourcesDto.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE);
            resourcesService.dealRes(resourcesDto);
        }
    }

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "30 38 0/6 * * ?")
    public void expireMineBackground() {
        if (k8sUtils.isMasterFromCache()) {
            ResourcesDTO resourcesDto = new ResourcesDTO();
            resourcesDto.setResType(DataResourcesConstant.TYPE_MINE_BACKGROUND);
            resourcesDto.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE);
            resourcesService.dealRes(resourcesDto);
        }
    }

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "30 39 0/6 * * ?")
    public void expireMineBackgroundNote() {
        if (k8sUtils.isMasterFromCache()) {
            ResourcesDTO resourcesDto = new ResourcesDTO();
            resourcesDto.setResType(DataResourcesConstant.TYPE_MINE_BACKGROUND);
            resourcesDto.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE_NOTE);
            resourcesService.dealRes(resourcesDto);
        }
    }


    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "45 38 0/6 * * ?")
    public void expireBeautifulRid() {
        if (k8sUtils.isMasterFromCache()) {
            ResourcesDTO resourcesDto = new ResourcesDTO();
            resourcesDto.setResType(DataResourcesConstant.TYPE_BEAUTIFUL_RID);
            resourcesDto.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE);
            resourcesService.dealRes(resourcesDto);
        }
    }

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "45 39 0/6 * * ?")
    public void expireBeautifulRidNote() {
        if (k8sUtils.isMasterFromCache()) {
            ResourcesDTO resourcesDto = new ResourcesDTO();
            resourcesDto.setResType(DataResourcesConstant.TYPE_BEAUTIFUL_RID);
            resourcesDto.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE_NOTE);
            resourcesService.dealRes(resourcesDto);
        }
    }

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "45 40 0/6 * * ?")
    public void expireEntryEffectNote() {
        if (k8sUtils.isMasterFromCache()) {
            ResourcesDTO resourcesDto = new ResourcesDTO();
            resourcesDto.setResType(DataResourcesConstant.TYPE_ENTRY_EFFECT);
            resourcesDto.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE);
            resourcesService.dealRes(resourcesDto);
        }
    }

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "45 41 0/6 * * ?")
    public void expireHonorTitleNote() {
        if (k8sUtils.isMasterFromCache()) {
            ResourcesDTO resourcesDto = new ResourcesDTO();
            resourcesDto.setResType(DataResourcesConstant.TYPE_HONOR_TITLE);
            resourcesDto.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE);
            resourcesService.dealRes(resourcesDto);
        }
    }

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "50 38 0/6 * * ?")
    public void expireRechargeCoupon() {
        if (k8sUtils.isMasterFromCache()) {
            ResourcesDTO resourcesDto = new ResourcesDTO();
            resourcesDto.setResType(DataResourcesConstant.TYPE_RECHARGE_COUPON);
            resourcesDto.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE);
            resourcesService.dealRes(resourcesDto);
        }
    }

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "50 39 0/6 * * ?")
    public void expireRechargeCouponNote() {
        if (k8sUtils.isMasterFromCache()) {
            ResourcesDTO resourcesDto = new ResourcesDTO();
            resourcesDto.setResType(DataResourcesConstant.TYPE_RECHARGE_COUPON);
            resourcesDto.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE_NOTE);
            resourcesService.dealRes(resourcesDto);
        }
    }

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "50 35 0/6 * * ?")
    public void expireTicket() {
        if (k8sUtils.isMasterFromCache()) {
            ResourcesDTO resourcesDto = new ResourcesDTO();
            resourcesDto.setResType(DataResourcesConstant.TYPE_TICKET);
            resourcesDto.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE);
            resourcesService.dealRes(resourcesDto);
        }
    }

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "50 36 0/6 * * ?")
    public void expireTicketNote() {
        if (k8sUtils.isMasterFromCache()) {
            ResourcesDTO resourcesDto = new ResourcesDTO();
            resourcesDto.setResType(DataResourcesConstant.TYPE_TICKET);
            resourcesDto.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE_NOTE);
            resourcesService.dealRes(resourcesDto);
        }
    }

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "50 37 0/6 * * ?")
    public void expireVipCardNote() {
        if (k8sUtils.isMasterFromCache()) {
            ResourcesDTO resourcesDto = new ResourcesDTO();
            resourcesDto.setResType(DataResourcesConstant.TYPE_VIP_CARD);
            resourcesDto.setActionType(DataResourcesConstant.ACTION_ALL_EXPIRE_NOTE);
            resourcesService.dealRes(resourcesDto);
        }
    }
}
