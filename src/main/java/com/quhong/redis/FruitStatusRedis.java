package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.quhong.data.RedisFruitPoolRewardData;
import com.quhong.data.RedisFruitStatusData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@Component
public class FruitStatusRedis {
    private final static Logger logger = LoggerFactory.getLogger(FruitStatusRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;

    public void saveGameStatus(RedisFruitStatusData data) {
        try {
            String json = JSON.toJSONString(data);
            if (!StringUtils.isEmpty(json)) {
                clusterRedis.opsForValue().set(getKey(), json);
            }
        } catch (Exception e) {
            logger.error("saveGameStatus error msg={} ", e.getMessage(), e);
        }
    }


    public RedisFruitStatusData getGameStatus() {
        try {
            String json = clusterRedis.opsForValue().get(getKey());
            if (StringUtils.isEmpty(json)) {
                return null;
            }
            return JSON.parseObject(json, RedisFruitStatusData.class);
        } catch (Exception e) {
            logger.error("getGameStatus error msg={} ", e.getMessage(), e);
            return null;
        }
    }

    public void savePoolReward(RedisFruitPoolRewardData data) {
        try {
            String json = JSON.toJSONString(data);
            if (!StringUtils.isEmpty(json)) {
                clusterRedis.opsForValue().set(getRewardKey(), json);
            }
        } catch (Exception e) {
            logger.error("saveGameStatus error msg={} ", e.getMessage(), e);
        }
    }


    public RedisFruitPoolRewardData getPoolReward() {
        try {
            String json = clusterRedis.opsForValue().get(getRewardKey());
            if (StringUtils.isEmpty(json)) {
                return null;
            }
            return JSON.parseObject(json, RedisFruitPoolRewardData.class);
        } catch (Exception e) {
            logger.error("getGameStatus error msg={} ", e.getMessage(), e);
            return null;
        }
    }


    private String getKey() {
        return "str:fruit:status:key";
    }


    private String getRewardKey() {
        return "str:fruit:reward:key";
    }


}
