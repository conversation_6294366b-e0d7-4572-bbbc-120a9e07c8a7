package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.data.vo.RoomRankItemVO;
import com.quhong.data.vo.UserInfoItemVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
public class HomeRankListRedis {
    private static final Logger logger = LoggerFactory.getLogger(HomeRankListRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;

    @Cacheable(value = "getRoomRankList", key = "T(String).valueOf(#p0)",
            cacheManager = CaffeineCacheConfig.EXPIRE_5S_AFTER_WRITE)
    public List<RoomRankItemVO> getRoomRankList(int mode) {
        List<RoomRankItemVO> result = new ArrayList<>();
        try {
            String json = clusterRedis.opsForValue().get(getRoomRankListKey(mode));
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, RoomRankItemVO.class);
        } catch (Exception e) {
            logger.error("getRoomRankList error mode={} ", mode, e);
            return result;
        }
    }

    public void saveRoomRankList(List<RoomRankItemVO> data, int mode) {
        try {
            clusterRedis.opsForValue().set(getRoomRankListKey(mode), JSON.toJSONString(data), 2, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("saveRoomRankList error mode={}", mode, e);
        }
    }

    public void deleteRoomRankList(int mode) {
        try {
            clusterRedis.delete(getRoomRankListKey(mode));
        } catch (Exception e) {
            logger.error("deleteRoomRankList error mode={}", mode, e);
        }
    }


    @Cacheable(value = "getSendRankList", key = "T(String).valueOf(#p0)",
            cacheManager = CaffeineCacheConfig.EXPIRE_5S_AFTER_WRITE)
    public List<UserInfoItemVO> getSendRankList(int mode) {
        List<UserInfoItemVO> result = new ArrayList<>();
        try {
            String json = clusterRedis.opsForValue().get(getSendRankListKey(mode));
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, UserInfoItemVO.class);
        } catch (Exception e) {
            logger.error("getSendRankList error mode={} ", mode, e);
            return result;
        }
    }

    public void saveSendRankList(List<UserInfoItemVO> data, int mode) {
        try {
            clusterRedis.opsForValue().set(getSendRankListKey(mode), JSON.toJSONString(data), 2, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("saveSendRankList error mode={}", mode, e);
        }
    }

    public void deleteSendRankList(int mode) {
        try {
            clusterRedis.delete(getSendRankListKey(mode));
        } catch (Exception e) {
            logger.error("deleteSendRankList error mode={}", mode, e);
        }
    }


    @Cacheable(value = "getReceiveRankList", key = "T(String).valueOf(#p0)",
            cacheManager = CaffeineCacheConfig.EXPIRE_5S_AFTER_WRITE)
    public List<UserInfoItemVO> getReceiveRankList(int mode) {
        List<UserInfoItemVO> result = new ArrayList<>();
        try {
            String json = clusterRedis.opsForValue().get(getReceiveRankListKey(mode));
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, UserInfoItemVO.class);
        } catch (Exception e) {
            logger.error("getSendRankList error mode={} ", mode, e);
            return result;
        }
    }

    public void saveReceiveRankList(List<UserInfoItemVO> data, int mode) {
        try {
            clusterRedis.opsForValue().set(getReceiveRankListKey(mode), JSON.toJSONString(data), 2, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("saveSendRankList error mode={}", mode, e);
        }
    }

    public void deleteReceiveRankList(int mode) {
        try {
            clusterRedis.delete(getReceiveRankListKey(mode));
        } catch (Exception e) {
            logger.error("deleteSendRankList error mode={}", mode, e);
        }
    }

    private String getRoomRankListKey(int mode) {
        return String.format("str:room:devote:list:mode%d", mode);
    }

    private String getSendRankListKey(int mode) {
        return String.format("str:send:devote:list:mode%d", mode);
    }

    private String getReceiveRankListKey(int mode) {
        return String.format("str:receive:devote:list:mode%d", mode);
    }

}
