package com.quhong.redis;

import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/8/3
 */
@Component
public class RoomLikeRedis {

    private static final Logger logger = LoggerFactory.getLogger(RoomLikeRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    private String rankingExpireDate;

    public void saveFirstLikeRecord(String uid, String roomId) {
        String key = getFirstLikeKey(uid);
        try {
            redisTemplate.opsForHash().put(key, roomId, DateHelper.getNowSeconds() + "");
            redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_ONE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save first like record error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    public int getFirstLikeRecord(String uid, String roomId) {
        try {
            String strValue = (String) redisTemplate.opsForHash().get(getFirstLikeKey(uid), roomId);
            if (StringUtils.hasLength(strValue)) {
                return Integer.parseInt(strValue);
            }
        } catch (Exception e) {
            logger.error("get first like record error.uid={} roomId={} {}", uid, roomId, e.getMessage(), e);
        }
        return 0;
    }

    public void removeFirstLikeRecord(String uid, String roomId) {
        try {
            redisTemplate.opsForHash().delete(getFirstLikeKey(uid), roomId);
        } catch (Exception e) {
            logger.error("remove first like record error. uid={} roomId={} {}", uid, roomId, e.getMessage(), e);
        }
    }

    public long addRoomLikes(String roomId, long likesNum) {
        try {
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            String key = getNewRoomLikeRankingKey(dateStr);
            Double value = redisTemplate.opsForZSet().incrementScore(key, roomId, likesNum);
            if (!dateStr.equals(rankingExpireDate)) {
                redisTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
                rankingExpireDate = dateStr;
            }
            return value != null ? value.longValue() : 0;
        } catch (Exception e) {
            logger.error("addRoomLikes error. roomId={} likesNum={} {}", roomId, likesNum, e.getMessage(), e);
            return 0;
        }
    }

    public Map<String, Long> getNewRankingMap(int length) {
        String dateStr = DateHelper.ARABIAN.formatDateInDay();
        String key = getNewRoomLikeRankingKey(dateStr);
        try {
            Set<ZSetOperations.TypedTuple<String>> rangeWithScores = redisTemplate.opsForZSet().reverseRangeWithScores(key, 0, length -1);
            if (null == rangeWithScores) {
                return Collections.emptyMap();
            }
            Map<String, Long> rankingMap = new LinkedHashMap<>();
            for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
                if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                    continue;
                }
                rankingMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().longValue());
            }
            return rankingMap;
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    public long getRoomLikesNum(String roomId) {
        String dateStr = DateHelper.ARABIAN.formatDateInDay();
        String key = getNewRoomLikeRankingKey(dateStr);
        try {
            Double value = redisTemplate.opsForZSet().score(key, roomId);
            return value != null ? value.longValue() : 0;
        } catch (Exception e) {
            logger.error("getRoomLikesNum error. roomId={} {}", roomId, e.getMessage(), e);
            return 0;
        }
    }

    private String getNewRoomLikeRankingKey(String dateStr) {
        return "zset:roomLikeRanking_" + dateStr;
    }

    private String getFirstLikeKey(String uid) {
        return "hash:firstLikeRecord_" + DateHelper.ARABIAN.formatDateInDay() + "_" + uid;
    }
}
