package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.PartyListData;
import com.quhong.data.vo.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class RoomListRedis {
    private static final Logger logger = LoggerFactory.getLogger(RoomListRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;
    @Resource
    private OperationConfigRedis operationConfigRedis;
    @Resource
    private RoomListRedis roomListRedis;

    @Cacheable(value = "getForYouScoreWeight", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public JSONObject getForYouScoreWeight() {
        return operationConfigRedis.getForYouScoreWeight();
    }

    @Cacheable(value = "getAllNewOldUserScoreWeight", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public JSONObject getAllNewOldUserScoreWeight() {
        return operationConfigRedis.getAllNewOldUserScoreWeight();
    }

    @Cacheable(value = "getAllNewOldUserMeetScoreWeight", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public JSONObject getAllNewOldUserMeetScoreWeight() {
        return operationConfigRedis.getAllNewOldUserMeetScoreWeight();
    }

    /**
     * k21,k22用于同国家或者同地区推荐
     * 1 首页星空-新用户  3 meet-all-新用户
     *
     * @return
     */
    @Cacheable(value = "getAllNewRookieRoomScoreWeight", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public JSONObject getAllNewRookieRoomScoreWeight() {
        return operationConfigRedis.getAllNewRookieRoomScoreWeight();
    }

    @Cacheable(value = "popularList", key = "T(String).valueOf(#p0).concat('-').concat(#p1)",
            cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE)
    public List<PopularListVO> getPopularList(int area, int page) {
        List<PopularListVO> result = new ArrayList<>();
        try {
            String json = clusterRedis.opsForValue().get(getPopularListKey(area, page));
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, PopularListVO.class);
        } catch (Exception e) {
            logger.error("get popularList error area={} page={}", area, page, e);
            return result;
        }
    }

    public void savePopularList(List<PartyListData> pageList, int area, int page) {
        try {
            clusterRedis.opsForValue().set(getPopularListKey(area, page), JSON.toJSONString(pageList), 2, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save popularList error pageListSize={} area={} page={}", pageList.size(), area, page, e);
        }
    }

    public void deletePopularList(int area, int page) {
        try {
            clusterRedis.delete(getPopularListKey(area, page));
        } catch (Exception e) {
            logger.error("delete popularList error area={} page={}", area, page, e);
        }
    }

    private String getPopularListKey(int area, int page) {
        return String.format("str:popularList:area%d:page%d", area, page);
    }

    private String getPopularListPageCountKey(int area) {
        return String.format("str:popularListPageCount:area%d", area);
    }

    public int getPopularListPageCount(int area) {
        try {
            String pageCount = clusterRedis.opsForValue().get(getPopularListPageCountKey(area));
            if (null == pageCount) {
                return 0;
            }
            return Integer.parseInt(pageCount);
        } catch (Exception e) {
            logger.error("get popularListPageCount error area={}", area, e);
            return 0;
        }
    }

    public void setPopularListPageCount(int area, int count) {
        try {
            clusterRedis.opsForValue().set(getPopularListPageCountKey(area), count + "", 7, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("set popularListPageCount error area={}", area, e);
        }
    }

    private String getCountryListKey(String countryCode) {
        return "str:countryList:" + countryCode;
    }

    public List<CountryListVO> getCountryList(String countryCode) {
        List<CountryListVO> result = new ArrayList<>();
        try {
            String json = clusterRedis.opsForValue().get(getCountryListKey(countryCode));
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, CountryListVO.class);
        } catch (Exception e) {
            logger.error("get countryList error countryCode={}", countryCode, e);
            return result;
        }
    }

    public void saveCountryList(List<PartyListData> countryList, String countryCode) {
        try {
            clusterRedis.opsForValue().set(getCountryListKey(countryCode), JSON.toJSONString(countryList), 1, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("save countryList error countryListSize={} countryCode={}", countryList.size(), countryCode, e);
        }
    }

    private String getForYouListKey() {
        return "str:forYouList";
    }

    public void saveForYouList(List<PartyListData> resultList) {
        try {
            clusterRedis.opsForValue().set(getForYouListKey(), JSON.toJSONString(resultList), 10, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("save forYouList error resultListSize={}", resultList.size(), e);
        }
    }

    public List<ForYouListVO> getForYouList() {
        List<ForYouListVO> result = new ArrayList<>();
        try {
            String json = clusterRedis.opsForValue().get(getForYouListKey());
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, ForYouListVO.class);
        } catch (Exception e) {
            logger.error("get forYouList error", e);
            return result;
        }
    }

    public List<GameListVO> getGameList(int gameType) {
        List<GameListVO> result = new ArrayList<>();
        try {
            String json = clusterRedis.opsForValue().get(getGameListKey(gameType));
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, GameListVO.class);
        } catch (Exception e) {
            logger.error("getGameList error gameType={}", gameType, e);
            return result;
        }
    }

    public void saveGameList(List<GameListVO> pageList, int gameType) {
        try {
            clusterRedis.opsForValue().set(getGameListKey(gameType), JSON.toJSONString(pageList), 1, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("saveGameList error pageListSize={}", pageList.size(), e);
        }
    }

    public List<GameListVO> getAllGameList() {
        List<GameListVO> result = new ArrayList<>();
        try {
            String json = clusterRedis.opsForValue().get(getAllGameListKey());
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, GameListVO.class);
        } catch (Exception e) {
            logger.error("getAllGameList error.", e);
            return result;
        }
    }

    public void saveAllGameList(List<GameListVO> pageList) {
        try {
            clusterRedis.opsForValue().set(getAllGameListKey(), JSON.toJSONString(pageList), 1, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("saveAllGameList error pageListSize={}", pageList.size(), e);
        }
    }

    @Cacheable(value = "isPopularVipSwOpen", key = "targetClass + methodName",
            cacheManager = CaffeineCacheConfig.EXPIRE_5S_AFTER_WRITE)
    public boolean isPopularVipSwOpen() {
        try {
            String value = clusterRedis.opsForValue().get(getIsPopularVipSwKey());
            if (null == value) {
                return true;
            }
            int a = Integer.parseInt(value);
            return a > 0;

        } catch (Exception e) {
            logger.error("isPopularVipSwOpen error", e);
            return true;
        }

    }

    public List<RoomEventVO> getEventList(int type) {
        List<RoomEventVO> result = new ArrayList<>();
        try {
            String json = clusterRedis.opsForValue().get(getEventListKey(type));
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, RoomEventVO.class);
        } catch (Exception e) {
            logger.error("getEventList error.", e);
            return result;
        }
    }

    public void saveEventList(int type, List<RoomEventVO> eventList) {
        try {
            clusterRedis.opsForValue().set(getEventListKey(type), JSON.toJSONString(eventList), 1, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("saveEventList error. type={} pageListSize={}", type, eventList.size(), e);
        }
    }

    public List<RoomEventVO> getFamilyEventList(int familyId) {
        List<RoomEventVO> result = new ArrayList<>();
        try {
            String json = clusterRedis.opsForValue().get(getFamilyEventListKey(familyId));
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, RoomEventVO.class);
        } catch (Exception e) {
            logger.error("getFamilyEventList error.", e);
            return result;
        }
    }

    public void saveFamilyEventList(int familyId, List<RoomEventVO> eventList) {
        try {
            clusterRedis.opsForValue().set(getFamilyEventListKey(familyId), JSON.toJSONString(eventList), 5, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("saveFamilyEventList error. familyId={} pageListSize={}", familyId, eventList.size(), e);
        }
    }

    public void saveActiveRoomList(List<PartyListData> resultList, boolean rookie) {
        try {
            clusterRedis.opsForValue().set(getActiveRoomListKey(rookie), JSON.toJSONString(resultList), 10, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("save ActiveRoomList error resultListSize={}", resultList.size(), e);
        }
    }

    public List<ForYouListVO> getActiveRoomList(boolean rookie) {
        List<ForYouListVO> result = new ArrayList<>();
        try {
            String json = clusterRedis.opsForValue().get(getActiveRoomListKey(rookie));
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, ForYouListVO.class);
        } catch (Exception e) {
            logger.error("get ActiveRoomList error", e);
            return result;
        }
    }

    private String getAllPartyRoomKey() {
        return "hash:allPartyRoomData";
    }

    public void savePartyRoomData(Map<String, PartyListData> map) {
        try {
            Map<String, String> strMap = map.keySet().stream().collect(Collectors.toMap(Function.identity(), k -> JSONObject.toJSONString(map.get(k))));
            clusterRedis.delete(getAllPartyRoomKey());
            clusterRedis.opsForHash().putAll(getAllPartyRoomKey(), strMap);
        } catch (Exception e) {
            logger.error("savePartyRoomData error. {}", e.getMessage(), e);
        }
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public Map<String, PartyListData> getAllPartyRoomData() {
        try {
            Map<String, PartyListData> resultMap = new HashMap<>();
            Map<Object, Object> entries = clusterRedis.opsForHash().entries(getAllPartyRoomKey());
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                try {
                    String value = String.valueOf(entry.getValue());
                    resultMap.put(String.valueOf(entry.getKey()), JSONObject.parseObject(value, PartyListData.class));
                } catch (Exception e) {
                    logger.error("getAllFieldData error key={} value={}", entry.getKey(), entry.getValue(), e);
                }
            }
            return resultMap;
        } catch (Exception e) {
            logger.error("getAllPartyRoomData error. {}", e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public Map<String, Integer> getAllRoomOnlineMap() {
        Map<String, PartyListData> allPartyRoomData = roomListRedis.getAllPartyRoomData();
        Map<String, Integer> result = new HashMap<>();
        if (allPartyRoomData != null) {
            for (Map.Entry<String, PartyListData> entry : allPartyRoomData.entrySet()) {
                PartyListData partyData = entry.getValue();
                if (partyData != null) {
                    result.put(partyData.getRoomId(), partyData.getRealOnline());
                }
            }
        }
        return result;
    }

    public Map<String, Long> getLikeRankingMap(int length) {
        String key = getRoomLikeRankingKey(DateHelper.ARABIAN.formatDateInDay());
        Map<String, Long> linkedRankMap = new LinkedHashMap<>();
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterRedis.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().longValue());
        }
        return linkedRankMap;
    }


    public void saveAllOnlyOnlineRoomList(List<PartyListData> resultList) {
        try {
            clusterRedis.opsForValue().set(getAllOnlyOnlineRoomListKey(), JSON.toJSONString(resultList), 10, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("save ActiveRoomList error resultListSize={}", resultList.size(), e);
        }
    }

    public List<PartyListData> getAllOnlyOnlineRoomList() {
        List<PartyListData> result = new ArrayList<>();
        try {
            String json = clusterRedis.opsForValue().get(getAllOnlyOnlineRoomListKey());
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, PartyListData.class);
        } catch (Exception e) {
            logger.error("get getAllOnlyOnlineRoomList error", e);
            return result;
        }
    }

    public void setGameTypeDataAll(Map<String, String> dataMap) {
        try {
            String key = getCommonHashKey();
            clusterRedis.opsForHash().putAll(key, dataMap);
        } catch (Exception e) {
            logger.info("setGameTypeDataAll error dataMap={}  e={}", dataMap, e);
        }
    }


    /**
     * 获取hash key所有值
     */
    public Map<String, Integer> getGameTypeDataAll() {
        Map<String, Integer> hashMap = new HashMap<>();
        try {
            Map<Object, Object> entries = clusterRedis.opsForHash().entries(getCommonHashKey());
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                hashMap.put(entry.getKey() + "", Integer.parseInt(String.valueOf(entry.getValue())));
            }
            return hashMap;
        } catch (Exception e) {
            logger.error("getGameTypeAll error e={}", e.getMessage(), e);
        }
        return hashMap;
    }

    public void deleteGameTypeDataAll() {
        try {
            clusterRedis.delete(getCommonHashKey());
        } catch (Exception e) {
            logger.error("delete deleteGameTypeDataAll error a", e);
        }
    }


    public void saveSocialRoomList(List<PartyListData> resultList) {
        try {
            clusterRedis.opsForValue().set(getSocialRoomListKey(), JSON.toJSONString(resultList), 10, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("save ActiveRoomList error resultListSize={}", resultList.size(), e);
        }
    }

    /**
     * 数据用于 8.59 星空首页推荐（推荐用户）（适用新,老用户策略），
     * 星空首页（推荐用户）meet friend-all（适用新用户策略），
     * ALL-new（推荐房间）（适用新用户策略）
     *
     * @return
     */
    public List<ForYouListVO> getSocialRoomList() {
        List<ForYouListVO> result = new ArrayList<>();
        try {
            String json = clusterRedis.opsForValue().get(getSocialRoomListKey());
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, ForYouListVO.class);
        } catch (Exception e) {
            logger.error("get socialRoomList error", e);
            return result;
        }
    }

    public void saveSocialNewRoomList(List<PartyListData> resultList) {
        try {
            clusterRedis.opsForValue().set(getSocialNewRoomListKey(), JSON.toJSONString(resultList), 10, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("save ActiveRoomList error resultListSize={}", resultList.size(), e);
        }
    }

//    public List<ForYouListVO> getSocialNewRoomList() {
//        List<ForYouListVO> result = new ArrayList<>();
//        try {
//            String json = clusterRedis.opsForValue().get(getSocialRoomListKey());
//            if (StringUtils.isEmpty(json)) {
//                return result;
//            }
//            return JSON.parseArray(json, ForYouListVO.class);
//        } catch (Exception e) {
//            logger.error("get socialRoomList error", e);
//            return result;
//        }
//    }

    /**
     * @param resultList
     * @param isMeet     true  meet-all-老用户  false all-new-老用户
     */
    public void saveAllNewOldUserList(List<PartyListData> resultList, boolean isMeet) {
        try {
            clusterRedis.opsForValue().set(getAllNewOldUserKey(isMeet), JSON.toJSONString(resultList), 10, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("save saveAllNewOldList error resultListSize={}", resultList.size(), e);
        }
    }

    /**
     * ALL-new（推荐房间）（适用老用户策略）
     *
     * @param isMeet true  meet-all-老用户  false all-new-老用户
     * @return
     */
    public List<ForYouListVO> getAllNewOldUserList(boolean isMeet) {
        List<ForYouListVO> result = new ArrayList<>();
        try {
            String json = clusterRedis.opsForValue().get(getAllNewOldUserKey(isMeet));
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, ForYouListVO.class);
        } catch (Exception e) {
            logger.error("get getAllNewOldList error", e);
            return result;
        }
    }

    @Cacheable(value = "getAllNewRoomScoreWeight", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public JSONObject getAllNewRoomScoreWeight() {
        return operationConfigRedis.getAllNewRoomScoreWeight();
    }

    public void saveRoomGuideTotalCountSet(String uid, String configId, Set<String> daySet) {
        try {
            clusterRedis.opsForValue().set(getRoomGuideTotalCountDayKey(uid, configId), JSON.toJSONString(daySet), 10, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save getRoomGuideTotalCountDay error uid={} configId={}", uid, configId, e);
        }
    }


    public Set<String> getRoomGuideTotalCountSet(String uid, String configId) {
        Set<String> result = new HashSet<>();
        try {
            String json = clusterRedis.opsForValue().get(getRoomGuideTotalCountDayKey(uid, configId));
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            List<String> fromResult = JSON.parseArray(json, String.class);
            result.addAll(fromResult);
            return result;
        } catch (Exception e) {
            logger.error("get getRoomGuideTotalCountSet uid={} configId={} error", uid, configId, e);
            return result;
        }
    }

    /**
     * all-recommend 老用户
     */
    public void saveAllRecommendOldUserList(List<PartyListData> resultList) {
        try {
            clusterRedis.opsForValue().set(getAllRecommendOldUserKey(), JSON.toJSONString(resultList), 10, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.error("saveAllRecommendOldUserList error resultListSize={}", resultList.size(), e);
        }
    }

    /**
     * all-recommend 老用户
     */
    public List<ForYouListVO> getAllRecommendOldUserList() {
        List<ForYouListVO> result = new ArrayList<>();
        try {
            String json = clusterRedis.opsForValue().get(getAllRecommendOldUserKey());
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, ForYouListVO.class);
        } catch (Exception e) {
            logger.error("get getAllRecommendOldUserList error", e);
            return result;
        }
    }


    private String getGameListKey(int gameType) {
        return "str:gameList:" + gameType;
    }

    private String getAllGameListKey() {
        return "str:allGameList:";
    }

    private String getIsPopularVipSwKey() {
        return "str:isPopularVipSw";
    }

    private String getEventListKey(int type) {
        return "str:eventList:" + type;
    }

    private String getFamilyEventListKey(int familyId) {
        return "str:familyEventList:" + familyId;
    }

    private String getActiveRoomListKey(boolean rookie) {
        return "str:activeRoomList" + (rookie ? ":rookie" : "");
    }

    private String getRoomLikeRankingKey(String dateStr) {
        return "zset:roomLikeRanking_" + dateStr;
    }

    private String getAllOnlyOnlineRoomListKey() {
        return "str:allOnlyOnlineRoomList";
    }

    /**
     * hash key
     */
    private String getCommonHashKey() {
        return "hash:room:gameType:online";
    }


    private String getSocialRoomListKey() {
        return "str:socialRoomList";
    }

    private String getAllRecommendOldUserKey() {
        return "str:allRecommendOldUser";
    }


    private String getAllNewOldUserKey(boolean isMeet) {
        return "str:allNewOldUserList" + (isMeet ? ":meet" : "");
    }

    private String getRoomGuideTotalCountDayKey(String uid, String configId) {
        return String.format("str:roomGuideTotalCountDay:%s:%s", uid, configId);
    }

    private String getSocialNewRoomListKey() {
        return "str:socialNewRoomList";
    }
}
