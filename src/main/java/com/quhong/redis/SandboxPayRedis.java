package com.quhong.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class SandboxPayRedis {
    private static final Logger logger = LoggerFactory.getLogger(SandboxPayRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    public boolean iosSandbox(String uid) {
        try {
            return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(getIOSKey(), uid));
        } catch (Exception e) {
            logger.error("apple pay in white error. uid={} {}", uid, e.getMessage(), e);
        }
        return false;
    }

    private String getIOSKey() {
        return "ios_sandbox_buy";
    }

    public boolean androidSandbox(String uid) {
        try {
            return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(getAndroidKey(), uid));
        } catch (Exception e) {
            logger.error("android pay in white error. uid={} {}", uid, e.getMessage(), e);
        }
        return false;
    }

    private String getAndroidKey() {
        return "android_sandbox_buy";
    }
}
