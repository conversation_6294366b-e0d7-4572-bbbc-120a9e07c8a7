package com.quhong.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Component
public class TopRoomRedis {
    private static final Logger logger = LoggerFactory.getLogger(TopRoomRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;

    /**
     * 获取置顶房间，key为roomId，value为排名
     */
    public Map<String, Integer> getAllTopRooms() {
        try {
            Map<String, Integer> result = new HashMap<>();
            Map<Object, Object> topRooms = clusterRedis.opsForHash().entries("ar_top_room_new");
            for (Map.Entry<Object, Object> entry : topRooms.entrySet()) {
                try {
                    result.put(String.format("r:%s", entry.getKey()), Integer.parseInt(String.valueOf(entry.getValue())));
                } catch (Exception e) {
                    logger.error("parse top room error. {}", e.getMessage(), e);
                }
            }
            return result;
        } catch (Exception e) {
            logger.error("get all top rooms error {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }
}
