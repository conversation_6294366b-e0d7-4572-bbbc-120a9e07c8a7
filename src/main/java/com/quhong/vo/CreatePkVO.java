package com.quhong.vo;

import com.quhong.msg.obj.PKInfoObject;

/**
 * <AUTHOR>
 * @date 2022/12/5
 */
public class CreatePkVO {

    private String creator_uid;

    private String gicon;

    private Integer left_time;

    private String room_id;

    private String pid;

    private Integer status;

    private Integer total_time;

    private Integer pk_type;

    private PKInfoObject red_info;

    public String getCreator_uid() {
        return creator_uid;
    }

    public void setCreator_uid(String creator_uid) {
        this.creator_uid = creator_uid;
    }

    public String getGicon() {
        return gicon;
    }

    public void setGicon(String gicon) {
        this.gicon = gicon;
    }

    public Integer getLeft_time() {
        return left_time;
    }

    public void setLeft_time(Integer left_time) {
        this.left_time = left_time;
    }

    public String getRoom_id() {
        return room_id;
    }

    public void setRoom_id(String room_id) {
        this.room_id = room_id;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getTotal_time() {
        return total_time;
    }

    public void setTotal_time(Integer total_time) {
        this.total_time = total_time;
    }

    public Integer getPk_type() {
        return pk_type;
    }

    public void setPk_type(Integer pk_type) {
        this.pk_type = pk_type;
    }

    public PKInfoObject getRed_info() {
        return red_info;
    }

    public void setRed_info(PKInfoObject red_info) {
        this.red_info = red_info;
    }
}
