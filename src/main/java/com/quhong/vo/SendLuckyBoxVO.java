package com.quhong.vo;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * <AUTHOR>
 * @date 2023/1/13
 */
public class SendLuckyBoxVO {

    @JSONField(name = "box_id")
    private String boxId;

    private Integer beans;

    @JSO<PERSON>ield(name = "box_num")
    private Integer boxNum;

    @JSONField(name = "valid_box")
    private Integer validBox;

    public SendLuckyBoxVO() {
    }

    public SendLuckyBoxVO(String boxId, Integer beans, Integer boxNum, Integer validBox) {
        this.boxId = boxId;
        this.beans = beans;
        this.boxNum = boxNum;
        this.validBox = validBox;
    }

    public String getBoxId() {
        return boxId;
    }

    public void setBoxId(String boxId) {
        this.boxId = boxId;
    }

    public Integer getBeans() {
        return beans;
    }

    public void setBeans(Integer beans) {
        this.beans = beans;
    }

    public Integer getBoxNum() {
        return boxNum;
    }

    public void setBoxNum(Integer boxNum) {
        this.boxNum = boxNum;
    }

    public Integer getValidBox() {
        return validBox;
    }

    public void setValidBox(Integer validBox) {
        this.validBox = validBox;
    }
}
