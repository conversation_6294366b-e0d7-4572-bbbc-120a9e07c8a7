package com.quhong;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class NumberFilter {

    // 连续3位相同数字的组合
    private static final List<String> LIST_1 = Arrays.asList("000", "111", "222", "333", "444", "555", "444", "555", "666", "777", "888", "999");

    //连续4位数字递增或递减的数字组合
    private static final List<String> LIST_3 = Arrays.asList("0123", "1234", "2345", "3456", "4567", "5678", "6789", "9876", "8765", "7654", "6543", "5432", "4321", "3210");

    public static void main(String[] args) {
//        int start = 8000000;
//        int end = 9000000;
//
//        List<Integer> validNumbers = new ArrayList<>();
//
//        for (int i = start; i <= end; i++) {
//            if (isExcluded(i)) {
//                continue; // 排除符合规则的数字
//            }
//            validNumbers.add(i); // 添加符合条件的数字
//        }
//
//        System.out.println("符合条件的数字总数: " + validNumbers.size());
//        // 输出前10个结果作为示例
//        if (!validNumbers.isEmpty()) {
//            System.out.println("前100个符合条件的数字: " + validNumbers.subList(0, Math.min(100, validNumbers.size())));
//        }
//
//        System.out.println("isExcluded: " + isExcluded(153457434));
//        isPatten();
        total();
    }

    private static void isPatten() {
        int start = 100000;
        int end = 1000000;
        List<Integer> validNumbers = new ArrayList<>();
        for (int i = start; i <= end; i++) {
            String numStr = String.valueOf(i);
            if (isAABBCC(numStr)) {
                validNumbers.add(i); // 添加符合条件的数字
            }
//            if (isAAABBB(numStr)) {
//                validNumbers.add(i); // 添加符合条件的数字
//            }
        }
        System.out.println("符合条件的数字总数: " + validNumbers.size());
        // 输出前100个结果作为示例
        if (!validNumbers.isEmpty()) {
            System.out.println("前100个符合条件的数字: " + validNumbers.subList(0, Math.min(100, validNumbers.size())));
        }
    }

    /**
     * 判断是否符合排除规则
     */
    private static boolean isExcluded(int number) {
        String numStr = String.valueOf(number);
        // 至少含有1组连续3位或以上相同数字以上的组合
        if (isBeautifulRid1(numStr, LIST_1)) {
            return true;
        }
        // 至少含有1组连续4位数字递增或递减的数字组合
        if (isBeautifulRid1(numStr, LIST_3)) {
            return true;
        }
        if (numStr.length() == 7) {
            return isExcluded7(numStr);
        } else if (numStr.length() == 8) {
            return isExcluded8(number);
        }
        return false; // 不符合任何排除规则
    }

    private static boolean isExcluded7(String numStr) {
        // 检查 AABB 类型
        if (numStr.matches("\\d*(\\d)\\1(\\d)\\2\\d*")) return true;

        // 检查 ABAB 类型
        if (numStr.matches("\\d*(\\d)(\\d)\\1\\2\\d*")) return true;

        // 检查 ABBABB 类型
        if (numStr.matches("\\d*(\\d)(\\d)\\2(\\d)\\1\\1\\d*")) return true;

        // 检查 ABAABA 类型
        if (numStr.matches("\\d*(\\d)(\\d)\\1\\2\\2\\1\\d*")) return true;

        // 检查 AABAAB 类型
        if (numStr.matches("\\d*(\\d)\\1(\\d)\\2\\1\\1\\d*")) return true;

        // 检查 ABCABC 类型
        if (numStr.matches("\\d*(\\d)(\\d)(\\d)\\1\\2\\3\\d*")) return true;

        // 检查 ABCCBA 类型
        if (numStr.matches("\\d*(\\d)(\\d)(\\d)\\3\\2\\1\\d*")) return true;

        return false; // 不符合任何排除规则
    }

    private static boolean isExcluded8(int number) {
        String numStr = String.valueOf(number);
        // 检查 AABBCC 类型
        if (numStr.matches("\\d*(\\d)\\1(\\d)\\2(\\d)\\3\\d*")) return true;
        // 检查 ABABAB 类型
        if (numStr.matches("\\d*(\\d)(\\d)\\1\\2\\1\\2\\d*")) return true;
        // 检查 ABBABB 类型
        if (numStr.matches("\\d*(\\d)(\\d)\\2(\\d)\\1\\1\\d*")) return true;
        // 检查 ABAABA 类型
        if (numStr.matches("\\d*(\\d)(\\d)\\1\\2\\2\\1\\d*")) return true;
        // 检查 AABAAB 类型
        if (numStr.matches("\\d*(\\d)\\1(\\d)\\2\\1\\1\\d*")) return true;
        // 检查 AABBAA 类型
        if (numStr.matches("\\d*(\\d)\\1(\\d)\\2\\2\\1\\1\\d*")) return true;
        // 检查 ABCABC 类型
        if (numStr.matches("\\d*(\\d)(\\d)(\\d)\\1\\2\\3\\d*")) return true;
        // 检查 ABCCBA 类型
        if (numStr.matches("\\d*(\\d)(\\d)(\\d)\\3\\2\\1\\d*")) return true;

//        // 检查 AAABBB 类型
//        if (numStr.matches("\\d*(\\d)\\1\\1(\\d)\\2\\2\\d*")) return true;

        // 检查 ABCDABCD 类型
//        if (numStr.matches("\\d*(\\d)(\\d)(\\d)(\\d)\\1\\2\\3\\4\\d*")) return true;

        // 检查 ABCDDCBA 类型
//        if (numStr.matches("\\d*(\\d)(\\d)(\\d)(\\d)\\4\\3\\2\\1\\d*")) return true;
        return false; // 不符合任何排除规则
    }

    private static boolean isAABBCC(String numStr) {
        // 检查 AABBCC 类型
        if (numStr.matches("(\\d)\\1(\\d)\\2(\\d)\\3")) return true;
        return false;
    }

    private static boolean isAAABBB(String numStr) {
        // 检查 AAABBB 类型
        if (numStr.matches("(\\d)\\1\\1(\\d)\\2\\2")) return true;
        return false;
    }


    // 检查是否是连续升序或降序
    public static boolean isSequential(String sub) {
        boolean ascending = true, descending = true;
        for (int i = 1; i < sub.length(); i++) {
            int diff = sub.charAt(i) - sub.charAt(i - 1);
            if (diff != 1) ascending = false;
            if (diff != -1) descending = false;
        }
        return ascending || descending;
    }

    private static boolean isBeautifulRid1(String strRid, List<String> list) {
        for (String s : list) {
            if (strRid.contains(s)) {
                return true;
            }
        }
        return false;
    }

    private static void total() {
        List<Double> dValues = Arrays.asList(60.96, 0.99, 409.97, 499.98, 4.99, 0.99, 199.99, 183.77, 99.98, 7.96, 39.98, 19.99, 24.98, 49.99, 57.14, 101.37, 0.99, 38.96, 49.99, 309.97, 7.92, 19.99, 0.99, 26.96, 199.99, 0.99, 109.96, 63.93, 69.98, 0.99, 0.99, 10.97, 0.99, 6.97, 33.93, 4.99, 19.99, 1.98, 0.99, 4.99, 5.98, 36.91, 149.97, 8.95, 449.97, 104.97, 18.93, 69.98, 0.99, 99.95, 0.99, 489.97, 22.96, 289.96, 38.92, 0.99, 1.0, 9.98, 199.99, 2.97, 70.94, 135.86, 19.99, 4.99, 39.98, 49.99, 49.99, 51.95, 0.99, 39.98, 19.99, 2.97, 69.98, 2.97, 0.99, 74.97, 0.99, 89.99, 19.99, 799.83, 19.99, 210.96, 59.97, 149.97, 298.7, 19.99, 109.98, 4.99, 39.98, 5.98, 2.97, 0.99, 174.0, 105.26, 19.99, 9.94, 49.99, 259.87, 16.95, 19.99, 74.81, 1.98, 5.98, 89.99, 8.95, 25.97, 50.0, 3.96, 2077.92, 389.94, 0.99, 199.99, 5.98, 1.98, 16.95, 4.99, 69.98, 0.99, 10.97, 19.99, 45.96, 89.99, 0.99, 454.55, 73.68, 25.94, 19.96, 499.9, 139.98, 8.95, 0.99, 92.62, 5.94, 969.32, 0.99, 623.38, 0.99, 19.99, 0.99, 51.87, 5.94, 19.99, 44.97, 399.98, 19.99, 7.96, 39.98, 121.89, 129.97, 149.97, 159.97, 0.99, 0.99, 0.99, 199.99, 0.99, 19.99, 0.99, 89.99, 2.97, 19.99, 7.96, 0.99, 59.97, 14.97, 89.97, 1.98, 2.97, 99.98, 22.96, 1.98, 94.98, 19.99, 15.92, 0.99, 0.99, 0.99, 52.63, 75.96, 89.99, 89.99, 454.55, 0.99, 0.99, 2.97, 19.99, 89.99, 0.99, 39.98, 24.98, 2.97, 25.97, 0.99, 162.0, 89.99, 0.99, 0.99, 39.98, 39.95, 199.99, 9.98, 13.94, 729.95, 62.94, 4.99, 4.99, 1.98, 31.58, 194.81, 199.99, 930.85, 0.99, 6.97, 1.98, 49.99, 24.98, 89.99, 0.99, 22.96, 89.99, 4.95, 311.69, 21.05, 14.93, 0.99, 0.99, 364.87, 1.98, 6.97, 4.99, 22.96, 19.99, 24.98, 11.96, 9.98, 19.99, 0.99, 49.99, 194.0, 12.91, 4.99, 69.98, 2.97, 9.98, 0.99, 0.99, 7.96, 8.95, 0.99, 969.95, 4.99, 0.99, 49.99, 4.99, 0.99, 0.99, 26.96, 49.99, 19.99, 19.99, 49.99, 19.99, 4.99, 544.54, 49.99, 763.71, 19.99, 49.99, 19.99, 9.98, 599.98, 39.98, 19.99, 4.99, 77.92, 19.99, 0.99, 5.98, 109.98, 19.99, 3.96, 290.0, 29.93, 44.97, 39.98, 149.97, 199.99, 69.98, 49.99, 1.98, 4.99, 146.23, 11.96, 16.95, 19.99, 9.98, 5.98, 8.95, 18.93, 99.98, 0.99, 55.91, 3.96, 1.98, 51.95, 1.98, 174.0, 4.99, 209.94, 0.99, 99.98, 0.99, 26.96, 39.98, 1.98, 0.99, 194.81, 289.98, 9.98, 39.98, 19.92, 26.96, 44.97, 19.99, 32.9, 19.99, 160.96, 49.96, 39.98, 0.99, 2402.86, 199.99, 39.98, 19.99, 465.43, 0.99, 14.97, 19.99, 0.99, 20.91, 59.97, 189.95, 4.99);
        int total = 0;
        for (Double i : dValues) {
            double m = i * 2;
            total += dd(m);
        }
        System.out.println("dValues size:" + dValues.size() + " total: " + total);
    }

    private static int dd(double m) {
        int d = 0;
        if (m < 4.99) {
            d = 0;
        } else if (m >= 4.99 && m < 19.99) {
            d = 199;
        } else if (m >= 19.99 && m < 49.99) {
            d = 299;
        } else if (m >= 49.99 && m < 69.99) {
            d = 699;
        } else if (m >= 69.99 && m < 199.99) {
            d = 1299;
        } else if (m >= 199.99 && m < 599.99) {
            d = 2999;
        } else if (m >= 599.99 && m < 899.99) {
            d = 5555;
        } else if (m >= 899.99 && m < 1399.99) {
            d = 8888;
        } else {
            d = 9999;
        }
        return d;
    }
}
