package com.quhong.enums;

/**
 * <AUTHOR>
 * @date 2022/6/8
 */
public class UserInfoHttpCode extends HttpCode{

    /**
     * 用户不存在
     */
    public static final HttpCode USER_NOT_EXIST = new HttpCode(10, "user_not_exist");
    /**
     * 靓号id不存在
     */
    public static final HttpCode ID_NOT_EXIST = new HttpCode(11, "id_not_exist");
    /**
     * 搜索的关键字不是数值
     */
    public static final HttpCode PARAMETER_ERR = new HttpCode(20, "Please enter user ID.", "يرجى إدخال ID المستخدم.");
    /**
     * 未输入搜索关键字
     */
    public static final HttpCode CODE_PARAMETER_ERR = new HttpCode(20, "Please enter key word");
    /**
     * 请更新app
     */
    public static final HttpCode PARAMETER_UPDATE_APP = new HttpCode(21, "please update the app.");
    /**
     * 用户未校验图灵盾接口
     */
    public static final HttpCode UNLOGIN = new HttpCode(40, "please login again");
    /**
     * 不允许发送好友请求
     */
    public static final HttpCode CODE_NOT_ALLOW = new HttpCode(45, "Dirty word in message is not allowed!", "غير مسموح بالكلمة القذرة في الجملة!");
    /**
     * 用户设置了拒绝添加好友
     */
    public static final HttpCode USER_SET_REFUSE_ADD_FRIENDS = new HttpCode(45, "He/She has set refuse to add friends", "حدد الشحص رفض إضافة أصدقاء");
    /**
     * 用户有违规行为，已被禁止添加朋友
     */
    public static final HttpCode CODE_TN_DEVICE_FAILED = new HttpCode(72, "");
    /**
     * 已经是好友了
     */
    public static final HttpCode CODE_ALREADY_FRIEND = new HttpCode(100,"You are already friends", "أنتم بالفعل أصدقاء");
    /**
     * 已经申请过好友了
     */
    public static final HttpCode CODE_ALREADY_SEND = new HttpCode(101,"Too many friend requests sent. Please try again later. ", "تم إرسال طلبات صداقة كثيرة جدا.  الرجاء معاودة المحاولة في وقت لاحق.");
    /**
     * 不是好友
     */
    public static final HttpCode NOT_FRIENDS = new HttpCode(102,"");
    /**
     * 删除失败
     */
    public static final HttpCode DELETE_ERROR = new HttpCode(2101, "delete error");
    /**
     * 账号被管理员限制
     */
    public static final HttpCode USER_LIMIT_SEND_FRIEND_APPLY = new HttpCode(2109, "Your are not allowed to send friend request due to violation of YouStar regulation");
    /**
     * 好友数量超过上限
     */
    public static final HttpCode FRIENDS_NUMBER_EXCEEDS_LIMIT = new HttpCode(45, "Your friends number is over the limitation, please clean up in time.", "لقد وصل عدد أصدقائك إلى الحد الأقصى ، يرجى التنظيف في الوقت المناسب.");
    /**
     * 对方设置了拒绝添加好友
     */
    public static final HttpCode SET_REFUSE_TO_ADD_FRIENDS = new HttpCode(45, "He/She has set refuse to add friends", "حدد الشحص رفض إضافة أصدقاء");
    /**
     * 不是svip不能使用
     */
    public static final HttpCode NOT_SVIP_CAN_NOT_USE = new HttpCode(46, "not_svip_can_not_use");
    /**
     * 不是vip不能使用
     */
    public static final HttpCode NOT_VIP_CAN_NOT_USE = new HttpCode(47, "not_vip_can_not_use");
    /**
     * 坐骑已经过期无法佩戴
     */
    public static final HttpCode ENTRANCE_HAS_BEEN_EXPIRED = new HttpCode(48, "entrance_has_been_expired");
    /**
     * 坐骑已经佩戴
     */
    public static final HttpCode USING_THIS_ENTRANCE = new HttpCode(49, "using_this_entrance");
    /**
     * 不存在
     */
    public static final HttpCode NOT_EXIST = new HttpCode(50, "not_exist");

    public static final HttpCode VIDEO_NOT_EXIST = new HttpCode(51, "video_not_exist");

    public static final HttpCode CAN_ONLY_BE_GUARDED_ONCE_A_DAY = new HttpCode(101, "can_only_be_guarded_once_a_day");
    public static final HttpCode ONLY_FRIENDS_CAN_PROTECT_YOU = new HttpCode(102, "only_friends_can_protect_you");
}
