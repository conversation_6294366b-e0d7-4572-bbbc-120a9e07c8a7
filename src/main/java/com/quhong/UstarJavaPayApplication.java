package com.quhong;

import com.quhong.config.BaseAppConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.version.VersionReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * BaseAppConfig 基础配置
 * NettyConfig 服务发现
 * TCPServerConfig 长连接配置
 * RoomMongoBean 连接room库
 */
@EnableScheduling
@EnableFeignClients
@EnableAsync
@ServletComponentScan
@ImportResource("classpath*:spring-context.xml")
@ImportAutoConfiguration({BaseAppConfig.class})
@SpringBootApplication(exclude = {RedisAutoConfiguration.class, MongoAutoConfiguration.class,
        DataSourceAutoConfiguration.class})
public class UstarJavaPayApplication {
    private final static Logger logger = LoggerFactory.getLogger(UstarJavaPayApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(UstarJavaPayApplication.class, args);
        logger.info("============= pay service {} v={} start ===============================", ServerConfig.getServerID(), VersionReader.read());
    }
}
