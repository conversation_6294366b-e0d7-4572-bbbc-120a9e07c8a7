package com.quhong.api;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.web.HttpResponseData;
import com.quhong.core.web.WebClient;
import com.quhong.data.BaiShunGameInfo;
import com.quhong.dto.BsGetGameInfoDTO;
import com.quhong.enums.ApiCode;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.vo.BsGameInfoVO;
import com.quhong.vo.BsGameListVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/7/31
 */
@Service
@Lazy
public class BaiShunGameApi {

    private static final Logger logger = LoggerFactory.getLogger(BaiShunGameApi.class);

    private static final String GET_GAME_INFO_URL_DEBUG = "https://game-cn-test.jieyou.shop/v1/api/one_game_info";
    private static final String GET_GAME_LIST_URL_DEBUG = "https://game-cn-test.jieyou.shop/v1/api/gamelist";
    private static final String GET_GAME_INFO_URL_PRO = "https://aws-gameapi.jieyou.shop/v1/api/one_game_info";
    private static final String GET_GAME_LIST_URL_PRO = "https://aws-gameapi.jieyou.shop/v1/api/gamelist";

    private static final Map<String, String> map = new HashMap<String, String>() {
        {
            put("Content-Type", "application/json;charset=utf-8");
            put("Authorization", "?");
        }
    };

    @Autowired
    private WebClient webClient;

    /**
     * 获取游戏信息
     */
    public ApiResult<BaiShunGameInfo> getGameInfo(BsGetGameInfoDTO dto) {
        String url = ServerConfig.isProduct() ? GET_GAME_INFO_URL_PRO : GET_GAME_INFO_URL_DEBUG;
        try {
            HttpResponseData<String> responseData = webClient.sendRestfulPost(url, JSON.toJSONString(dto), map);
            String body = responseData.getBody();
            if (StringUtils.isEmpty(body)) {
                logger.error("get sud game list error, http status={}", responseData.getStatus());
                return ApiResult.getError(HttpCode.SERVER_ERROR);
            }
            BsGameInfoVO vo = JSON.parseObject(body, BsGameInfoVO.class);
            if (vo.getCode() != 0) {
                logger.error("api error, http code={}", vo.getCode());
                return ApiResult.getError(new HttpCode(vo.getCode(), vo.getMsg()));
            }
            return ApiResult.getOk(vo.getData());
        } catch (Exception e) {
            logger.error("api error, url={} error msg={}", url, e.getMessage(), e);
            return ApiResult.getError(ApiCode.SERVER_ERROR);
        }
    }

    /**
     * 获取游戏列表
     */
    public ApiResult<List<BaiShunGameInfo>> getGameList(BsGetGameInfoDTO dto) {
        String url = ServerConfig.isProduct() ? GET_GAME_LIST_URL_PRO : GET_GAME_LIST_URL_DEBUG;
        try {
            HttpResponseData<String> responseData = webClient.sendRestfulPost(url, JSON.toJSONString(dto), map);
            String body = responseData.getBody();
            if (StringUtils.isEmpty(body)) {
                logger.error("get sud game list error, http status={}", responseData.getStatus());
                return ApiResult.getError(HttpCode.SERVER_ERROR);
            }
            BsGameListVO vo = JSON.parseObject(body, BsGameListVO.class);
            if (vo.getCode() != 0) {
                logger.error("api error, http code={}", vo.getCode());
                return ApiResult.getError(new HttpCode(vo.getCode(), vo.getMsg()));
            }
            return ApiResult.getOk(vo.getData());
        } catch (Exception e) {
            logger.error("api error, url={} error msg={}", url, e.getMessage(), e);
            return ApiResult.getError(ApiCode.SERVER_ERROR);
        }
    }
}
