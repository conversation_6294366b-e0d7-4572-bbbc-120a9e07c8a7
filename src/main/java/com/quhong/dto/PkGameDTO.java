package com.quhong.dto;

import com.quhong.handler.HttpEnvData;

/**
 * <AUTHOR>
 * @date 2022/11/9
 */
public class PkGameDTO extends HttpEnvData {

    private String pk_game_id;

    /**
     * 0代表全服 1代表邀请好友
     */
    private int pk_type;

    /**
     * pk时长
     */
    private int pk_time;

    private int pk_gid;

    private String pk_gicon;

    private String room_id;

    private int page;

    private String aid;

    private String pid;

    /**
     * 1日榜，2周榜，3月榜
     */
    private int opt_type;

    /**
     * 搜索房间好友关键字
     */
    private String key;

    public String getRoom_id() {
        return room_id;
    }

    public void setRoom_id(String room_id) {
        this.room_id = room_id;
    }

    public int getPk_type() {
        return pk_type;
    }

    public void setPk_type(int pk_type) {
        this.pk_type = pk_type;
    }

    public int getPk_time() {
        return pk_time;
    }

    public void setPk_time(int pk_time) {
        this.pk_time = pk_time;
    }

    public int getPk_gid() {
        return pk_gid;
    }

    public void setPk_gid(int pk_gid) {
        this.pk_gid = pk_gid;
    }

    public String getPk_gicon() {
        return pk_gicon;
    }

    public void setPk_gicon(String pk_gicon) {
        this.pk_gicon = pk_gicon;
    }

    public String getPk_game_id() {
        return pk_game_id;
    }

    public void setPk_game_id(String pk_game_id) {
        this.pk_game_id = pk_game_id;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public int getOpt_type() {
        return opt_type;
    }

    public void setOpt_type(int opt_type) {
        this.opt_type = opt_type;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }
}
