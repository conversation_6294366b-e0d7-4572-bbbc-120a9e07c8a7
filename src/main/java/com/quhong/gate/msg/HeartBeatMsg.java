package com.quhong.gate.msg;

import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsMsg;
import com.quhong.proto.YoustarProtoBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Message(cmd = Cmd.HEART_BEAT, isGate = true)
public class HeartBeatMsg extends MarsMsg {
    private static final Logger logger = LoggerFactory.getLogger(HeartBeatMsg.class);
    private int scene; //场景
    private long roomMsgId; //房间内上次获取的msgId
    private int micVersion; // 麦位版本信息
    private int musicVersion; // 音乐版本号
    private int slang; //app语言 1 英语 2 阿语 3 土耳其语
    private int vad; // 0 有人声，1 有声音没有人声，2 没有声音


    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoBase.HeartBeat msg = YoustarProtoBase.HeartBeat.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.scene = msg.getScene();
        this.roomMsgId = msg.getRoomMsgId();
        this.micVersion = msg.getMicVersion();
        this.musicVersion = msg.getMusicVersion();
        this.slang = msg.getSlang();
        this.vad = msg.getVad();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoBase.HeartBeat.Builder builder = YoustarProtoBase.HeartBeat.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setScene(scene);
        builder.setRoomMsgId(roomMsgId);
        builder.setMicVersion(micVersion);
        builder.setMusicVersion(musicVersion);
        builder.setSlang(slang);
        builder.setVad(vad);
        return builder.build().toByteArray();
    }

    public int getScene() {
        return scene;
    }

    public void setScene(int scene) {
        this.scene = scene;
    }

    public long getRoomMsgId() {
        return roomMsgId;
    }

    public void setRoomMsgId(long roomMsgId) {
        this.roomMsgId = roomMsgId;
    }

    public int getMicVersion() {
        return micVersion;
    }

    public void setMicVersion(int micVersion) {
        this.micVersion = micVersion;
    }

    public int getMusicVersion() {
        return musicVersion;
    }

    public void setMusicVersion(int musicVersion) {
        this.musicVersion = musicVersion;
    }

    public int getSlang() {
        return slang;
    }

    public void setSlang(int slang) {
        this.slang = slang;
    }

    public int getVad() {
        return vad;
    }

    public void setVad(int vad) {
        this.vad = vad;
    }
}
