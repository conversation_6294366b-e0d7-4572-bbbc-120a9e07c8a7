package com.quhong.handler;

import com.alibaba.fastjson.JSONObject;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.GiftContext;
import com.quhong.data.dto.SendGiftDTO;
import com.quhong.data.dto.UnlockGiftConfigDTO;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.AdvancedGiftDao;
import com.quhong.mongo.data.AdvancedGiftData;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.msg.room.RoomNotificationMsg;
import com.quhong.msg.room.UnlockGiftMsg;
import com.quhong.mysql.data.GiftData;
import com.quhong.redis.GiftPlayRedis;
import com.quhong.service.MarsMsgService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 解锁礼物处理
 */
@Component
public class UnlockGiftHandler implements IGiftHandler {
    protected static final Logger logger = LoggerFactory.getLogger(UnlockGiftHandler.class);

    @Resource
    private AdvancedGiftDao advancedGiftDao;
    @Resource
    private UnlockGiftHandler unlockGiftHandler;
    @Resource
    private GiftPlayRedis giftPlayRedis;
    @Resource
    private MarsMsgService marsMsgService;
    @Resource
    private ActorDao actorDao;

    @Cacheable(value = "getUnlockGiftMapCache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public Map<Integer, UnlockGiftConfigDTO> getUnlockGiftMapCache() {
        Map<Integer, UnlockGiftConfigDTO> unlockGiftMap = new HashMap<>();
        List<AdvancedGiftData> advancedGiftDataList = advancedGiftDao.getListByTypeFromCache(AdvancedGiftDao.PLAY_TYPE_UNLOCK);

        for (AdvancedGiftData data : advancedGiftDataList) {
            UnlockGiftConfigDTO dto = new UnlockGiftConfigDTO();
            String screenEn = data.getScreenEn();
            String screenAr = data.getScreenAr();
            String objId = data.get_id().toString();
            dto.setObjId(objId);
            dto.setGiftId(data.getGiftId());
            dto.setGiftNum(0);
            dto.setRateNum(data.getRateNum());
            dto.setScreenEn(screenEn);
            dto.setScreenAr(screenAr);

            List<Integer> giftIdList = data.getConfigList().stream().map(AdvancedGiftData.AdvancedConfig::getGiftId).collect(Collectors.toList());
            giftIdList.add(0, data.getGiftId());
            dto.setGiftIdList(giftIdList);
            unlockGiftMap.put(data.getGiftId(), dto);

            for (AdvancedGiftData.AdvancedConfig config : data.getConfigList()) {
                UnlockGiftConfigDTO dto1 = new UnlockGiftConfigDTO();
                BeanUtils.copyProperties(config, dto1);
                dto1.setObjId(objId);
                dto1.setGiftIdList(giftIdList);
                unlockGiftMap.put(config.getGiftId(), dto1);
            }

        }
        return unlockGiftMap;
    }

    @Override
    public void process(SendGiftDTO req, GiftData giftData, GiftContext context) {
        long timeMillis = System.currentTimeMillis();
        handlerProcess(req, giftData, context);
        logger.info("handler unlockGift Process aid1={} cost={}", req.getAid(), System.currentTimeMillis() - timeMillis);

    }


    public void handlerProcess(SendGiftDTO req, GiftData giftData, GiftContext context) {

        try {
            String reqUid = req.getUid();
            int giftId = req.getGiftId();
            int totalNum = req.getNumber() * context.getAidSet().size();
            int currentTime = DateHelper.getNowSeconds();
            ActorData actorData = actorDao.getActorDataFromCache(reqUid);
            if(actorData == null){
                return;
            }

            String username = actorData.getName();
            String userHead = ImageUrlGenerator.generateRoomUserUrl(actorData.getHead());
            String giftName = giftData.getGname();
            Map<Integer, UnlockGiftConfigDTO> unlockGiftMap = unlockGiftHandler.getUnlockGiftMapCache();
            // logger.info("handlerProcess unlockGiftMap: {}", JSONObject.toJSONString(unlockGiftMap));
            UnlockGiftConfigDTO unlockGiftConfigDTO = unlockGiftMap.get(giftId);
            if (unlockGiftConfigDTO != null) {
                String objId = unlockGiftConfigDTO.getObjId();
                int incUnlockNum = totalNum * unlockGiftConfigDTO.getRateNum();
                List<Integer> unlockGiftConfigIds = unlockGiftConfigDTO.getGiftIdList();
                for (Integer giftLockId : unlockGiftConfigIds) {
                    UnlockGiftConfigDTO dto = unlockGiftMap.get(giftLockId);
                    if (dto != null && dto.getGiftNum() > 0) {
                        int maxGiftNum = dto.getGiftNum();
                        int lockTimeOut = dto.getLockTimeOut();
                        int expireTime = giftPlayRedis.getUnlockGiftExpireScore(objId, reqUid, giftLockId);
                        if (expireTime <= 0) {
                            int afterUnlockNum = giftPlayRedis.incUnlockGiftNum(objId, reqUid, giftLockId, incUnlockNum);
                            if (afterUnlockNum >= maxGiftNum) {
                                int afterExpireTime = currentTime + lockTimeOut;
                                giftPlayRedis.setUnlockGiftExpireScore(objId, reqUid, giftLockId, afterExpireTime);
                                giftPlayRedis.removeUnlockGiftNumKey(objId, reqUid, giftLockId);

                                if(!StringUtils.isEmpty(dto.getScreenEn())){
                                    RoomNotificationMsg msg = new RoomNotificationMsg();
                                    msg.setUid(reqUid);
                                    msg.setUser_name(username);
                                    msg.setUser_head(userHead);
                                    msg.setText(dto.getScreenEn().replace("#username#", username).replace("#giftName#", giftName));
                                    msg.setText_ar(dto.getScreenAr().replace("#username#", username).replace("#giftName#", giftName));
                                    List<HighlightTextObject> list = new ArrayList<>();
                                    HighlightTextObject object = new HighlightTextObject();
                                    object.setText(username);
                                    object.setHighlightColor("#FFE200");
                                    list.add(object);
                                    msg.setHighlight_text(list);
                                    msg.setHighlight_text_ar(list);
                                    msg.setWeb_type(1);
                                    logger.info("handlerProcess RoomNotificationMsg send {}", JSONObject.toJSONString(msg));
                                    marsMsgService.asyncSendMsg("all_" + req.getRoomId(), null, msg, false);
                                }
                            }
                        }

                    }
                }

            }
        } catch (Exception e) {
            logger.error("handler UnlockGift Process error e={}", e.getMessage(), e);
        }
    }

    public void unLockGiftCheck() {
        try {
            Map<Integer, UnlockGiftConfigDTO> unlockGiftMap = unlockGiftHandler.getUnlockGiftMapCache();
            int currentTime = DateHelper.getNowSeconds();
            for (Integer giftId : unlockGiftMap.keySet()) {
                UnlockGiftConfigDTO dto = unlockGiftMap.get(giftId);
                if (dto.getGiftNum() > 0) {
                    Map<String, Integer> rankingMap = giftPlayRedis.getUnlockGiftExpireMapByScore(dto.getObjId(), giftId, 0, currentTime);
                    for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
                        String rankUid = entry.getKey();
                        giftPlayRedis.removeUnlockGiftExpire(dto.getObjId(), giftId, rankUid);
                        int selectUnlockGift = giftPlayRedis.getSelectUnlockGift(dto.getObjId(), rankUid, 0);
                        if(selectUnlockGift == giftId){
                            giftPlayRedis.deleteSelectUnlockGift(dto.getObjId(), rankUid);
                            UnlockGiftMsg msg = new UnlockGiftMsg();
                            msg.setGiftId(dto.getGiftIdList().get(0));
                            msg.setSelectGiftId(dto.getGiftIdList().get(0));
                            marsMsgService.asyncSendPlayerMsg("", "", rankUid, msg, true);
                            logger.info("deleteSelectUnlock msg:{}", JSONObject.toJSONString(msg));
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("unLockGiftCheck error e={}", e.getMessage(), e);
        }
    }


}
