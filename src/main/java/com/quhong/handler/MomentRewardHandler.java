
package com.quhong.handler;

import com.alibaba.fastjson.JSON;
import com.quhong.api.ApiMomentService;
import com.quhong.constant.GiftConstant;
import com.quhong.data.GiftContext;
import com.quhong.data.dto.GiftRewardDTO;
import com.quhong.data.dto.SendGiftDTO;
import com.quhong.mysql.data.GiftData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;

/**
 * 个朋友圈礼物打赏
 */
@Component
public class MomentRewardHandler implements IGiftHandler {
    protected static final Logger logger = LoggerFactory.getLogger(MomentRewardHandler.class);

    @Resource
    private ApiMomentService apiMomentService;

    @Override
    public void process(SendGiftDTO req, GiftData giftData, GiftContext context) {
        if (req.getSendScene() == GiftConstant.SEND_SCENE_MOMENT_REWARD) {
            GiftRewardDTO dto = new GiftRewardDTO();
            dto.setUid(req.getUid());
            dto.setMid(req.getMid());
            dto.setGiftId(giftData.getRid());
            dto.setGiftPrice(giftData.getPrice());
            dto.setGiftIcon(giftData.getGicon());
            dto.setPriceType(1 == giftData.getGtype() ? 1 : 2);
            dto.setRewardPrice(giftData.getPrice() * req.getNumber() * context.getAidSet().size());
            dto.setRewardNum(req.getNumber());
            CompletableFuture<Void> completableFuture = apiMomentService.giftReward(dto);
            completableFuture.whenComplete((result, throwable) -> {
                if (null != throwable) {
                    logger.error("send moment gift reward error dto={}", JSON.toJSONString(dto), throwable);
                } else {
                    logger.info("finish send moment gift reward dto={}", JSON.toJSONString(dto));
                }
            });
        }
    }
}
