package com.quhong.handler;

import com.mongodb.bulk.BulkWriteResult;
import com.quhong.config.GiftYamlConfig;
import com.quhong.constant.GiftConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.GiftContext;
import com.quhong.data.dto.SendGiftDTO;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.dao.GiftNumDao;
import com.quhong.mongo.data.GiftNum;
import com.quhong.mq.MqSenderService;
import com.quhong.mysql.data.GiftData;
import com.quhong.redis.UidAidDevoteLogRedis;
import com.quhong.service.BadgeService;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 记录礼物接收及发钻
 */
@Component
public class ReceiveGiftHandler implements IGiftHandler {
    protected static final Logger logger = LoggerFactory.getLogger(ReceiveGiftHandler.class);

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;
    @Resource
    private GiftNumDao giftNumDao;
    @Resource
    protected MqSenderService mqService;
    @Resource
    private BadgeService badgeService;
    @Resource
    private GiftYamlConfig giftYamlConfig;
    @Resource
    private UidAidDevoteLogRedis uidAidDevoteLogRedis;

    @Override
    public void process(SendGiftDTO req, GiftData giftData, GiftContext context) {
        // 礼物接收用户发钻
        doAsyncChargeDiamonds(giftData, context);
        // 主播礼物贡献
        doReceiveGiftContribution(req, giftData, context);
        // 累计用户收到某礼物的总数量
        doReceiveGiftStatistics(req, context);
    }

    /**
     * 异步打钻
     */
    private void doAsyncChargeDiamonds(GiftData giftData, GiftContext context) {
        if (!context.isBeansGift()) {
            return;
        }
        for (String aid : context.getAidSet()) {
            MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
            moneyDetailReq.setRandomId();
            moneyDetailReq.setUid(aid);
            moneyDetailReq.setAtype(GiftConstant.AT_RECEIVE_GIFTS);
            moneyDetailReq.setChanged(context.getEarnBeans());
            moneyDetailReq.setTitle(GiftConstant.RECEIVE_GIFTS_TITLE);
            moneyDetailReq.setDesc(String.format(GiftConstant.RECEIVE_GIFTS_DESC, giftData.getGname(), context.getSendData().getRid()));
            moneyDetailReq.setRoomId(null == context.getOwnerData() ? null : RoomUtils.formatRoomId(context.getOwnerData().getUid()));
            mqService.asyncChargeDiamonds(moneyDetailReq);
        }
    }

    /**
     * 累计用户对某个主播的礼物贡献
     */
    private void doReceiveGiftContribution(SendGiftDTO req, GiftData giftData, GiftContext context) {
        if (!context.isBeansGift()) {
            return;
        }
        String roomId = req.getRoomId();
        // 私信礼物不计入
        if (!StringUtils.isEmpty(roomId)) {
            boolean isVoiceRoom = RoomUtils.isVoiceRoom(roomId);
            int devote = req.getNumber() * giftData.getPrice();
            String uid = req.getUid();
            for (String aid : context.getAidSet()) {
                if (isVoiceRoom) {
                    uidAidDevoteLogRedis.incrementData(roomId, uid, aid, devote);
                }
            }
        }
    }

    /**
     * 累计用户收到某礼物的总数量
     */
    private void doReceiveGiftStatistics(SendGiftDTO req, GiftContext context) {
        // 提高处理效率，非私聊礼物处理
        if (req.getSendType() != GiftConstant.ST_SEND_ONE) {
            bulkStatistics(req, context);
        }
    }

    private void bulkStatistics(SendGiftDTO req, GiftContext context) {
        // 收到盲盒礼物，实际累加的是开出来的礼物
        int giftId = context.isRandomGift() ? context.getGiftInfo().getGiftId() : req.getGiftId();
        List<String> bulkOpsList = new ArrayList<>(context.getAidSet());
        long timeMillis = System.currentTimeMillis();
        BulkOperations operations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, GiftNum.class);
        Update update = new Update();
        update.inc("giftNum", req.getNumber());
        update.set("mtime", DateHelper.getNowSeconds());
        for (String aid : bulkOpsList) {
            operations.upsert(new Query(Criteria.where("aid").is(aid).and("giftId").is(giftId)), update);
        }
        BulkWriteResult result = operations.execute();
        logger.info("receiveGiftStatistics bulkUpsert size={} cost={}", bulkOpsList.size(), System.currentTimeMillis() - timeMillis);
        // 处理等级勋章
        if (!giftYamlConfig.getGiftBadgeMap().containsKey(giftId)) {
            return;
        }
        List<GiftNum> allGiftNum = giftNumDao.getByUidSet(bulkOpsList, giftId);
        for (GiftNum giftNum : allGiftNum) {
            logger.info("gift badge giftNum record uid={} aid={} giftId={} sendNum={} total={}",
                    req.getUid(), giftNum.getAid(), giftId, req.getNumber(), giftNum.getGiftNum());
            badgeService.doGiftBadge(giftNum.getAid(), giftId, giftNum.getGiftNum(),
                    giftNum.getGiftNum() - req.getNumber(), giftYamlConfig.getGiftBadgeMap(), giftYamlConfig.getGiftBadgeIdMap());
        }
    }
}
