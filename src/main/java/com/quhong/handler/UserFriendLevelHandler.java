package com.quhong.handler;

import com.quhong.data.CommonMqTopicData;
import com.quhong.data.RechargeInfo;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.service.UserFriendLevelHonorService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component
public class UserFriendLevelHandler implements RechargeHandler {

    private static final Logger logger = LoggerFactory.getLogger(UserFriendLevelHandler.class);

    @Resource
    private UserFriendLevelHonorService userFriendLevelHonorService;

    @Override
    public void process(RechargeInfo rechargeInfo) {
        logger.info("UserFriendLevelHandler process rechargeInfo{}", rechargeInfo);
        // 这里要转化一下，这个topic_common_msg的mq充值消息是最全的，task_common_msg的充值消息只有java pay的充值
        CommonMqTopicData data = new CommonMqTopicData
                (rechargeInfo.getUid(), "", "", rechargeInfo.getOrderId(), CommonMqTaskConstant.RECHARGE_DIAMONDS, rechargeInfo.getRechargeDiamond());
        userFriendLevelHonorService.parseUserExpData(data);
    }

}
