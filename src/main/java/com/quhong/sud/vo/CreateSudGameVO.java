package com.quhong.sud.vo;

import com.alibaba.fastjson.JSONObject;
import com.quhong.ludo.data.ConfigInfo;
import com.quhong.mongo.data.SudGamePlayerData;
import com.quhong.msg.obj.SudPlayerInfoObject;
import com.quhong.msg.room.SudGameChangeMsg;
import com.quhong.sud.data.SudGameConfigInfo;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/14
 */
@Data
public class CreateSudGameVO {

    private String gameId; // 游戏id
    private int gameType; // 1碰碰 2Ludo 3UMO
    private String selfUid; // 游戏创建者
    private int playerNumber; // 创建游戏时由app选择玩家数量
    private int currency; // 入场费
    private int currencyType; // 游戏币类型 1 心心 2 钻石
    private int status; // 0 无 1匹配中 2进行中 3结束 4游戏关闭 5 匹配中暂停
    private JSONObject rule;
    private List<SudGamePlayerData> playerList;
    private String leaderUid; // 游戏房队长uid，(队长可改入场费，以及开始游戏)

    // web调用增加返回
    private Integer handleType; // 1 调用join或者createOrJoin接口返回 2 调用quit接口返回

    public SudGameChangeMsg toMarsMsg() {
        SudGameChangeMsg msg = new SudGameChangeMsg();
        msg.setGameId(gameId);
        msg.setGameType(gameType);
        msg.setSelfUid(selfUid);
        msg.setPlayerNumber(playerNumber);
        msg.setCurrency(currency);
        msg.setCurrencyType(currencyType);
        msg.setStatus(status);
        msg.setRule(null == rule ? "" : rule.toJSONString());
        msg.setLeaderUid(null == leaderUid ? "" : leaderUid);
        List<SudPlayerInfoObject> playerInfoObjects = new ArrayList<>();
        SudPlayerInfoObject playerInfoObject;
        for (SudGamePlayerData sudGamePlayerData : playerList) {
            playerInfoObject = new SudPlayerInfoObject();
            BeanUtils.copyProperties(sudGamePlayerData, playerInfoObject);
            playerInfoObjects.add(playerInfoObject);
        }
        msg.setPlayerList(playerInfoObjects);
        return msg;
    }
}
