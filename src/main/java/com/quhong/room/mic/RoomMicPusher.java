package com.quhong.room.mic;

import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.timers.DelayTask;
import com.quhong.core.timers.TimerService;
import com.quhong.data.ActorData;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.msg.obj.RoomMicInfoObject;
import com.quhong.msg.obj.RoomMicUserObject;
import com.quhong.msg.room.RoomMicChangePushMsg;
import com.quhong.mysql.dao.RoomMicDao;
import com.quhong.mysql.data.RoomMicData;
import com.quhong.mysql.data.RoomMicListData;
import com.quhong.net.sender.PlayerMsgSender;
import com.quhong.redis.CheatGiftRedis;
import com.quhong.redis.RoomMicRedis;
import com.quhong.redis.SudGameRedis;
import com.quhong.room.api.ThirdPartApiAdapter;
import com.quhong.room.api.data.MicChangeDTO;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.room.processors.RoomMsgProcessor;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.room.rooms.Room;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.RoomMicListVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class RoomMicPusher {
    private static final Logger logger = LoggerFactory.getLogger(RoomMicPusher.class);

    public static final long CHECK_MIC_INTERVAL = 5 * 60 * 1000L;

    @Autowired
    private RoomActorCache actorCache;
    @Autowired
    private RoomMicService micService;
    @Autowired
    private RoomMicDao micDao;
    @Autowired
    private MongoRoomDao roomDao;
    @Autowired
    private RoomMsgProcessor msgProcessor;
    @Autowired
    private PlayerMsgSender msgSender;
    @Autowired
    private RoomPlayerRedis roomPlayerRedis;
    @Autowired
    private RoomMicPusher micPusher;
    @Autowired
    private CheatGiftRedis cheatGiftRedis;
    @Autowired
    private RoomMicRedis roomMicRedis;
    @Resource
    private ThirdPartApiAdapter thirdPartApi;
    @Resource
    private ActorDao actorDao;
    @Resource
    private SudGameRedis sudGameRedis;

    public RoomMicPusher() {

    }

    public void pushRoomMics(Room room, String fromUid, boolean forceUpdate) {
        if (RoomUtils.isGameRoom(room.getRoomId())) {
            pushRoomMics(room, fromUid, forceUpdate, true);
        } else {
            pushRoomMics(room, fromUid, forceUpdate, false);
        }
    }

    public void pushRoomMics(Room room, String fromUid, boolean forceUpdate, boolean containMe) {
        room.getSlowTaskQueue(fromUid).add(new Task() {
            @Override
            protected void execute() {
                RoomMicChangePushMsg pushMsg = createMsg(room, forceUpdate);
                if (pushMsg == null) {
                    return;
                }
                // 2996881房间不发送mars消息，用于验证保障消息有没有问题
                if (!"r:5cc2797c66dc630025bf17c2".equals(room.getRoomId())) {
                    msgProcessor.sendMsgToAll(room, fromUid, pushMsg, containMe, null);
                }
                TimerService.getService().addDelay(new DelayTask(null, 1000) {
                    @Override
                    protected void execute() {
                        // 向第三方发送房间广播推送麦位信息
                        MicChangeDTO micChangeDTO = new MicChangeDTO(room.getRoomId(), pushMsg.getVersion());
                        thirdPartApi.micChange(micChangeDTO, room.getRoomData().getRtcType());
                    }
                });
            }
        });
    }

    public void pushMicVersionToActor(Room room, String toUid, int micVersion) {
        if (micVersion == 0) {
            logger.info("do not push mic info msg. mic version is zero. roomId={} toUid={}", room.getRoomId(), toUid);
            return;
        }
        int version = micDao.getVersion(room.getRoomId());
        if (version <= micVersion) {
            logger.info("do not push mic info msg. mic version less then version. reqVersion={} version={}  roomId={} toUid={}", version, micVersion, room.getRoomId(), toUid);
            return;
        }
        room.getSlowTaskQueue(toUid).add(new Task() {
            @Override
            protected void execute() {
                RoomMicChangePushMsg pushMsg = createMsg(room, false);
                if (pushMsg == null) {
                    return;
                }
                pushMsg.getProtoHeader().setRoomID(room.getRoomId());
                msgSender.sendMsg(toUid, pushMsg);
            }
        });
    }

    public void checkMic(Room room, boolean force) {
        long lastCheckTime = room.getCheckMicTime();
        long curTime = System.currentTimeMillis();
        if (!force) {
            if (curTime - lastCheckTime < CHECK_MIC_INTERVAL) {
                return;
            }
        }
        room.updateCheckMic();
        room.getSlowTaskQueue(room.getRoomId()).add(new Task() {
            @Override
            protected void execute() {
                doCheckMics(room);
            }
        });
    }

    public void doCheckMics(Room room) {
        logger.info("check room mics. roomId={}", room.getRoomId());
        MongoRoomData roomData = roomDao.findData(room.getRoomId());
        try (DistributeLock lock = new DistributeLock(micService.createLockKey(room.getRoomId()))) {
            lock.lock();
            RoomMicListData micListData = micService.getRoomMicList(room.getRoomId(), roomDao.getRoomType(roomData), roomData.getMicSize());
            for (RoomMicData micData : micListData.getList()) {
                String uid = micData.getUid();
                if (StringUtils.isEmpty(uid)) {
                    continue;
                }
                if (micData.getMicLock() > 0) {
                    // 麦位已锁，移除
                    logger.error("room mics has locked. op down user. position={} roomId={} uid={}", micData.getPosition(), room.getRoomId(), micData.getUid());
                    boolean ret = micService.downRoomMic(micListData, micData.getUid(), false, true, 2, roomData);
                    if (ret) {
                        micPusher.pushRoomMics(room, null, false);
                    }
                } else {
                    String redisRoomId = roomPlayerRedis.getActorRoomStatus(uid);
                    // 用户不在房间，移除麦位
                    if (redisRoomId == null || (!room.getRoomId().equals(redisRoomId))) {
                        logger.error("room mics has dirty data. op down user. position={} roomId={} uid={}", micData.getPosition(), room.getRoomId(), micData.getUid());
                        boolean ret = micService.downRoomMic(micListData, micData.getUid(), false, true, 4, roomData);
                        if (ret) {
                            micPusher.pushRoomMics(room, null, false);
                        }
                    }
                }
            }
        }
    }

    private RoomMicChangePushMsg createMsg(Room room, boolean forceUpdate) {
        String roomId = room.getRoomId();
        MongoRoomData roomData = roomDao.findData(roomId);
        if (roomData == null) {
            logger.error("can not find room data. roomId={}", roomId);
            return null;
        }
        room.getRoomData().setRtcType(roomData.getRtc_type());
        RoomMicListData listData = micService.getRoomMicList(roomId, roomDao.getRoomType(roomData), roomData.getMicSize());
        RoomMicChangePushMsg msg = new RoomMicChangePushMsg();
        List<RoomMicInfoObject> infoList = new ArrayList<>();
        Map<String, CheatGiftRedis.CheatGiftData> map = cheatGiftRedis.fillCheatGift(listData.getList());
        ownerRidCheck(roomData);

        List<String> inGameList = new ArrayList<>();
        if (RoomUtils.isGameRoom(roomId)) {
            inGameList = sudGameRedis.getInGameUserList(roomId);
        }

        // 用于判断是否有某个用户同时在两个麦上
        int micCount = 0;
        for (RoomMicData micData : listData.getList()) {
            RoomMicInfoObject micObj = new RoomMicInfoObject();
            micObj.setIndex(micData.getPosition());
            micObj.setMute(micData.getMute());
            micObj.setStatus(micData.getStatus());
            String uid = micData.getUid();
            if (!StringUtils.isEmpty(uid)) {
                RoomMicUserObject userObject = createUser(roomId, uid, forceUpdate, roomData);
                // 以下仅作业务处理
                CheatGiftRedis.CheatGiftData cheatGiftData = map.get(uid);
                userObject.setVoice_type(cheatGiftData.getVoiceType());
                if (!StringUtils.isEmpty(cheatGiftData.getPrankMicFrame())) {
                    userObject.setMic_frame(cheatGiftData.getPrankMicFrame());
                }
                userObject.setUpMicTime(micData.getUpMicTime());
                userObject.setThirdPartId(micData.getThirdPartId());
                userObject.setGameRunning(inGameList.contains(uid) ? 1 : 0);
                micObj.setUser(userObject);
                micCount++;
            }
            infoList.add(micObj);
        }
        msg.setList(infoList);
        msg.setVersion(listData.getVersion());
        // 保存最新的麦位数据
        RoomMicListVo vo = new RoomMicListVo();
        vo.setVersion(listData.getVersion());
        vo.setList(infoList);
        vo.setRoomId(listData.getRoomId());
        roomMicRedis.saveRoomMicToRedis(roomId, vo);
        roomMicRedis.setMicActorCount(roomId, micCount);
        return msg;
    }

    private void ownerRidCheck(MongoRoomData roomData) {
        if (0 == roomData.getOwnerRid()) {
            ActorData actorData = actorDao.getActorData(RoomUtils.getRoomHostId(roomData.getRid()));
            if (actorData == null) {
                logger.error("cannot find actor data uid={}", RoomUtils.getRoomHostId(roomData.getRid()));
                return;
            }
            roomDao.updateField(roomData.getRid(), "ownerRid", actorData.getRid());
            roomData.setOwnerRid(actorData.getRid());
        }
    }

    private RoomMicUserObject createUser(String roomId, String uid, boolean forceUpdate, MongoRoomData roomData) {
        RoomActorDetailData detailData = actorCache.getData(roomId, uid, forceUpdate);
        RoomMicUserObject userObject = new RoomMicUserObject();
        userObject.setAid(detailData.getAid());
        userObject.setName(detailData.getName());
        userObject.setHead(detailData.getHead());
        userObject.setMic_frame(detailData.getMicFrame());
        userObject.setRipple_url(detailData.getRippleUrl());
        userObject.setVip_level(detailData.getVipLevel());
        userObject.setVipMedal(detailData.getVipMedal());
        userObject.setRole(detailData.getRole());
        userObject.setIdentify(detailData.getIdentify());
        userObject.setViceHost(detailData.getViceHost());
        userObject.setStreamId(thirdPartApi.generateActorStreamId(roomData.getOwnerRid(), detailData.getRid(), detailData.getAid(), roomData.getRtc_type()));
        return userObject;
    }
}
