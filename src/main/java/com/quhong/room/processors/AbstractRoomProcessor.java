package com.quhong.room.processors;

import com.alibaba.fastjson.JSONObject;
import com.quhong.room.RoomService;
import com.quhong.room.api.ThirdPartApiAdapter;
import com.quhong.room.rooms.Room;
import org.springframework.beans.factory.annotation.Autowired;

public class AbstractRoomProcessor implements RoomProcessor {

    @Autowired
    protected RoomService roomService;
    @Autowired
    protected ThirdPartApiAdapter thirdPartApi;


    /**
     * 房间人数较低时，发送一个用户列表保障消息
     */
    protected void sendUserChangeMsg(Room room) {
        // 房间人数较低时，发送一个用户列表保障消息
        if (room.getActorMap().size() < 10) {
            JSONObject msgJson = new JSONObject();
            msgJson.put("uidSet", room.getActorMap().keySet());
            thirdPartApi.sendBroadcastMessage(room.getRoomId(), "userChange", msgJson.toJSONString());
        }
    }
}
