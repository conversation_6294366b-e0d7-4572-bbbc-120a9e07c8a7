package com.quhong.mongo.data;

import com.quhong.mongo.dao.RoomLockSourceDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 个人房间锁
 */
@Document(collection = RoomLockSourceDao.TABLE_NAME)
public class RoomLockSourceData {
    @Id
    private ObjectId _id;
    private int pid;
    private int c_time; // 创建时间戳
    private int status; // 状态
    private int diamonds; // 购买所花的钻石
    private int vaild_days; // 销售的天数


    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public int getPid() {
        return pid;
    }

    public void setPid(int pid) {
        this.pid = pid;
    }

    public int getC_time() {
        return c_time;
    }

    public void setC_time(int c_time) {
        this.c_time = c_time;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getDiamonds() {
        return diamonds;
    }

    public void setDiamonds(int diamonds) {
        this.diamonds = diamonds;
    }

    public int getVaild_days() {
        return vaild_days;
    }

    public void setVaild_days(int vaild_days) {
        this.vaild_days = vaild_days;
    }
}
