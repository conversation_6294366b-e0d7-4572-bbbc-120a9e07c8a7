package com.quhong.mongo.data;

import com.quhong.mongo.dao.FollowDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 关注和粉丝
 */
@Document(collection = FollowDao.TABLE_NAME)
public class FollowData {

    @Id
    private ObjectId _id;
    // uid
    private String uid;
    // uid关注的用户uid
    private String aid;
    private Integer ctime;
    @Field("is_new")
    private Integer isNew;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getIsNew() {
        return isNew;
    }

    public void setIsNew(Integer isNew) {
        this.isNew = isNew;
    }
}
