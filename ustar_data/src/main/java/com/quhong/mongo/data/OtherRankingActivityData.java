package com.quhong.mongo.data;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * 手动配置类活动模板
 */
@Document(collection = "other_ranking_activity_template")
public class OtherRankingActivityData {

    @Id
    private ObjectId _id;
    private String acNameEn; // 英文活动名
    private String acNameAr; // 阿语活动名
    private String acUrl; // 活动链接 用于公屏消息点击跳转
    private Integer startTime; // 活动开始时间
    private Integer endTime; // 活动结束时间
    private List<Integer> activityGiftList; // 活动礼物
    private List<RankingConfig> rankingRewardList; // 需要哪些排行榜列表、 活动结束后奖励配置
    private List<ReachingConfig> reachingConfigList; // 等级奖励，列表不为空时在奖励页面展示
    private Integer status; // 是否已结束  0:运行中  1:已结束
    private int roundNum;         // 同一个活动多次运行; 列如: 盲盒周榜、 心愿周榜
    private int popUp; // 结束后是否弹窗展示排名信息
    private int dailyNum;         // 按每天统计, 1-7
    private int dailyStartTime;   // 每天开始时间
    private int dailyEndTime;     // 每天结束时间
    private Integer ctime; // 创建时间
    private Integer mtime; // 更新时间
    private int activityType; // 榜单类型 0: 普通榜单 1: pk榜单
    private String nextActivityId; // 跟下一个活动id相关联
    private List<String> rankUidList; // 指定uid增加分数

    public static class RankingConfig {
        private String rankNameEn; // 英文活动名
        private String rankNameAr; // 英文活动名
        private Integer rankType; // 排行榜属性 1发送者 2接收者 3房间
        private int calculateMethod; //  计算方式: 1计算礼物数 2计算钻石数
        private int rankingGender; //  排行榜展示对象：0全体用户 1男性用户 2女性用户
        private int pushStatus; //  排行榜是否推送：0:否  1:是
        private int dailyRank; //  是否进行统计每日榜单：0:否  1:是
        private List<RankingRewardConfig> rankingRewardConfigList; // 排行榜奖励配置

        public String getRankNameEn() {
            return rankNameEn;
        }

        public void setRankNameEn(String rankNameEn) {
            this.rankNameEn = rankNameEn;
        }

        public String getRankNameAr() {
            return rankNameAr;
        }

        public void setRankNameAr(String rankNameAr) {
            this.rankNameAr = rankNameAr;
        }

        public Integer getRankType() {
            return rankType;
        }

        public void setRankType(Integer rankType) {
            this.rankType = rankType;
        }

        public int getCalculateMethod() {
            return calculateMethod;
        }

        public void setCalculateMethod(int calculateMethod) {
            this.calculateMethod = calculateMethod;
        }

        public int getRankingGender() {
            return rankingGender;
        }

        public void setRankingGender(int rankingGender) {
            this.rankingGender = rankingGender;
        }

        public int getPushStatus() {
            return pushStatus;
        }

        public void setPushStatus(int pushStatus) {
            this.pushStatus = pushStatus;
        }

        public int getDailyRank() {
            return dailyRank;
        }

        public void setDailyRank(int dailyRank) {
            this.dailyRank = dailyRank;
        }

        public List<RankingRewardConfig> getRankingRewardConfigList() {
            return rankingRewardConfigList;
        }

        public void setRankingRewardConfigList(List<RankingRewardConfig> rankingRewardConfigList) {
            this.rankingRewardConfigList = rankingRewardConfigList;
        }
    }


    public static class RankingRewardConfig {
        private List<Integer> rewardObject; // 奖励对象, [1],[2],[3],[4,5,6]...
        private List<RewardConfigDetail> rewardConfigDetailList; // 奖励配置
        private String resourceKey;

        public List<Integer> getRewardObject() {
            return rewardObject;
        }

        public void setRewardObject(List<Integer> rewardObject) {
            this.rewardObject = rewardObject;
        }

        public List<RewardConfigDetail> getRewardConfigDetailList() {
            return rewardConfigDetailList;
        }

        public void setRewardConfigDetailList(List<RewardConfigDetail> rewardConfigDetailList) {
            this.rewardConfigDetailList = rewardConfigDetailList;
        }

        public String getResourceKey() {
            return resourceKey;
        }

        public void setResourceKey(String resourceKey) {
            this.resourceKey = resourceKey;
        }
    }


    public static class ReachingConfig {
        private Integer reachingRewardType; // 等级奖励属性 1发送者 2接收者
        private int reachingGender; // 奖励对象 0全体用户 1男性用户 2女性用户
        private Integer calculateMethod; // 计算方式 1计算礼物数 2计算钻石数
        private List<ReachingReward> reachingRewardList; // 新版奖励配置，支持多种类型的奖励

        public Integer getReachingRewardType() {
            return reachingRewardType;
        }

        public void setReachingRewardType(Integer reachingRewardType) {
            this.reachingRewardType = reachingRewardType;
        }

        public int getReachingGender() {
            return reachingGender;
        }

        public void setReachingGender(int reachingGender) {
            this.reachingGender = reachingGender;
        }

        public Integer getCalculateMethod() {
            return calculateMethod;
        }

        public void setCalculateMethod(Integer calculateMethod) {
            this.calculateMethod = calculateMethod;
        }

        public List<ReachingReward> getReachingRewardList() {
            return reachingRewardList;
        }

        public void setReachingRewardList(List<ReachingReward> reachingRewardList) {
            this.reachingRewardList = reachingRewardList;
        }
    }

    public static class ReachingReward {
        private Integer giftNum; // 礼物数量(礼物钻石总数)
        private List<RewardConfigDetail> rewardConfigDetailList; // 奖励配置

        public Integer getGiftNum() {
            return giftNum;
        }

        public void setGiftNum(Integer giftNum) {
            this.giftNum = giftNum;
        }

        public List<RewardConfigDetail> getRewardConfigDetailList() {
            return rewardConfigDetailList;
        }

        public void setRewardConfigDetailList(List<RewardConfigDetail> rewardConfigDetailList) {
            this.rewardConfigDetailList = rewardConfigDetailList;
        }
    }

    public static class RewardConfigDetail {
        // 资源类型 背包礼物:gift 麦位框:mic 气泡框:buddle 入场动画:ride 麦位声波:ripple 钻石:diamond 勋章:badge 浮屏:float_screen 钻石:diamond
        private String rewardType;
        private Integer sourceId; // 奖励资源id
        private Integer rewardTime; //资源时长（天） 0永久
        private Integer rewardNum; // 礼物数量 可能为空

        public String getRewardType() {
            return rewardType;
        }

        public void setRewardType(String rewardType) {
            this.rewardType = rewardType;
        }

        public Integer getSourceId() {
            return sourceId;
        }

        public void setSourceId(Integer sourceId) {
            this.sourceId = sourceId;
        }

        public Integer getRewardTime() {
            return rewardTime;
        }

        public void setRewardTime(Integer rewardTime) {
            this.rewardTime = rewardTime;
        }

        public Integer getRewardNum() {
            return rewardNum;
        }

        public void setRewardNum(Integer rewardNum) {
            this.rewardNum = rewardNum;
        }
    }


    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getAcNameEn() {
        return acNameEn;
    }

    public void setAcNameEn(String acNameEn) {
        this.acNameEn = acNameEn;
    }

    public String getAcNameAr() {
        return acNameAr;
    }

    public void setAcNameAr(String acNameAr) {
        this.acNameAr = acNameAr;
    }

    public String getAcUrl() {
        return acUrl;
    }

    public void setAcUrl(String acUrl) {
        this.acUrl = acUrl;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public List<Integer> getActivityGiftList() {
        return activityGiftList;
    }

    public void setActivityGiftList(List<Integer> activityGiftList) {
        this.activityGiftList = activityGiftList;
    }

    public List<RankingConfig> getRankingRewardList() {
        return rankingRewardList;
    }

    public void setRankingRewardList(List<RankingConfig> rankingRewardList) {
        this.rankingRewardList = rankingRewardList;
    }

    public List<ReachingConfig> getReachingConfigList() {
        return reachingConfigList;
    }

    public void setReachingConfigList(List<ReachingConfig> reachingConfigList) {
        this.reachingConfigList = reachingConfigList;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public int getRoundNum() {
        return roundNum;
    }

    public void setRoundNum(int roundNum) {
        this.roundNum = roundNum;
    }

    public int getDailyNum() {
        return dailyNum;
    }

    public void setDailyNum(int dailyNum) {
        this.dailyNum = dailyNum;
    }

    public int getDailyStartTime() {
        return dailyStartTime;
    }

    public void setDailyStartTime(int dailyStartTime) {
        this.dailyStartTime = dailyStartTime;
    }

    public int getDailyEndTime() {
        return dailyEndTime;
    }

    public void setDailyEndTime(int dailyEndTime) {
        this.dailyEndTime = dailyEndTime;
    }

    public int getPopUp() {
        return popUp;
    }

    public void setPopUp(int popUp) {
        this.popUp = popUp;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public int getActivityType() {
        return activityType;
    }

    public void setActivityType(int activityType) {
        this.activityType = activityType;
    }

    public String getNextActivityId() {
        return nextActivityId;
    }

    public void setNextActivityId(String nextActivityId) {
        this.nextActivityId = nextActivityId;
    }

    public List<String> getRankUidList() {
        return rankUidList;
    }

    public void setRankUidList(List<String> rankUidList) {
        this.rankUidList = rankUidList;
    }
}
