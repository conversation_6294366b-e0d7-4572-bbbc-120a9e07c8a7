package com.quhong.mongo.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.quhong.mongo.dao.RocketRewardConfigDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Document(collection = RocketRewardConfigDao.TABLE_NAME)
public class RocketRewardConfigData {

    @Id
    @JSONField(serialize = false)
    private ObjectId _id;

    /**
     * 火箭配置版本 新版火箭值为2， 2最新的为2.0，其他1.0老版本,
     */
    private int rocketVer;

    /**
     * 火箭等级 取值1-8，目前只能有8个
     */
    private int rocketLevel;

    /**
     * 火箭发射限制
     */
    private int rocketLaunchLimit;

    /**
     * 是否全房间通知
     */
    private int  allRoomBroadcast;

    private List<RewardConfigDetail> detailList; // 火箭1.0列表字段，新版本已经废弃

    private List<ResourceMeta> superMetaList;  // 公共奖励资源列表

    private Integer superMaxCount;  // 公共奖励资源最大中取人数

    private List<ResourceMeta> roomTop3MetaList;  // 贡献top3资源列表

    private List<ResourceMeta> roomOwnerMetaList;  // 房主奖励资源列表


    public static class RewardConfigDetail {

        // 资源类型 背包礼物:gift 麦位框:mic 气泡框:buddle 入场动画:ride 麦位声波:ripple 钻石:diamond 勋章:badge 浮屏:float_screen 钻石:diamond
        private String rewardType;
        private Integer sourceId; // 奖励资源id
        private String rewardNameEn; // 奖励名称
        private String rewardNameAr; // 奖励名称阿语
        private String rewardIcon; // 奖励资源介绍图片
        private Integer rewardTime; //资源时长（天） 0永久
        private Integer rewardNum; // 礼物数量 可能为空
        private Integer lowerLimit; // 需要花费钻石的下限
        private Integer upperLimit; // 需要花费钻石的上限
        private int rate; // 获取概率

        public String getRewardType() {
            return rewardType;
        }

        public void setRewardType(String rewardType) {
            this.rewardType = rewardType;
        }

        public Integer getSourceId() {
            return sourceId;
        }

        public void setSourceId(Integer sourceId) {
            this.sourceId = sourceId;
        }

        public String getRewardIcon() {
            return rewardIcon;
        }

        public void setRewardIcon(String rewardIcon) {
            this.rewardIcon = rewardIcon;
        }

        public Integer getRewardTime() {
            return rewardTime;
        }

        public void setRewardTime(Integer rewardTime) {
            this.rewardTime = rewardTime;
        }

        public Integer getRewardNum() {
            return rewardNum;
        }

        public void setRewardNum(Integer rewardNum) {
            this.rewardNum = rewardNum;
        }

        public Integer getLowerLimit() {
            return lowerLimit;
        }

        public void setLowerLimit(Integer lowerLimit) {
            this.lowerLimit = lowerLimit;
        }

        public Integer getUpperLimit() {
            return upperLimit;
        }

        public void setUpperLimit(Integer upperLimit) {
            this.upperLimit = upperLimit;
        }

        public int getRate() {
            return rate;
        }

        public void setRate(int rate) {
            this.rate = rate;
        }

        public String getRewardNameEn() {
            return rewardNameEn;
        }

        public void setRewardNameEn(String rewardNameEn) {
            this.rewardNameEn = rewardNameEn;
        }

        public String getRewardNameAr() {
            return rewardNameAr;
        }

        public void setRewardNameAr(String rewardNameAr) {
            this.rewardNameAr = rewardNameAr;
        }
    }

    public static class ResourceMeta {
        private String metaId;               // 自动生成的mongoId String
        private String resourceIcon;         // 奖品图标
        private String resourcePreview;      // 奖品预览图，详情图
        private String resourceNameEn;       // 奖品名称英语
        private String resourceNameAr;       // 奖品名称阿语
        private int resourcePrice;           // 奖品价值
        private int resourceType;            // 奖品奖励类型
        private int resourceId;              // 奖品资源id
        private int resourceTime;            // 奖品奖励时长
        private int resourceNumber;          // 奖品奖励数量
        private int status;                  // 状态
        private String rateNumber;           // 概率 0.01-0.99
        private int inRoomScreen;            // 本房间公屏与横幅
        private int allRoomScreen;           // 全房间公屏与横幅
        private int broadcast;               // 房间横幅广播
        private Integer lowerLimit; // 需要花费钻石的下限
        private Integer upperLimit; // 需要花费钻石的上限

        public String getMetaId() {
            return metaId;
        }

        public void setMetaId(String metaId) {
            this.metaId = metaId;
        }

        public String getResourceIcon() {
            return resourceIcon;
        }

        public void setResourceIcon(String resourceIcon) {
            this.resourceIcon = resourceIcon;
        }

        public String getResourcePreview() {
            return resourcePreview;
        }

        public void setResourcePreview(String resourcePreview) {
            this.resourcePreview = resourcePreview;
        }

        public String getResourceNameEn() {
            return resourceNameEn;
        }

        public void setResourceNameEn(String resourceNameEn) {
            this.resourceNameEn = resourceNameEn;
        }

        public String getResourceNameAr() {
            return resourceNameAr;
        }

        public void setResourceNameAr(String resourceNameAr) {
            this.resourceNameAr = resourceNameAr;
        }

        public int getResourcePrice() {
            return resourcePrice;
        }

        public void setResourcePrice(int resourcePrice) {
            this.resourcePrice = resourcePrice;
        }

        public int getResourceType() {
            return resourceType;
        }

        public void setResourceType(int resourceType) {
            this.resourceType = resourceType;
        }

        public int getResourceTime() {
            return resourceTime;
        }

        public void setResourceTime(int resourceTime) {
            this.resourceTime = resourceTime;
        }

        public int getResourceId() {
            return resourceId;
        }

        public void setResourceId(int resourceId) {
            this.resourceId = resourceId;
        }

        public int getResourceNumber() {
            return resourceNumber;
        }

        public void setResourceNumber(int resourceNumber) {
            this.resourceNumber = resourceNumber;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public String getRateNumber() {
            return rateNumber;
        }

        public void setRateNumber(String rateNumber) {
            this.rateNumber = rateNumber;
        }

        public int getInRoomScreen() {
            return inRoomScreen;
        }

        public void setInRoomScreen(int inRoomScreen) {
            this.inRoomScreen = inRoomScreen;
        }

        public int getAllRoomScreen() {
            return allRoomScreen;
        }

        public void setAllRoomScreen(int allRoomScreen) {
            this.allRoomScreen = allRoomScreen;
        }

        public int getBroadcast() {
            return broadcast;
        }

        public void setBroadcast(int broadcast) {
            this.broadcast = broadcast;
        }

        public Integer getLowerLimit() {
            return lowerLimit;
        }

        public void setLowerLimit(Integer lowerLimit) {
            this.lowerLimit = lowerLimit;
        }

        public Integer getUpperLimit() {
            return upperLimit;
        }

        public void setUpperLimit(Integer upperLimit) {
            this.upperLimit = upperLimit;
        }

    }

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public int getRocketLevel() {
        return rocketLevel;
    }

    public void setRocketLevel(int rocketLevel) {
        this.rocketLevel = rocketLevel;
    }

    public int getRocketLaunchLimit() {
        return rocketLaunchLimit;
    }

    public void setRocketLaunchLimit(int rocketLaunchLimit) {
        this.rocketLaunchLimit = rocketLaunchLimit;
    }

    public List<RewardConfigDetail> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<RewardConfigDetail> detailList) {
        this.detailList = detailList;
    }

    public int getRocketVer() {
        return rocketVer;
    }

    public void setRocketVer(int rocketVer) {
        this.rocketVer = rocketVer;
    }

    public List<ResourceMeta> getSuperMetaList() {
        return superMetaList;
    }

    public void setSuperMetaList(List<ResourceMeta> superMetaList) {
        this.superMetaList = superMetaList;
    }

    public List<ResourceMeta> getRoomOwnerMetaList() {
        return roomOwnerMetaList;
    }

    public void setRoomOwnerMetaList(List<ResourceMeta> roomOwnerMetaList) {
        this.roomOwnerMetaList = roomOwnerMetaList;
    }

    public List<ResourceMeta> getRoomTop3MetaList() {
        return roomTop3MetaList;
    }

    public void setRoomTop3MetaList(List<ResourceMeta> roomTop3MetaList) {
        this.roomTop3MetaList = roomTop3MetaList;
    }

    public Integer getSuperMaxCount() {
        return superMaxCount;
    }

    public void setSuperMaxCount(Integer superMaxCount) {
        this.superMaxCount = superMaxCount;
    }

    public int getAllRoomBroadcast() {
        return allRoomBroadcast;
    }

    public void setAllRoomBroadcast(int allRoomBroadcast) {
        this.allRoomBroadcast = allRoomBroadcast;
    }
}
