package com.quhong.mongo.data;

import com.quhong.mongo.dao.GiftLevelBadgeDao;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * 礼物等级勋章配置
 */
@Document(collection = GiftLevelBadgeDao.TABLE_NAME)
public class GiftLevelBadgeData {
    @Id
    private String mongoId;                                // 数据id
    private Integer giftId;                                // 礼物id
    private String giftIcon;                               // 礼物图标
    private Integer countType;                             // 统计类型  0:发送礼物 1:接收礼物
    private String giftBannerDesc;                         // 礼物banner描述
    private List<LevelNumBadge> levelBadgeList;            // 等级数量及勋章配置列表

    public static class LevelNumBadge{
        private Integer levelNumber;                       // 等级数量
        private Integer badgeId;                           // 勋章id
        private String smallIcon;                          // 勋章小图标

        public Integer getLevelNumber() {
            return levelNumber;
        }

        public void setLevelNumber(Integer levelNumber) {
            this.levelNumber = levelNumber;
        }

        public Integer getBadgeId() {
            return badgeId;
        }

        public void setBadgeId(Integer badgeId) {
            this.badgeId = badgeId;
        }

        public String getSmallIcon() {
            return smallIcon;
        }

        public void setSmallIcon(String smallIcon) {
            this.smallIcon = smallIcon;
        }
    }

    public String getMongoId() {
        return mongoId;
    }

    public void setMongoId(String mongoId) {
        this.mongoId = mongoId;
    }

    public Integer getGiftId() {
        return giftId;
    }

    public void setGiftId(Integer giftId) {
        this.giftId = giftId;
    }

    public String getGiftIcon() {
        return giftIcon;
    }

    public void setGiftIcon(String giftIcon) {
        this.giftIcon = giftIcon;
    }

    public Integer getCountType() {
        return countType;
    }

    public void setCountType(Integer countType) {
        this.countType = countType;
    }

    public String getGiftBannerDesc() {
        return giftBannerDesc;
    }

    public void setGiftBannerDesc(String giftBannerDesc) {
        this.giftBannerDesc = giftBannerDesc;
    }

    public List<LevelNumBadge> getLevelBadgeList() {
        return levelBadgeList;
    }

    public void setLevelBadgeList(List<LevelNumBadge> levelBadgeList) {
        this.levelBadgeList = levelBadgeList;
    }
}
