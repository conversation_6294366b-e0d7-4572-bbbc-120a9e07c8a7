package com.quhong.mongo.data;

import com.quhong.mongo.dao.BuddleSourceDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = BuddleSourceDao.TABLE_NAME)
public class BuddleSourceData {
    @Id
    private ObjectId _id;
    private String buddle_icon;    // 展示的icon
    private String buddle_preview; // 预览展示的图(可以是动图)
    private int buddle_id; // id，1是默认， 5是vip5，6是vip6，其他的10以上
    private String ios_buddle_source; // ios资源下载路径
    private String ios_buddle_source_2x; // ios_xxdpi的资源
    private String ios_buddle_source_3x; // ios_xxxdpi的资源
    private String android_buddle_source; // 安卓xxdpi的资源
    private String android_buddle_source_1x; // 安卓xdpi的资源
    private String android_buddle_source_3x; // 安卓xxxdpi的资源
    private String name; // 气泡的名字
    private String namear; // 气泡的阿语名字
    private long c_time; //
    private int status; // 状态    1 代表有效， 0代表失效
    private int forder; // 排序用
    private int is_activity; //  0 代表购买  1代表抽奖获, 活动得, 2代表是vip
    private String buddle_color; // 客户端的展示字体颜色

    private String buddle_tl;  // top_left 左上角图标
    private String buddle_tr;  // top_right 右上角图标
    private String buddle_bl;  // bottom_left 左下角图标
    private String buddle_br;  // bottom_right 右下角图标
    private int top;  // 上间距
    private int bottom;  // 下间距
    private int left;  // 左间距
    private int right;  // 右间距

    private int beans;  // 售价
    private int days;  // 售卖天数
    private int buy_type ;
    private int item_type; // 1: 奖励相关  2: 荣誉相关 3: vip相关  4: 用户等级相关 5 商店购买
    private int is_new;  // 上新标识字段 1: 是
    private String labelNameEn; // 气泡的角标名字
    private String labelNameAr; // 气泡的角标名字

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getBuddle_icon() {
        return buddle_icon;
    }

    public void setBuddle_icon(String buddle_icon) {
        this.buddle_icon = buddle_icon;
    }

    public String getBuddle_preview() {
        return buddle_preview;
    }

    public void setBuddle_preview(String buddle_preview) {
        this.buddle_preview = buddle_preview;
    }

    public int getBuddle_id() {
        return buddle_id;
    }

    public void setBuddle_id(int buddle_id) {
        this.buddle_id = buddle_id;
    }

    public String getIos_buddle_source() {
        return ios_buddle_source;
    }

    public void setIos_buddle_source(String ios_buddle_source) {
        this.ios_buddle_source = ios_buddle_source;
    }

    public String getIos_buddle_source_2x() {
        return ios_buddle_source_2x;
    }

    public void setIos_buddle_source_2x(String ios_buddle_source_2x) {
        this.ios_buddle_source_2x = ios_buddle_source_2x;
    }

    public String getIos_buddle_source_3x() {
        return ios_buddle_source_3x;
    }

    public void setIos_buddle_source_3x(String ios_buddle_source_3x) {
        this.ios_buddle_source_3x = ios_buddle_source_3x;
    }

    public String getAndroid_buddle_source() {
        return android_buddle_source;
    }

    public void setAndroid_buddle_source(String android_buddle_source) {
        this.android_buddle_source = android_buddle_source;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public long getC_time() {
        return c_time;
    }

    public void setC_time(long c_time) {
        this.c_time = c_time;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getForder() {
        return forder;
    }

    public void setForder(int forder) {
        this.forder = forder;
    }

    public int getIs_activity() {
        return is_activity;
    }

    public void setIs_activity(int is_activity) {
        this.is_activity = is_activity;
    }

    public String getBuddle_color() {
        return buddle_color;
    }

    public void setBuddle_color(String buddle_color) {
        this.buddle_color = buddle_color;
    }

    public String getNamear() {
        return namear;
    }

    public void setNamear(String namear) {
        this.namear = namear;
    }

    public String getBuddle_tl() {
        return buddle_tl;
    }

    public void setBuddle_tl(String buddle_tl) {
        this.buddle_tl = buddle_tl;
    }

    public String getBuddle_tr() {
        return buddle_tr;
    }

    public void setBuddle_tr(String buddle_tr) {
        this.buddle_tr = buddle_tr;
    }

    public String getBuddle_bl() {
        return buddle_bl;
    }

    public void setBuddle_bl(String buddle_bl) {
        this.buddle_bl = buddle_bl;
    }

    public String getBuddle_br() {
        return buddle_br;
    }

    public void setBuddle_br(String buddle_br) {
        this.buddle_br = buddle_br;
    }

    public int getTop() {
        return top;
    }

    public void setTop(int top) {
        this.top = top;
    }

    public int getBottom() {
        return bottom;
    }

    public void setBottom(int bottom) {
        this.bottom = bottom;
    }

    public int getLeft() {
        return left;
    }

    public void setLeft(int left) {
        this.left = left;
    }

    public int getRight() {
        return right;
    }

    public void setRight(int right) {
        this.right = right;
    }

    public String getAndroid_buddle_source_1x() {
        return android_buddle_source_1x;
    }

    public void setAndroid_buddle_source_1x(String android_buddle_source_1x) {
        this.android_buddle_source_1x = android_buddle_source_1x;
    }

    public String getAndroid_buddle_source_3x() {
        return android_buddle_source_3x;
    }

    public void setAndroid_buddle_source_3x(String android_buddle_source_3x) {
        this.android_buddle_source_3x = android_buddle_source_3x;
    }

    public int getBeans() {
        return beans;
    }

    public void setBeans(int beans) {
        this.beans = beans;
    }

    public int getDays() {
        return days;
    }

    public void setDays(int days) {
        this.days = days;
    }

    public int getBuy_type() {
        return buy_type;
    }

    public void setBuy_type(int buy_type) {
        this.buy_type = buy_type;
    }

    public int getItem_type() {
        return item_type;
    }

    public void setItem_type(int item_type) {
        this.item_type = item_type;
    }

    public int getIs_new() {
        return is_new;
    }

    public void setIs_new(int is_new) {
        this.is_new = is_new;
    }

    public String getLabelNameEn() {
        return labelNameEn;
    }

    public void setLabelNameEn(String labelNameEn) {
        this.labelNameEn = labelNameEn;
    }

    public String getLabelNameAr() {
        return labelNameAr;
    }

    public void setLabelNameAr(String labelNameAr) {
        this.labelNameAr = labelNameAr;
    }
}
