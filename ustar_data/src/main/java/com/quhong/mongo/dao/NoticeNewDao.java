package com.quhong.mongo.dao;

import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.NoticeNewData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Lazy
@Component
public class NoticeNewDao {
    private static final Logger logger = LoggerFactory.getLogger(NoticeNewDao.class);

    public static final String TABLE_NAME = "notice_new";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public int findCount(String uid) {
        try {
            Criteria criteria = Criteria.where("aid").is(uid).and("status").is(1).and("delete_start").ne(1);
            return (int) mongoTemplate.count(new Query(criteria), NoticeNewData.class);
        } catch (Exception e) {
            logger.error("find notice new data count error. uid={} {}", uid, e.getMessage(), e);
        }
        return 0;
    }

    /**
     * 官方消息数量
     *
     * @param uid
     * @return
     */
    public int findAllNotificationCount(String uid) {
        try {
            Criteria criteria = Criteria.where("aid").is(uid).and("delete_start").ne(1).and("ntype").ne(1);
            return (int) mongoTemplate.count(new Query(criteria), NoticeNewData.class);
        } catch (Exception e) {
            logger.error("find all notification notice data count error. uid={} {}", uid, e.getMessage(), e);
        }
        return 0;
    }

    /**
     * 官方消息未读数量
     *
     * @param uid
     * @return
     */
    public int findNotificationCount(String uid) {
        try {
            Criteria criteria = Criteria.where("aid").is(uid).and("status").is(1).and("delete_start").ne(1).and("ntype").ne(1);
            return (int) mongoTemplate.count(new Query(criteria), NoticeNewData.class);
        } catch (Exception e) {
            logger.error("find notification notice new data count error. uid={} {}", uid, e.getMessage(), e);
        }
        return 0;
    }

    /**
     * 活动消息数量
     *
     * @param uid
     * @return
     */
    public int findAllActivityCount(String uid) {
        try {
            Criteria criteria = Criteria.where("aid").is(uid).and("delete_start").ne(1).and("ntype").is(1);
            return (int) mongoTemplate.count(new Query(criteria), NoticeNewData.class);
        } catch (Exception e) {
            logger.error("find all activity notice data count error. uid={} {}", uid, e.getMessage(), e);
        }
        return 0;
    }

    /**
     * 活动消息未读数量
     *
     * @param uid
     * @return
     */
    public int findActivityCount(String uid) {
        try {
            Criteria criteria = Criteria.where("aid").is(uid).and("status").is(1).and("delete_start").ne(1).and("ntype").is(1);
            return (int) mongoTemplate.count(new Query(criteria), NoticeNewData.class);
        } catch (Exception e) {
            logger.error("find activity notice new data count error. uid={} {}", uid, e.getMessage(), e);
        }
        return 0;
    }

    public NoticeNewData findLastData(String uid) {
        try {
            Criteria criteria = Criteria.where("aid").is(uid).and("status").is(1).and("delete_start").ne(1);
            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.DESC, "ctime");
            query.with(sort);
            query.limit(1);
            return mongoTemplate.findOne(query, NoticeNewData.class);
        } catch (Exception e) {
            logger.error("find last notice new data data error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public void cleanUnread(String uid) {
        try {
            Criteria criteria = Criteria.where("aid").is(uid);
            Update update = new Update();
            update.set("status", 0);
            mongoTemplate.updateMulti(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("clean unread. uid={} {}", e.getMessage(), e);
        }
    }

    /**
     * 将未读官方消息变已读
     *
     * @param uid
     */
    public void cleanNotificationUnread(String uid) {
        try {
            Criteria criteria = Criteria.where("aid").is(uid).and("delete_start").ne(1).and("ntype").ne(1);
            Update update = new Update();
            update.set("status", 0);
            mongoTemplate.updateMulti(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("clean notification unread. uid={} {}", e.getMessage(), e);
        }
    }

    /**
     * 删除官方通知（逻辑删除）
     *
     * @param uid
     */
    public void deleteNotificationMsg(String uid) {
        try {
            Criteria criteria = Criteria.where("aid").is(uid).and("delete_start").ne(1).and("ntype").ne(1);
            Update update = new Update();
            update.set("delete_start", 1);
            mongoTemplate.updateMulti(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("delete notification msg. uid={} {}", e.getMessage(), e);
        }
    }

    /**
     * 将未读活动消息变已读
     *
     * @param uid
     */
    public void cleanActivityUnread(String uid) {
        try {
            Criteria criteria = Criteria.where("aid").is(uid).and("delete_start").ne(1).and("ntype").is(1);
            Update update = new Update();
            update.set("status", 0);
            mongoTemplate.updateMulti(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("clean activity unread. uid={} {}", e.getMessage(), e);
        }
    }

    /**
     * 删除活动通知（逻辑删除）
     *
     * @param uid
     */
    public void deleteActivityMsg(String uid) {
        try {
            Criteria criteria = Criteria.where("aid").is(uid).and("delete_start").ne(1).and("ntype").is(1);
            Update update = new Update();
            update.set("delete_start", 1);
            mongoTemplate.updateMulti(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("delete activity msg. uid={} {}", e.getMessage(), e);
        }
    }

    public void save(NoticeNewData data) {
        try {
            mongoTemplate.save(data);
        } catch (Exception e) {
            logger.error("save notice new data error uid={} {}", data.getAid(), e.getMessage());
        }
    }

    public void clearNoticeNewMsg(List<String> objIdList) {
        try {
            Criteria criteria = Criteria.where("official_id").in(objIdList);
            mongoTemplate.remove(new Query(criteria), TABLE_NAME);
        } catch (Exception e) {
            logger.error("clearNoticeNewMsg. error={}", e.getMessage(), e);
        }
    }


}
