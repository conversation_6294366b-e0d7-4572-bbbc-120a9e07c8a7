package com.quhong.mongo.dao;

import com.mongodb.client.result.UpdateResult;
import com.quhong.constant.ActivityConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.LuckyLotteryActivity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

@Lazy
@Component
public class LuckyLotteryActivityDao {
    private static final Logger logger = LoggerFactory.getLogger(LuckyLotteryActivityDao.class);

    public static final String TABLE_NAME = "lucky_lottery_activity_template";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;


    public LuckyLotteryActivity findData(String id) {
        try {
            return mongoTemplate.findOne(new Query(Criteria.where("_id").is(id)), LuckyLotteryActivity.class);
        } catch (Exception e) {
            logger.error("getActivity error Activity={} {}", id, e.getMessage(), e);
            return null;
        }
    }

    public void save(LuckyLotteryActivity data) {
        int nowSeconds = DateHelper.getNowSeconds();
        data.setCtime(nowSeconds);
        data.setMtime(nowSeconds);
        data.setStatus(ActivityConstant.STATUS_INIT);

        mongoTemplate.save(data);
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(data.getAcUrl());
        urlBuilder.queryParam("activityId", data.get_id().toString());
        String url = urlBuilder.build(false).encode().toUriString();
        updateData(data, new Update().set("acUrl", url));
    }

    /**
     * 获取所有正在执行的活动
     */
    public List<LuckyLotteryActivity> getRankingActivities() {
        try {
            Query query = new Query(Criteria.where("status").is(ActivityConstant.STATUS_INIT));
            return mongoTemplate.find(query, LuckyLotteryActivity.class);
        } catch (Exception e) {
            logger.error("getRankingActivities error={}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取所有已结束未结算的活动
     */
    public List<LuckyLotteryActivity> getInitActivities() {
        try {
            Query query = new Query(Criteria.where("status").is(ActivityConstant.STATUS_INIT));
            return mongoTemplate.find(query, LuckyLotteryActivity.class);
        } catch (Exception e) {
            logger.error("getInitActivities error={}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    public UpdateResult updateData(LuckyLotteryActivity data, Update update) {
        try {
            return mongoTemplate.updateFirst(new Query(Criteria.where("_id").is(data.get_id())), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("updateData error.  id={} {}", data.get_id(), e.getMessage(), e);
            return null;
        }
    }

    public List<LuckyLotteryActivity> selectPage(String acNameEn, int start, int size) {
        try {
            // 查询条件
            Criteria criteria = new Criteria();
            if (!StringUtils.isEmpty(acNameEn)) {
                criteria = Criteria.where("acNameEn").regex(Pattern.compile(".*?" + acNameEn + ".*"));
            }
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    // 排序
                    Aggregation.sort(Sort.by(Sort.Direction.DESC, "mtime")),
                    // 分页
                    Aggregation.skip(start),
                    Aggregation.limit(size)
            );
            return mongoTemplate.aggregate(aggregation, TABLE_NAME, LuckyLotteryActivity.class).getMappedResults();
        } catch (Exception e) {
            logger.error("LuckyLotteryActivity selectPage error.{}", e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    public long selectCount() {
        return mongoTemplate.count(new Query(), TABLE_NAME);
    }
}
