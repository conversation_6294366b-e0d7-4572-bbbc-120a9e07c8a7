package com.quhong.mongo.dao;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.ResourceKeyConfigData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;

@Component
public class ResourceKeyConfigDao {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    public static final String TABLE_NAME = "resource_key_config";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public void save(ResourceKeyConfigData data) {
        try {
            mongoTemplate.save(data);
        } catch (Exception e) {
            logger.error("save ResourceKeyConfigData error. {}", e.getMessage(), e);
        }
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public ResourceKeyConfigData findByKey(String key) {
        return mongoTemplate.findOne(new Query(Criteria.where("key").is(key)), ResourceKeyConfigData.class);
    }

    public ResourceKeyConfigData findById(String id) {
        return mongoTemplate.findById(id, ResourceKeyConfigData.class);
    }


    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE, key = "#p0.hashCode()")
    public List<ResourceKeyConfigData> findListByKeys(Set<String> keySet) {
        return mongoTemplate.find(new Query(Criteria.where("key").in(keySet)), ResourceKeyConfigData.class);
    }

    @Cacheable(value = "findListByKeyList", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE, key = "#p0.hashCode()")
    public List<ResourceKeyConfigData> findListByKeyList(List<String> keyList) {
        return mongoTemplate.find(new Query(Criteria.where("key").in(keyList)), ResourceKeyConfigData.class);
    }


    public List<ResourceKeyConfigData> selectPage(int keyType, String search, int start, int size) {
        try {
            // 查询条件
            Criteria criteria = new Criteria();
            if (!StringUtils.isEmpty(search)){
                criteria.orOperator(Criteria.where("name").regex(".*?" + search + ".*?"), Criteria.where("key").regex(".*?" + search + ".*?"));
            }
            if(keyType != -1){
                criteria.and("keyType").is(keyType);
            }
            Sort sort = Sort.by(Sort.Direction.DESC, "_id");

            Query query = new Query(criteria);
            query.with(sort).skip(start).limit(size);
            return mongoTemplate.find(query, ResourceKeyConfigData.class);
        } catch (Exception e) {
            logger.error("ResourceKeyConfigData selectPage error.{}", e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public long selectCount(int keyType, String search) {
        // 查询条件
        Criteria criteria = new Criteria();
        if (!StringUtils.isEmpty(search)){
            criteria.orOperator(Criteria.where("name").regex(".*?" + search + ".*?"), Criteria.where("key").regex(".*?" + search + ".*?"));
        }
        if(keyType != -1){
            criteria.and("keyType").is(keyType);
        }

        return mongoTemplate.count(new Query(criteria), TABLE_NAME);
    }

    public ResourceKeyConfigData selectByKey(String key) {
        return mongoTemplate.findOne(new Query(Criteria.where("key").is(key)), ResourceKeyConfigData.class);
    }


    public void insert(ResourceKeyConfigData data){
        try {
            mongoTemplate.insert(data);
        } catch (Exception e) {
            logger.error("ResourceKeyConfigData insert error. msg = {}",e.getMessage(),e);
        }
    }

    public void updateData(String docId, Update update) {
        try {
            mongoTemplate.updateFirst(new Query(Criteria.where("_id").is(docId)), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("FloatScreenSourceData  updateData error.  id={} {}", docId, e.getMessage(), e);
        }
    }

}
