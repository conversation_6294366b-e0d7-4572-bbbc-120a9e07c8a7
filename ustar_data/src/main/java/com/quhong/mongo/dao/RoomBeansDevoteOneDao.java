package com.quhong.mongo.dao;

import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.RoomBeansDevoteOneData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/11/11
 */
@Component
public class RoomBeansDevoteOneDao {

    private static final Logger logger = LoggerFactory.getLogger(RoomBeansDevoteOneDao.class);

    public static final String TABLE_NAME = "room_beans_devote_one";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public RoomBeansDevoteOneData findData(String uid, String roomId) {
        try {
            Criteria criteria = Criteria.where("room_id").is(roomId).and("uid").is(uid);
            return mongoTemplate.findOne(new Query(criteria), RoomBeansDevoteOneData.class);
        } catch (Exception e) {
            logger.error("find room beans devote one data error. uid={}, roomId={} {}", uid, roomId, e.getMessage(), e);
            return null;
        }
    }

    public void save(RoomBeansDevoteOneData data) {
        try {
            mongoTemplate.save(data);
        } catch (Exception e) {
            logger.error("find room beans devote one data error. uid={}, roomId={} {}", data.getUid(), data.getRoom_id(), e.getMessage(), e);
        }
    }

    public void incRoomBeansDevote(String uid, String roomId, int beans) {
        try {
            Query query = new Query(Criteria.where("room_id").is(roomId).and("uid").is(uid));
            Update update = new Update();
            update.inc("devote", beans);
//            mongoTemplate.updateFirst(query, update, TABLE_NAME);
            mongoTemplate.upsert(query, update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("increase room beans devote error. roomId={}, beans={} {}", roomId, beans, e.getMessage(), e);
        }
    }
}
