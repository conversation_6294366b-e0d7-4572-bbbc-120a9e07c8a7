package com.quhong.mongo.dao;

import com.quhong.cache.CacheMap;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.EmojiConfigData;
import com.quhong.mongo.data.EmojiResourceData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/11
 */
@Lazy
@Component
public class EmojiConfigDao {

    private static final Logger logger = LoggerFactory.getLogger(EmojiConfigDao.class);

    public static final String TABLE_NAME = "emoji_config";
    private static final long CACHE_TIME_MILLIS = 60 * 1000L;  // 1分钟缓存
    public static final Integer EMOJI_TYPE_VOICE_MIC = 0;  // 语音房麦位表情
    public static final Integer EMOJI_TYPE_VOICE_MSG = 1;  // 语音房消息表情
    public static final Integer EMOJI_TYPE_PRIVATE_MSG = 2;  // 私信消息表情
    public static final Integer EMOJI_TYPE_GAME_MIC = 3;  // 游戏房麦位表情
    public static final Integer EMOJI_TYPE_GAME_MSG = 4;  // 游戏房消息表情
    public static final Integer EMOJI_TYPE_PRIVATE_ACTION = 5;  // 私信快捷互动表情

    private final CacheMap<String, List<EmojiConfigData>> cacheMap;

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;


    public EmojiConfigDao() {
        this.cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }

    private String getKey(int emojiType) {
        return "emoji_config_list" + emojiType;
    }

    public List<EmojiConfigData> findEmojiConfigList(int emojiType) {
        String key = getKey(emojiType);
        List<EmojiConfigData> dataList = cacheMap.getData(key);
        if (!CollectionUtils.isEmpty(dataList)) {
            return dataList;
        }
        dataList = findEmojiConfigByType(emojiType);
        if (!CollectionUtils.isEmpty(dataList)) {
            cacheMap.cacheData(key, dataList);
        }
        return dataList;
    }

    private List<EmojiConfigData> findEmojiConfigByType(int emojiType) {
        try {
            Criteria criteria = Criteria.where("emojiTypeList").in(emojiType).and("status").is(1);
            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.ASC, "orderNum");
            query.with(sort);
            return mongoTemplate.find(query, EmojiConfigData.class);
        } catch (Exception e) {
            logger.error("findEmojiConfigByType error. {}", e.getMessage(), e);
        }
        return Collections.emptyList();
    }



    /**
     * 运营系统
     */
    public List<EmojiConfigData> selectEmojiConfigPage(int emojiType, int status, String search, int start, int size) {
        try {
            // 查询条件
            Criteria criteria = new Criteria();
            if (emojiType != -1) {
                criteria.and("emojiTypeList").in(emojiType);
            }

            if (status != -1) {
                criteria.and("status").is(status);
            }
            if (!StringUtils.isEmpty(search)) {
                criteria.and("name").regex(".*?" + search + ".*?");
            }
            Sort sort = Sort.by(Sort.Order.asc("orderNum"), Sort.Order.desc("_id"));
            Query query = new Query(criteria);
            query.with(sort).skip(start).limit(size);
            return mongoTemplate.find(query, EmojiConfigData.class);
        } catch (Exception e) {
            logger.error("EmojiResourceData selectPage error.{}", e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    public long selectCount(int emojiType, int status, String search) {
        // 查询条件
        Criteria criteria = new Criteria();
        if (emojiType != -1) {
            criteria.and("emojiTypeList").in(emojiType);
        }
        if (status != -1) {
            criteria.and("status").is(status);
        }
        if (!StringUtils.isEmpty(search)) {
            criteria.and("name").regex(".*?" + search + ".*?");
        }
        return mongoTemplate.count(new Query(criteria), TABLE_NAME);
    }

    public void insert(EmojiConfigData data) {
        try {
            mongoTemplate.insert(data);
        } catch (Exception e) {
            logger.error("EmojiResourceData insert error. msg = {}", e.getMessage(), e);
        }
    }

    public void updateData(String docId, Update update) {
        try {
            mongoTemplate.updateFirst(new Query(Criteria.where("_id").is(docId)), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("MicFrameSourceData  updateData error.  id={} {}", docId, e.getMessage(), e);
        }
    }

    public EmojiConfigData getEmojiConfigByID(String docId) {
        try {
            return mongoTemplate.findById(docId, EmojiConfigData.class);
        } catch (Exception e) {
            logger.error("EmojiConfigData query error. msg = {}", e.getMessage(), e);

        }
        return null;
    }
}
