package com.quhong.mongo.dao;

import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.BeautifulRidChangeLog;
import com.quhong.mongo.data.BeautifulRidData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Lazy
@Component
public class BeautifulRidChangeLogDao {
    public static final String TABLE_NAME = "beautiful_rid_change_log";
    private static final Logger logger = LoggerFactory.getLogger(BeautifulRidChangeLogDao.class);

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemp;

    /**
     * 从靓号记录中获取用户数据
     *
     * @param beforeRidList
     * @return
     */
    public List<BeautifulRidChangeLog> listByBeforeRids(List<Integer> beforeRidList) {
        Query query = new Query(Criteria.where("before_rid").in(beforeRidList));
        List<BeautifulRidChangeLog> list = mongoTemp.find(query, BeautifulRidChangeLog.class);
        if (list == null) {
            return new ArrayList<>();
        }
        return list;
    }

    public List<BeautifulRidChangeLog> listByUid(String uid) {
        Query query = new Query(Criteria.where("uid").is(uid));
        query.with(Sort.by(Sort.Order.desc("c_time")));
        List<BeautifulRidChangeLog> list = mongoTemp.find(query, BeautifulRidChangeLog.class);
        if (list == null) {
            return new ArrayList<>();
        }
        return list;
    }

    public void save(BeautifulRidChangeLog log) {
        try {
            mongoTemp.save(log);
        } catch (Exception e) {
            logger.error("save log fail log={} {}", log, e.getMessage());
        }
    }

    public void updateLogHonorLevel(String uid, String beautifulRid, int honorLevel) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid).and("after_rid").is(beautifulRid);
            Update update = new Update();
            update.set("honor_level", honorLevel);
            mongoTemp.updateFirst(new Query(criteria), update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("updateLogHonorLevel data error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    public List<BeautifulRidChangeLog> listByHonorLevel(String uid, int honorLevel) {
        Criteria criteria = Criteria.where("uid").is(uid).and("honor_level").lte(honorLevel);
        Query query = new Query(criteria);
        query.with(Sort.by(Sort.Order.desc("honor_level")));
        List<BeautifulRidChangeLog> list = mongoTemp.find(query, BeautifulRidChangeLog.class);
        if (list == null) {
            return new ArrayList<>();
        }
        return list;
    }

}
