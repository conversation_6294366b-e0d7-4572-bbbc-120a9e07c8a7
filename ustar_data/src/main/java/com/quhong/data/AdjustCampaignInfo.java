package com.quhong.data;

/**
 * Adjust广告信息对象
 * 用于接收用户注册时的广告来源信息
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-30
 */
public class AdjustCampaignInfo {
    
    /**
     * 追踪令牌
     */
    private String trackerToken;
    
    /**
     * 网络名称
     */
    private String network;
    
    /**
     * 广告系列ID
     */
    private String campaign;

    public AdjustCampaignInfo() {
    }

    public AdjustCampaignInfo(String trackerToken, String network, String campaign) {
        this.trackerToken = trackerToken;
        this.network = network;
        this.campaign = campaign;
    }

    public String getTrackerToken() {
        return trackerToken;
    }

    public void setTrackerToken(String trackerToken) {
        this.trackerToken = trackerToken;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public String getCampaign() {
        return campaign;
    }

    public void setCampaign(String campaign) {
        this.campaign = campaign;
    }

    @Override
    public String toString() {
        return "AdjustCampaignInfo{" +
                "trackerToken='" + trackerToken + '\'' +
                ", network='" + network + '\'' +
                ", campaign='" + campaign + '\'' +
                '}';
    }
}
