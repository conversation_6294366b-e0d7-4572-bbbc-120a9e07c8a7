package com.quhong.data;

import com.quhong.msg.obj.PKExtraInfoObject;
import com.quhong.msg.obj.PKInfoObject;
import com.quhong.msg.obj.PKUserInfoObject;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/16
 */
public class PkGameInfo {

    private String pid;

    private String room_id;

    private Integer total_time;

    private Integer status;

    private String creator_uid;

    private PKInfoObject red_info;

    private int red_gift_num;

    private PKInfoObject blue_info;

    private int blue_gift_num;

    private String gid;

    private String gicon;

    private Integer gift_price;

    private List<String> invite_uid;

    /**
     * pk类型 0代表全服 1代表邀请好友
     */
    private int pk_type;

    private int left_time;

    private PKUserInfoObject winner_info;

    private PKExtraInfoObject extra_msg;

    private int gift_receiver;

    private int win_diamond;

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public Integer getTotal_time() {
        return total_time;
    }

    public void setTotal_time(Integer total_time) {
        this.total_time = total_time;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCreator_uid() {
        return creator_uid;
    }

    public void setCreator_uid(String creator_uid) {
        this.creator_uid = creator_uid;
    }

    public PKInfoObject getRed_info() {
        return red_info;
    }

    public void setRed_info(PKInfoObject red_info) {
        this.red_info = red_info;
    }

    public PKInfoObject getBlue_info() {
        return blue_info;
    }

    public void setBlue_info(PKInfoObject blue_info) {
        this.blue_info = blue_info;
    }

    public String getGicon() {
        return gicon;
    }

    public void setGicon(String gicon) {
        this.gicon = gicon;
    }

    public Integer getGift_price() {
        return gift_price;
    }

    public void setGift_price(Integer gift_price) {
        this.gift_price = gift_price;
    }

    public String getRoom_id() {
        return room_id;
    }

    public void setRoom_id(String room_id) {
        this.room_id = room_id;
    }

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }

    public List<String> getInvite_uid() {
        return invite_uid;
    }

    public void setInvite_uid(List<String> invite_uid) {
        this.invite_uid = invite_uid;
    }

    public int getPk_type() {
        return pk_type;
    }

    public void setPk_type(int pk_type) {
        this.pk_type = pk_type;
    }

    public int getLeft_time() {
        return left_time;
    }

    public void setLeft_time(int left_time) {
        this.left_time = left_time;
    }

    public PKUserInfoObject getWinner_info() {
        return winner_info;
    }

    public void setWinner_info(PKUserInfoObject winner_info) {
        this.winner_info = winner_info;
    }

    public int getRed_gift_num() {
        return red_gift_num;
    }

    public void setRed_gift_num(int red_gift_num) {
        this.red_gift_num = red_gift_num;
    }

    public int getBlue_gift_num() {
        return blue_gift_num;
    }

    public void setBlue_gift_num(int blue_gift_num) {
        this.blue_gift_num = blue_gift_num;
    }

    public int getGift_receiver() {
        return gift_receiver;
    }

    public void setGift_receiver(int gift_receiver) {
        this.gift_receiver = gift_receiver;
    }

    public int getWin_diamond() {
        return win_diamond;
    }

    public void setWin_diamond(int win_diamond) {
        this.win_diamond = win_diamond;
    }

    public PKExtraInfoObject getExtra_msg() {
        return extra_msg;
    }

    public void setExtra_msg(PKExtraInfoObject extra_msg) {
        this.extra_msg = extra_msg;
    }
}
