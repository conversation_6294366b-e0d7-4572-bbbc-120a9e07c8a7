package com.quhong.data.component;

import com.quhong.mongo.data.ResourceKeyConfigData;

import java.util.List;

public class ComponentCommonConfig {
    // 共用字段
    private String componentMetaId;                       // 配置metaId
    private String componentId;                           // 组件id
    private String msgKey;                                // 消息key
    private int componentType;                            // 组件类型  0: 榜单  1: 任务  2: 抽奖
    private int countDimen;                               // 统计维度 0: 个人  1: 房间
    private String componentKey;                          // 组件key: 榜单、任务或抽奖通用字段key
    private int scaleSwitch;                              // 比例设置
    private int scaleData;                                // 比例
    private String titleEn;                               // 榜单、抽奖、任务标题
    private String titleAr;                               // 榜单、抽奖、任务标题
    private String subTitleEn;                            // 榜单、抽奖、任务子标题
    private String subTitleAr;                            // 榜单、抽奖、任务子标题
    private int period;                                   // 榜单周期/奖池更新周期: 0:日计算  1:周结算 2:按活动时间结算
    private int timeSwitch;                               // 是否单独设置统计时间
    private int startTime;                                // 开始统计时间
    private int endTime;                                  // 结束统计时间
    private int countryFilter;                            // 是否国家过滤
    private String country;                               // 国家名
    private int genderFilter;                             // 性别过滤
    private Integer gender;                               // 0全体用户 1男性用户 2女性用户
    private int deviceSet;                                // 是否设备去重  0:否  1:是
    private int gameFilter;                               // 是否指定游戏
    private List<String> gameItemList;                    // 游戏key列表
    private int giftFilter;                               // 是否指定礼物
    private List<Integer> giftIdList;                     // 统计活动礼物id
    private int roomEventFilter;                          // 是否有房间活动才统计
    private String resourceKey;                           // 抽奖或任务资源key
    private ResourceKeyConfigData resourceKeyConfigData;  // 抽奖或任务奖励资源
    private List<ComponentRankReward> rankRewardList;     // 抽奖或榜单奖励配置


    // 抽奖组件单独设置
    private int unitPrice;                            // 抽奖单价
    private int historyStatus;                        // 是否记录抽奖历史
    private int drawRankType;                         // 是否统计抽奖榜单 0:否 1:抽奖活动周期榜 2:抽奖日榜


    // 任务独立设置字段
    private int status;                               // 任务状态 0: 未完成 1: 已完成可领取 2: 已领取
    private int currentProcess;                       // 当前进度
    private int totalProcess;                         // 完成值
    private String taskIcon;                          // 任务图标
    private int nodeIndex;                            // 节点任务索引


    // 榜单特殊设置字段
    private int calculateMethod;                       // 榜单计算方式 0:无 1:钻石 2:数量
    private int top3ChangePush;                        // 排行榜前三名变化是否推送：0:否  1:是
    private int endResultPush;                         // 活动结束推送, 最多推2个榜单
    private List<ComponentLevelReward> levelRewardList;    // 等级奖励配置

    public String getComponentMetaId() {
        return componentMetaId;
    }

    public void setComponentMetaId(String componentMetaId) {
        this.componentMetaId = componentMetaId;
    }

    public String getComponentId() {
        return componentId;
    }

    public void setComponentId(String componentId) {
        this.componentId = componentId;
    }

    public String getMsgKey() {
        return msgKey;
    }

    public void setMsgKey(String msgKey) {
        this.msgKey = msgKey;
    }

    public int getComponentType() {
        return componentType;
    }

    public void setComponentType(int componentType) {
        this.componentType = componentType;
    }

    public int getCountDimen() {
        return countDimen;
    }

    public void setCountDimen(int countDimen) {
        this.countDimen = countDimen;
    }

    public String getComponentKey() {
        return componentKey;
    }

    public void setComponentKey(String componentKey) {
        this.componentKey = componentKey;
    }

    public int getScaleSwitch() {
        return scaleSwitch;
    }

    public void setScaleSwitch(int scaleSwitch) {
        this.scaleSwitch = scaleSwitch;
    }

    public int getScaleData() {
        return scaleData;
    }

    public void setScaleData(int scaleData) {
        this.scaleData = scaleData;
    }

    public String getTitleEn() {
        return titleEn;
    }

    public void setTitleEn(String titleEn) {
        this.titleEn = titleEn;
    }

    public String getTitleAr() {
        return titleAr;
    }

    public void setTitleAr(String titleAr) {
        this.titleAr = titleAr;
    }

    public String getSubTitleEn() {
        return subTitleEn;
    }

    public void setSubTitleEn(String subTitleEn) {
        this.subTitleEn = subTitleEn;
    }

    public String getSubTitleAr() {
        return subTitleAr;
    }

    public void setSubTitleAr(String subTitleAr) {
        this.subTitleAr = subTitleAr;
    }

    public int getPeriod() {
        return period;
    }

    public void setPeriod(int period) {
        this.period = period;
    }

    public int getTimeSwitch() {
        return timeSwitch;
    }

    public void setTimeSwitch(int timeSwitch) {
        this.timeSwitch = timeSwitch;
    }

    public int getStartTime() {
        return startTime;
    }

    public void setStartTime(int startTime) {
        this.startTime = startTime;
    }

    public int getEndTime() {
        return endTime;
    }

    public void setEndTime(int endTime) {
        this.endTime = endTime;
    }

    public int getCountryFilter() {
        return countryFilter;
    }

    public void setCountryFilter(int countryFilter) {
        this.countryFilter = countryFilter;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public int getGenderFilter() {
        return genderFilter;
    }

    public void setGenderFilter(int genderFilter) {
        this.genderFilter = genderFilter;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public int getDeviceSet() {
        return deviceSet;
    }

    public void setDeviceSet(int deviceSet) {
        this.deviceSet = deviceSet;
    }

    public int getGameFilter() {
        return gameFilter;
    }

    public void setGameFilter(int gameFilter) {
        this.gameFilter = gameFilter;
    }

    public List<String> getGameItemList() {
        return gameItemList;
    }

    public void setGameItemList(List<String> gameItemList) {
        this.gameItemList = gameItemList;
    }

    public int getGiftFilter() {
        return giftFilter;
    }

    public void setGiftFilter(int giftFilter) {
        this.giftFilter = giftFilter;
    }

    public List<Integer> getGiftIdList() {
        return giftIdList;
    }

    public void setGiftIdList(List<Integer> giftIdList) {
        this.giftIdList = giftIdList;
    }

    public int getRoomEventFilter() {
        return roomEventFilter;
    }

    public void setRoomEventFilter(int roomEventFilter) {
        this.roomEventFilter = roomEventFilter;
    }

    public String getResourceKey() {
        return resourceKey;
    }

    public void setResourceKey(String resourceKey) {
        this.resourceKey = resourceKey;
    }

    public ResourceKeyConfigData getResourceKeyConfigData() {
        return resourceKeyConfigData;
    }

    public void setResourceKeyConfigData(ResourceKeyConfigData resourceKeyConfigData) {
        this.resourceKeyConfigData = resourceKeyConfigData;
    }

    public List<ComponentRankReward> getRankRewardList() {
        return rankRewardList;
    }

    public void setRankRewardList(List<ComponentRankReward> rankRewardList) {
        this.rankRewardList = rankRewardList;
    }

    public int getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(int unitPrice) {
        this.unitPrice = unitPrice;
    }

    public int getHistoryStatus() {
        return historyStatus;
    }

    public void setHistoryStatus(int historyStatus) {
        this.historyStatus = historyStatus;
    }

    public int getDrawRankType() {
        return drawRankType;
    }

    public void setDrawRankType(int drawRankType) {
        this.drawRankType = drawRankType;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getCurrentProcess() {
        return currentProcess;
    }

    public void setCurrentProcess(int currentProcess) {
        this.currentProcess = currentProcess;
    }

    public int getTotalProcess() {
        return totalProcess;
    }

    public void setTotalProcess(int totalProcess) {
        this.totalProcess = totalProcess;
    }

    public String getTaskIcon() {
        return taskIcon;
    }

    public void setTaskIcon(String taskIcon) {
        this.taskIcon = taskIcon;
    }

    public int getNodeIndex() {
        return nodeIndex;
    }

    public void setNodeIndex(int nodeIndex) {
        this.nodeIndex = nodeIndex;
    }

    public int getCalculateMethod() {
        return calculateMethod;
    }

    public void setCalculateMethod(int calculateMethod) {
        this.calculateMethod = calculateMethod;
    }

    public int getTop3ChangePush() {
        return top3ChangePush;
    }

    public void setTop3ChangePush(int top3ChangePush) {
        this.top3ChangePush = top3ChangePush;
    }

    public int getEndResultPush() {
        return endResultPush;
    }

    public void setEndResultPush(int endResultPush) {
        this.endResultPush = endResultPush;
    }

    public List<ComponentLevelReward> getLevelRewardList() {
        return levelRewardList;
    }

    public void setLevelRewardList(List<ComponentLevelReward> levelRewardList) {
        this.levelRewardList = levelRewardList;
    }
}
