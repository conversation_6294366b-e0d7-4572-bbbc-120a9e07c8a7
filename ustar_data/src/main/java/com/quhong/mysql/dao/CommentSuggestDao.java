package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.mysql.data.CommentSuggestData;
import com.quhong.mysql.mapper.ustar.CommentSuggestMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

@Component
@Lazy
public class CommentSuggestDao {

    private static final Logger logger = LoggerFactory.getLogger(CommentSuggestDao.class);
    public static final String TABLE_NAME = "t_comment_suggest";

    @Resource
    private CommentSuggestMapper commentSuggestMapper;

    @Cacheable(value = "getCommentSuggestListCache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<CommentSuggestData> getCommentSuggestListCache() {
        QueryWrapper<CommentSuggestData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        queryWrapper.last("limit 7");
        return commentSuggestMapper.selectList(queryWrapper);
    }


    public CommentSuggestData selectOne(int id) {
        return commentSuggestMapper.selectById(id);
    }

    public int insertOne(CommentSuggestData data) {
        return commentSuggestMapper.insert(data);
    }

    public int updateOne(CommentSuggestData data) {
        return commentSuggestMapper.updateById(data);
    }


    // 运营系统
    public IPage<CommentSuggestData> selectPageList(String search, int status, int page, int pageSize) {
        QueryWrapper<CommentSuggestData> queryWrapper = new QueryWrapper<>();
        IPage<CommentSuggestData> dataPage = new Page<>(page == 0 ? 1 : page, pageSize == 0 ? 50 : pageSize);
        if(!StringUtils.isEmpty(search)){
            queryWrapper.like("comment_en", search);
        }

        if (status != -1){
            queryWrapper.eq("status", status);
        }
        queryWrapper.orderByDesc("ctime");
        return commentSuggestMapper.selectPage(dataPage, queryWrapper);
    }

}
