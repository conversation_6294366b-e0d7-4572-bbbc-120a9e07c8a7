package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.mysql.data.MomentBackgroundData;
import com.quhong.mysql.mapper.ustar.MomentBackgroundMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

@Component
@Lazy
public class MomentBackgroundDao {

    private static final Logger logger = LoggerFactory.getLogger(MomentBackgroundDao.class);
    @Resource
    private MomentBackgroundMapper momentBackgroundMapper;


    public IPage<MomentBackgroundData> selectListPage(String search, Integer status, Integer backgroundType,
                                                      Integer costType, int page, int pageSize) {
        QueryWrapper<MomentBackgroundData> queryWrapper = new QueryWrapper<>();
        if (!StringUtils.isEmpty(search)) {
            queryWrapper.like("id", search);
        }

        if (status != -1) {
            queryWrapper.eq("status", status);
        }

        if (backgroundType != -1) {
            queryWrapper.eq("background_type", backgroundType);
        }

        if (costType != -1) {
            queryWrapper.eq("cost_type", costType);
        }

        queryWrapper.orderByAsc("sort_num").orderByDesc("ctime");
        IPage<MomentBackgroundData> recordPage = new Page<>(page == 0 ? 1 : page, pageSize);
        recordPage = momentBackgroundMapper.selectPage(recordPage, queryWrapper);
        return recordPage;
    }

    public List<MomentBackgroundData> getAllUsableBackground() {
        QueryWrapper<MomentBackgroundData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        queryWrapper.orderByAsc("sort_num");
        return momentBackgroundMapper.selectList(queryWrapper);
    }

    public int insertOne(MomentBackgroundData data) {
        return momentBackgroundMapper.insert(data);
    }

    public int updateOne(MomentBackgroundData data) {
        return momentBackgroundMapper.updateById(data);
    }


    public MomentBackgroundData selectById(Integer id) {
        return momentBackgroundMapper.selectById(id);
    }
}
