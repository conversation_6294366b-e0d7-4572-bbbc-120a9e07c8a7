package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.data.TnDeviceAccountData;
import com.quhong.mysql.mapper.ustar.TnDeviceAccountMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Lazy
@Component
public class TnDeviceAccountDao extends ServiceImpl<TnDeviceAccountMapper, TnDeviceAccountData> {
    private static final Logger logger = LoggerFactory.getLogger(TnDeviceAccountDao.class);

    public void addOrUpdate(TnDeviceAccountData data) {
        try {
            QueryWrapper<TnDeviceAccountData> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("tn_id", data.getTnId());
            saveOrUpdate(data, queryWrapper);
        } catch (Exception e) {
            logger.error("addOrUpdate msg={}", e.getMessage(), e);
        }
    }

    public void updateRookieSign(String tnId) {
        try {
            LambdaUpdateWrapper<TnDeviceAccountData> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(TnDeviceAccountData::getTnId, tnId)
                    .set(TnDeviceAccountData::getIsRookieSign, 1)
                    .set(TnDeviceAccountData::getMtime, DateHelper.getNowSeconds());
            update(lambdaUpdateWrapper);
        } catch (Exception e) {
            logger.error("updateRookieSign tnId {} error {}", tnId, e.getMessage());
        }
    }

    public TnDeviceAccountData findRookieSignOne(String tnId, boolean status) {
        try {
            QueryWrapper<TnDeviceAccountData> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("tn_id", tnId);
            if (status) {
                queryWrapper.eq("is_rookie_sign", 1);
            }
            return getOne(queryWrapper);
        } catch (Exception e) {
            logger.error("isRookieSign tnId={} msg={}", tnId, e.getMessage(), e);
            return null;
        }
    }

    // 开宝箱设备检测
    public TnDeviceAccountData findTreasureDrawOne(String tnId) {
        try {
            QueryWrapper<TnDeviceAccountData> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("tn_id", tnId);
            queryWrapper.eq("is_treasure_draw", 1);
            return getOne(queryWrapper);
        } catch (Exception e) {
            logger.error("findTreasureDrawOne tnId={} msg={}", tnId, e.getMessage(), e);
            return null;
        }
    }
}
