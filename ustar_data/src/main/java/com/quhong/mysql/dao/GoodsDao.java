package com.quhong.mysql.dao;

import com.alibaba.fastjson.JSON;
import com.quhong.cache.CacheMap;
import com.quhong.cache.CacheObject;
import com.quhong.mysql.data.GoodsData;
import com.quhong.mysql.data.GoodsShowInfoData;
import com.quhong.mysql.mapper.ustar.GoodsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/9/15
 */
@Component
@Lazy
public class GoodsDao {
    @Autowired
    private GoodsMapper goodsMapper;

    private CacheObject<Map<String, GoodsData>> cacheObject = new CacheObject<>(5 * 60 * 1000);

    private static final long CACHE_TIME_MILLIS = 3 * 60 * 1000L;
    private final CacheMap<String, List<GoodsData>> cacheMap;

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    private String getConfigKey() {
        return "GoodsCacheKey";
    }


    private Map<String, Double> productToDollarsMap = new HashMap<>();

    public GoodsDao(){
        productToDollarsMap.put("product_coins_diamonds1", 0.99);
        productToDollarsMap.put("product_coins_diamonds2", 4.99);
        productToDollarsMap.put("product_coins_diamonds3", 19.99);
        productToDollarsMap.put("product_coins_diamonds4", 49.99);
        productToDollarsMap.put("product_coins_diamonds5", 99.99);
        productToDollarsMap.put("product_coins_diamonds6", 299.99);
        productToDollarsMap.put("product_coins_diamonds7", 1000d);
        productToDollarsMap.put("product_coins_diamonds8", 500d);
        productToDollarsMap.put("product_coins_diamonds9", 199.99);
        productToDollarsMap.put("product_coins_diamonds10", 199.99);

        this.cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }

    /**
     * 根据productid获取dollar
     * @param productId
     * @return
     */
    public Double getPriceDollars(String productId){
        return productToDollarsMap.get(productId);
    }

    /**
     * 获取商品的map， productId -> data
     * @return
     */
    public Map<String, GoodsData> getGoodsMap(){
        Map<String, GoodsData> map = cacheObject.getData();
        if(map != null){
            return map;
        }
        Map<String, GoodsData> goodsMap = new HashMap<>();
        List<GoodsData> list = goodsMapper.getGoodsList();
        list.forEach(data -> {
            if(StringUtils.hasLength(data.getShowInfo())){
                data.setShowInfoData(JSON.parseObject(data.getShowInfo(), GoodsShowInfoData.class));
            }
            goodsMap.put(data.getProductId(), data);
        });
        cacheObject.cacheData(goodsMap);
        return goodsMap;
    }

    /**
     * 获取充值种类列表
     * @return 充值种类列表
     */
    public List<GoodsData> getGoodsList() {
        return goodsMapper.getGoodsList();
    }


    /**
     * 获取充值种类列表
     * @return 充值种类列表
     */
    public List<GoodsData> getProductList() {
        List<GoodsData> goodsDataList = cacheMap.getData(getConfigKey());
        if(goodsDataList != null){
            return goodsDataList;
        }

        goodsDataList = goodsMapper.getProductList();
        if (goodsDataList != null) {
            cacheMap.cacheData(getConfigKey(), goodsDataList);
        }
        return goodsDataList;
    }
}
