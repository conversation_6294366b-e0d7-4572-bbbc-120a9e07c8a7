package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.quhong.mysql.data.UserRechargeRecordData;
import com.quhong.mysql.mapper.ustar_log.UserRechargeRecordMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/15
 */
@Component
public class UserRechargeRecordDao {

    private static final Logger logger = LoggerFactory.getLogger(UserRechargeRecordDao.class);

    private static final int FIRST_CHARGE_DIS_TYPE = 1; //1 用户排重 2 设备排重

    @Resource
    private UserRechargeRecordMapper userRechargeRecordMapper;

    public void insert(UserRechargeRecordData data) {
        userRechargeRecordMapper.insert(data);
    }

    public void update(UserRechargeRecordData data) {
        userRechargeRecordMapper.updateById(data);
    }

    public int selectCount(String uid) {
        QueryWrapper<UserRechargeRecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        return userRechargeRecordMapper.selectCount(queryWrapper);
    }

    public UserRechargeRecordData getFirstChargeData(String uid) {
        QueryWrapper<UserRechargeRecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.eq("first_charge", 1);
        queryWrapper.last("limit 1");
        return userRechargeRecordMapper.selectOne(queryWrapper);
    }

    public UserRechargeRecordData getFirstChargeData(String uid, String tnId) {
        QueryWrapper<UserRechargeRecordData> queryWrapper = new QueryWrapper<>();
        if (FIRST_CHARGE_DIS_TYPE == 1) {
            queryWrapper.eq("uid", uid);
        } else {
            if (StringUtils.isEmpty(tnId)) {
                logger.info("getFirstChargeData tnId is empty uid:{} tnId:{}", uid, tnId);
                return new UserRechargeRecordData();
            }
            queryWrapper.eq("tn_id", tnId);
        }
        queryWrapper.eq("first_charge", 1);
        queryWrapper.last("limit 1");
        return userRechargeRecordMapper.selectOne(queryWrapper);
    }

    public UserRechargeRecordData selectOne(String uid, String orderId) {
        QueryWrapper<UserRechargeRecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.eq("order_id", orderId);
        return userRechargeRecordMapper.selectOne(queryWrapper);
    }

    public int selectNoRefundCount(String uid) {
        QueryWrapper<UserRechargeRecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.eq("has_refunded", 0);
        return userRechargeRecordMapper.selectCount(queryWrapper);
    }

    public int selectHasRecycledRewardCount(String uid) {
        QueryWrapper<UserRechargeRecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.eq("has_refunded", 2);
        return userRechargeRecordMapper.selectCount(queryWrapper);
    }

    public List<UserRechargeRecordData> getFirstChargeDataList(int startTime, int endTime) {
        QueryWrapper<UserRechargeRecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.gt("recharge_time", startTime);
        queryWrapper.le("recharge_time", endTime);
        queryWrapper.eq("first_charge", 1);
        return userRechargeRecordMapper.selectList(queryWrapper);
    }
}
