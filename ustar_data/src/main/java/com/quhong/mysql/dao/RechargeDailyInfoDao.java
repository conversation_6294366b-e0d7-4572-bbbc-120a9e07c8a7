package com.quhong.mysql.dao;

import com.quhong.cache.CacheMap;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.UserRechargeAmountData;
import com.quhong.mysql.data.RechargeDailyInfoData;
import com.quhong.mysql.data.UserTotalRechargeData;
import com.quhong.mysql.mapper.ustar_log.RechargeDailyInfoMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Set;

@Component
@Lazy
public class RechargeDailyInfoDao {

    private static final Logger logger = LoggerFactory.getLogger(RechargeDailyInfoDao.class);

    private static final long CACHE_TIME_MILLIS = 3 * 60 * 1000L;
    private final CacheMap<String, Integer> cacheMap;

    public RechargeDailyInfoDao() {
        cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    @Resource
    private RechargeDailyInfoMapper rechargeDailyInfoMapper;

    public List<String> selectRechargeUserUid(UserRechargeAmountData userRechargeAmountData) {
        return rechargeDailyInfoMapper.selectRechargeUserUid(userRechargeAmountData);
    }

    public int getRechargeTotalNum(String uid) {
        String key = getRechargeTotalNumKey(uid);
        Integer num = cacheMap.getData(key);
        if (num != null) {
            return num;
        }
        num = rechargeDailyInfoMapper.getRechargeTotalNum(uid);
        cacheMap.cacheData(key, num);
        return num;
    }

    public int getHistoryRechargeTotalNum(String uid, int endTime) {
        return rechargeDailyInfoMapper.getHistoryRechargeTotalNum(uid, endTime);
    }

    private String getRechargeTotalNumKey(String uid) {
        return "rechargeTotalNum_" + uid;
    }

    public UserTotalRechargeData getUserTotalRecharge(String uid) {
        return rechargeDailyInfoMapper.getUserTotalRecharge(uid);
    }

    public List<RechargeDailyInfoData> getUserRechargeInfoList(String uid, int startTime, int endTime) {
        return rechargeDailyInfoMapper.getUserRechargeInfoList(uid, startTime, endTime);
    }


    public Set<String> selectUserRechargeByDay(Integer startTime, Integer endTime, BigDecimal rechargeAmount) {
        Set<String> retSet = rechargeDailyInfoMapper.selectUserRechargeByDay(startTime, endTime, rechargeAmount);
        return retSet == null ? Collections.emptySet() : retSet;
    }

    public int insert(RechargeDailyInfoData data) {
        return rechargeDailyInfoMapper.insert(data);
    }


    @Cacheable(value = "getUserLastRechargeCache",
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')",
            cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public int getUserLastRechargeCache(String uid, int days) {
        int endTime = DateHelper.getNowSeconds();
        int startTime = endTime - days * 86400;
        return (int) selectUserRechargeAmount(uid, startTime);
    }

    /**
     * 查最近n天充值金额
     */
    public double selectUserRechargeAmount(String uid, int rechargeTime) {
        try {
            BigDecimal totalRecharge = rechargeDailyInfoMapper.selectUserRechargeAmount(uid, rechargeTime);
            return totalRecharge != null ? totalRecharge.doubleValue() : 0;
        } catch (Exception e) {
            logger.error("selectUserRechargeAmount error uid:{}, time:{}, e:{}", uid, rechargeTime, e.getMessage(), e);
        }
        return 0;
    }

    public int getUserTotalRechargeBean(String uid, int startTime, int endTime) {
        return rechargeDailyInfoMapper.getUserTotalRechargeBean(uid, startTime, endTime);
    }

    public int getUserTotalRechargeBeanByUidList(List<String> aidList, int startTime, int endTime) {
        return rechargeDailyInfoMapper.getUserTotalRechargeBeanByUidList(aidList, startTime, endTime);
    }

    public List<RechargeDailyInfoData> getAllRechargeInfoList(int startTime) {
        return rechargeDailyInfoMapper.getAllRechargeInfoList(startTime);
    }
}
