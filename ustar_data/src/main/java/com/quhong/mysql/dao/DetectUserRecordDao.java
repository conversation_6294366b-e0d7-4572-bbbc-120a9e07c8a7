package com.quhong.mysql.dao;

import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.data.DetectUserRecordData;
import com.quhong.mysql.mapper.ustar_log.DetectUserRecordMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Lazy
@Component
public class DetectUserRecordDao extends MonthShardingDao<DetectUserRecordMapper> {
    private static final Logger logger = LoggerFactory.getLogger(DetectUserRecordDao.class);
    public static final Integer TYPE_TEXT = 0;
    public static final Integer TYPE_IMAGE = 1;

    @Resource
    private DetectUserRecordMapper  detectUserRecordMapper;

    public DetectUserRecordDao() {
        super("t_detect_user_record");
    }

    public void insert(DetectUserRecordData detectUserRecordData) {
        String suffix = DateHelper.ARABIAN.getTableSuffix(DateHelper.formatDate(detectUserRecordData.getCtime()));
        createTable(suffix);
        detectUserRecordMapper.insert(suffix, detectUserRecordData);
    }

    public void update(DetectUserRecordData recordData) {
        String suffix = DateHelper.ARABIAN.getTableSuffix(DateHelper.formatDate(recordData.getCtime()));
        createTable(suffix);
        detectUserRecordMapper.update(suffix, recordData);
    }

    public List<DetectUserRecordData> getRecords(String uid, Integer detectType, int page, int size) {
        List<String> suffixList = getTableSuffixList(-2);
        suffixList = suffixList.stream().filter(this::checkExist).collect(Collectors.toList());
        if (suffixList.isEmpty()) {
            return Collections.emptyList();
        }

        if(StringUtils.isEmpty(uid)){
            uid = null;
        }

        int start = (page - 1) * size;
        logger.info("DetectUserRecordDao getRecords uid:{}, start={}, size={}, suffixList={}", uid, start, size, suffixList);
        return detectUserRecordMapper.getRecords(uid, detectType, start, size, suffixList);
    }

    public List<DetectUserRecordData> getReportRecords(String uid, Integer detectType) {
        List<String> suffixList = getTableSuffixList(-2);
        suffixList = suffixList.stream().filter(this::checkExist).collect(Collectors.toList());
        if (suffixList.isEmpty()) {
            return Collections.emptyList();
        }

        if(StringUtils.isEmpty(uid)){
            uid = null;
        }
        logger.info("DetectUserRecordDao getRecords uid:{}, suffixList={}", uid, suffixList);
        return detectUserRecordMapper.getReportRecords(uid, detectType, suffixList);
    }

    public int getTotalRecords(String uid, Integer detectType) {
        List<String> suffixList = getTableSuffixList(-2);
        suffixList = suffixList.stream().filter(this::checkExist).collect(Collectors.toList());
        if (suffixList.isEmpty()) {
            return 0;
        }

        if(StringUtils.isEmpty(uid)){
            uid = null;
        }

        logger.info("DetectUserRecordDao getTotalRecords uid:{}, suffixList={}", uid, suffixList);
        return detectUserRecordMapper.getTotalRecord(uid, detectType, suffixList);
    }

    public DetectUserRecordData selectById(int id, int ctime) {
        String suffix = DateHelper.ARABIAN.getTableSuffix(DateHelper.formatDate(ctime));
        if (!checkExist(suffix)) {
            return null;
        }
        return detectUserRecordMapper.selectById(suffix, id, ctime);
    }
}
