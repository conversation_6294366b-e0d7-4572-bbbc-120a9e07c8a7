package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.data.GreetUserData;
import com.quhong.mysql.mapper.ustar.GreetUserMapper;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 打招呼推荐用户数据
 */
@Lazy
@Component
public class GreetUserDao {
    
    private static final Logger logger = LoggerFactory.getLogger(GreetUserDao.class);
    
    @Resource
    private GreetUserMapper greetUserMapper;

    /**
     * 根据ID查询单条记录
     */
    public GreetUserData selectOne(int id) {
        return greetUserMapper.selectById(id);
    }

    /**
     * 根据uid查询单条记录
     */
    public GreetUserData selectByUid(String uid) {
        QueryWrapper<GreetUserData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.last("limit 1");
        return greetUserMapper.selectOne(queryWrapper);
    }

    /**
     * 插入新记录
     */
    public int insert(GreetUserData data) {
        data.setCtime(DateHelper.getNowSeconds());
        data.setMtime(data.getCtime());
        return greetUserMapper.insert(data);
    }

    /**
     * 更新记录
     */
    public int update(GreetUserData data) {
        try {
            data.setMtime(DateHelper.getNowSeconds());
            return greetUserMapper.updateById(data);
        }catch (Exception e){
            logger.error("update greet user data error. uid={} {}", data.getUid(), e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 删除记录
     */
    public void delete(int recordId) {
        try {
            greetUserMapper.deleteById(recordId);
        }catch (Exception e){
            logger.error("delete error. uid={} {}", recordId, e.getMessage(), e);
        }
    }

    /**
     * 查询推荐列表
     */
    public List<GreetUserData> selectRecommendList(int region, String countryCode, byte[] interestBit,
                                                   double registerWeight, double regionWeight, double onlineWeight,  double interestWeight,
                                                   double replyWeight, double profileWeight,
                                                   int start, int size) {
        try {
            int currentTime = DateHelper.getNowSeconds();
            return greetUserMapper.getGreetUserByPage(currentTime, region, countryCode, interestBit, registerWeight, regionWeight, onlineWeight, interestWeight, replyWeight, profileWeight, start, size);
        }catch (Exception e){
            logger.error("selectRecommendList error. {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 根据countryCode查询推荐列表
     */
    public List<GreetUserData> selectRecommendListByCountryCode(int region, String countryCode, String filterCountryCode, byte[] interestBit,
                                                   double registerWeight, double regionWeight, double onlineWeight,  double interestWeight,
                                                   double replyWeight, double profileWeight,
                                                   int start, int size) {
        try {
            int currentTime = DateHelper.getNowSeconds();
            return greetUserMapper.getGreetUserByCountryCodePage(currentTime, region, countryCode, filterCountryCode, interestBit, registerWeight, regionWeight, onlineWeight, interestWeight, replyWeight, profileWeight, start, size);
        }catch (Exception e){
            logger.error("selectRecommendListByCountryCode error. {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }



    /**
     * 删除超过指定时间的旧用户
     * @param cutoffTime 截止时间戳，删除创建时间早于此时间的帖子
     * @return 删除的记录数
     */
    public int deleteOldGreetUser(int cutoffTime) {
        QueryWrapper<GreetUserData> queryWrapper = new QueryWrapper<>();
        queryWrapper.lt("mtime", cutoffTime);
        return greetUserMapper.delete(queryWrapper);
    }


}
