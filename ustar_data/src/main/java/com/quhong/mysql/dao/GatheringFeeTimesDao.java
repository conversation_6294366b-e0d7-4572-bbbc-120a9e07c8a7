package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.data.GatheringFeeTimesData;
import com.quhong.mysql.mapper.ustar.GatheringFeeTimesMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/2/13
 */
@Component
public class GatheringFeeTimesDao {

    @Resource
    private GatheringFeeTimesMapper gatheringFeeTimesMapper;

    public void insert(GatheringFeeTimesData data) {
        gatheringFeeTimesMapper.insert(data);
    }

    public GatheringFeeTimesData selectOne(String uid) {
        QueryWrapper<GatheringFeeTimesData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        return gatheringFeeTimesMapper.selectOne(queryWrapper);
    }

    public int changeFeeTimes(String uid, int changeNum) {
        return gatheringFeeTimesMapper.changeFeeTimes(uid, changeNum, DateHelper.getNowSeconds());
    }

    public void update(GatheringFeeTimesData data) {
        gatheringFeeTimesMapper.updateById(data);
    }
}
