package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.mysql.data.TruthDareTopicData;
import com.quhong.mysql.mapper.ustar.TruthDareTopicMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

@Lazy
@Component
public class TruthDareTopicDao {
    private static final Logger logger = LoggerFactory.getLogger(TruthDareTopicDao.class);

    @Resource
    private TruthDareTopicMapper truthDareTopicMapper;

    @Cacheable(value = "truthDareTopicCache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE, condition = "T(com.quhong.core.config.ServerConfig).isProduct()",
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public List<TruthDareTopicData> selectAllTruthDareTopic(){
        QueryWrapper<TruthDareTopicData> query = new QueryWrapper<>();
        return truthDareTopicMapper.selectList(query);
    }

    @Cacheable(value = "truthDareValidTopicCache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE, condition = "T(com.quhong.core.config.ServerConfig).isProduct()",
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public List<TruthDareTopicData> selectAllValidTruthDareTopic(){
        QueryWrapper<TruthDareTopicData> query = new QueryWrapper<>();
        query.eq("status", 1);
        return truthDareTopicMapper.selectList(query);
    }


    public void insert(TruthDareTopicData data) {
        truthDareTopicMapper.insert(data);
    }

    public void update(TruthDareTopicData data) {
        truthDareTopicMapper.updateById(data);
    }

    public TruthDareTopicData selectById(Integer id) {
        return truthDareTopicMapper.selectById(id);
    }

    public IPage<TruthDareTopicData> selectPageList(String search, int status, int topicType, int page, int pageSize) {
        QueryWrapper<TruthDareTopicData> queryWrapper = new QueryWrapper<>();
        IPage<TruthDareTopicData> dataPage = new Page<>(page == 0 ? 1 : page, pageSize == 0 ? 50 : pageSize);
        if (status != -1) {
            queryWrapper.eq("status", status);
        }
        if (topicType != -1){
            queryWrapper.eq("topic_type", topicType);
        }
        if (!StringUtils.isEmpty(search)) {
            queryWrapper.like("name_en", search);
        }
        queryWrapper.orderByDesc("ctime");
        return truthDareTopicMapper.selectPage(dataPage, queryWrapper);
    }

}
