package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.data.RoomEventData;
import com.quhong.mysql.mapper.ustar_log.RoomEventMapper;
import com.quhong.utils.CollectionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/12/6
 */
@Component
public class RoomEventDao {

    private static final Logger logger = LoggerFactory.getLogger(RoomEventDao.class);
    public static final Integer EVENT_SUPPORT_SWITCH = 1;
    public static final String EVENT_SUPPORT_LINK = ServerConfig.isProduct() ? "https://static.youstar.live/weekly_party_support/" : "https://test2.qmovies.tv/weekly_party_support/";

    @Resource
    private RoomEventMapper roomEventMapper;

    public void insert(RoomEventData data) {
        roomEventMapper.insert(data);
    }

    public int getTimeOverlapCount(String roomId, Integer startTime, int endTime) {
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("room_id", roomId)
                .or(k -> k.lt("start_time", startTime).gt("end_time", startTime))
                .or(k -> k.lt("start_time", endTime).gt("end_time", endTime))
                .or(k -> k.ge("start_time", startTime).le("end_time", endTime))
                .or(k -> k.le("start_time", startTime).ge("end_time", endTime));
        return roomEventMapper.selectCount(queryWrapper);
    }

    public int updateOne(RoomEventData roomEventData) {
        return roomEventMapper.updateById(roomEventData);
    }


    public List<RoomEventData> getRoomEventList(int nowTime) {
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.gt("end_time", nowTime);
        queryWrapper.orderByAsc("start_time");
        return roomEventMapper.selectList(queryWrapper);
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE,
            condition = "T(com.quhong.core.config.ServerConfig).isProduct()",
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public Set<String> getInProgressRoomSet() {
        try {
            int nowSeconds = DateHelper.getNowSeconds();
            QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
            queryWrapper.ge("start_time", nowSeconds);
            queryWrapper.le("end_time", nowSeconds);
            queryWrapper.select("room_id");
            List<RoomEventData> roomEventData = roomEventMapper.selectList(queryWrapper);
            return CollectionUtil.listToPropertySet(roomEventData, RoomEventData::getRoomId);
        } catch (Exception e) {
            logger.error("get getInProgressRoomSet error. {}", e.getMessage(), e);
            return Collections.emptySet();
        }
    }

    public List<RoomEventData> getRoomEventList(int nowTime, Set<String> testUserList) {
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        if (!CollectionUtils.isEmpty(testUserList)) {
            queryWrapper.notIn("room_id", testUserList);
        }
        queryWrapper.gt("end_time", nowTime);
        queryWrapper.orderByAsc("start_time");
        return roomEventMapper.selectList(queryWrapper);
    }

    public RoomEventData selectById(Integer eventId) {
        return roomEventMapper.selectById(eventId);
    }

    public List<RoomEventData> getPastRoomEvent(String roomId, int nowTime, int page, int pageSize) {
        IPage<RoomEventData> dataPage = new Page<>(page, pageSize);
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("room_id", roomId);
        queryWrapper.lt("end_time", nowTime);
        queryWrapper.orderByDesc("start_time");
        dataPage = roomEventMapper.selectPage(dataPage, queryWrapper);
        return dataPage.getRecords();
    }

    public List<RoomEventData> getNotEndRoomEvent(String roomId, int nowTime, int page, int pageSize) {
        IPage<RoomEventData> dataPage = new Page<>(page, pageSize);
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("room_id", roomId);
        queryWrapper.gt("end_time", nowTime);
        queryWrapper.orderByAsc("start_time");
        dataPage = roomEventMapper.selectPage(dataPage, queryWrapper);
        return dataPage.getRecords();
    }

    public RoomEventData getOneNotEndRoomEvent(String roomId, int nowTime) {
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("room_id", roomId);
        queryWrapper.gt("end_time", nowTime);
        queryWrapper.orderByAsc("start_time");
        queryWrapper.last("limit 1");
        return roomEventMapper.selectOne(queryWrapper);
    }

    public RoomEventData getHasOneNotEndRoomEvent(int nowTime) {
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.gt("end_time", nowTime);
        queryWrapper.last("limit 1");
        return roomEventMapper.selectOne(queryWrapper);
    }

    public int getNotEndRoomEventNum(String roomId, int nowTime) {
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("room_id", roomId);
        queryWrapper.gt("end_time", nowTime);
        return roomEventMapper.selectCount(queryWrapper);
    }

    public void update(RoomEventData data) {
        UpdateWrapper<RoomEventData> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", data.getId());
        updateWrapper.set("sub_num", data.getSubNum());
        roomEventMapper.update(data, updateWrapper);
    }

    public List<RoomEventData> selectList(List<Integer> eventIds) {
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", eventIds);
        queryWrapper.orderByAsc("start_time");
        return roomEventMapper.selectList(queryWrapper);
    }

    public List<RoomEventData> getMyCreatedList(String uid, int nowTime, int page, int pageSize) {
        IPage<RoomEventData> dataPage = new Page<>(page, pageSize);
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("creator", uid);
        queryWrapper.gt("end_time", nowTime);
        queryWrapper.orderByAsc("start_time");
        dataPage = roomEventMapper.selectPage(dataPage, queryWrapper);
        return dataPage.getRecords();
    }

    public List<RoomEventData> getRoomNotEndEvent(String roomId, int nowTime) {
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("room_id", roomId);
        queryWrapper.gt("end_time", nowTime);
        queryWrapper.orderByDesc("start_time");
        return roomEventMapper.selectList(queryWrapper);
    }

    public List<RoomEventData> getRoomNotEndEventByAsc(String roomId, int nowTime) {
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("room_id", roomId);
        queryWrapper.gt("end_time", nowTime);
        queryWrapper.orderByAsc("start_time");
        return roomEventMapper.selectList(queryWrapper);
    }

    public RoomEventData getOngoingRoomEvent(String roomId, int nowTime) {
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("room_id", roomId);
        queryWrapper.le("start_time", nowTime);
        queryWrapper.ge("end_time", nowTime);
        queryWrapper.last("limit 1");
        return roomEventMapper.selectOne(queryWrapper);
    }

    public List<RoomEventData> getNotStartRoomEvent(int nowTime) {
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.gt("start_time", nowTime);
        return roomEventMapper.selectList(queryWrapper);
    }

    public void delete(int eventId) {
        roomEventMapper.deleteById(eventId);
    }

    public List<RoomEventData> getHasEndedEventList(int startTime, int endTime) {
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.ge("end_time", startTime);
        queryWrapper.lt("end_time", endTime);
        return roomEventMapper.selectList(queryWrapper);
    }

    public RoomEventData getOneRoomEvent(String roomId, int startTime) {
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("room_id", roomId);
        queryWrapper.gt("start_time", startTime);
        queryWrapper.orderByDesc("start_time");
        queryWrapper.last("limit 1");
        return roomEventMapper.selectOne(queryWrapper);
    }

    public List<RoomEventData> getInitRoomEvent() {
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("event_status", 0);
        return roomEventMapper.selectList(queryWrapper);
    }

    public List<RoomEventData> getPushRoomEvent(int startTime, int endTime) {
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("event_push", 0);
        queryWrapper.gt("start_time", startTime);
        queryWrapper.lt("start_time", endTime);
        return roomEventMapper.selectList(queryWrapper);
    }

    public RoomEventData getOneSupportRoomEvent(String roomId) {
        QueryWrapper<RoomEventData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("room_id", roomId);
        queryWrapper.gt("event_support", 1);
        queryWrapper.last("limit 1");
        return roomEventMapper.selectOne(queryWrapper);
    }
}
