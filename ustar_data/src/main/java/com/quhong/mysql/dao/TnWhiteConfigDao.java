package com.quhong.mysql.dao;

import com.quhong.cache.CacheMap;
import com.quhong.mysql.data.TnWhiteConfigData;
import com.quhong.mysql.mapper.ustar.TnWhiteConfigMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
@Lazy
public class TnWhiteConfigDao {
    private static final Logger logger = LoggerFactory.getLogger(TnWhiteConfigDao.class);
    private final CacheMap<String, List<String>> cacheMap;
    private static final String TN_WHITE_CONFIG_KEY = "TnWhiteConfigKey";

    @Resource
    private TnWhiteConfigMapper configMapper;

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    public TnWhiteConfigDao() {
        this.cacheMap = new CacheMap<>();
    }

    public void insertList(List<TnWhiteConfigData> list) {
        try {
            configMapper.insertList(list);
        } catch (Exception e) {
            logger.error("insertList ", e);
        }
    }

    public List<String> getAllTnId() {
        try {
            List<String> allTnList = cacheMap.getData(TN_WHITE_CONFIG_KEY);
            if (null == allTnList) {
                synchronized (this) {
                    if (!cacheMap.hasData(TN_WHITE_CONFIG_KEY)) {
                        allTnList = configMapper.getAllTnId();
                        List<String> allUidList = configMapper.getAllUid();
                        allTnList.addAll(allUidList);
                        cacheMap.cacheData(TN_WHITE_CONFIG_KEY, allTnList);
                    } else {
                        allTnList = cacheMap.getData(TN_WHITE_CONFIG_KEY);
                    }
                }
            }
            return null == allTnList ? new ArrayList<>() : allTnList;
        } catch (Exception e) {
            logger.error("getAllTnId error", e);
            return new ArrayList<>();
        }
    }

    public boolean queryItem(String tnId) {
        try {
            String item = configMapper.queryItem(tnId);
            return !StringUtils.isEmpty(item);
        } catch (Exception e) {
            logger.error("queryItem tnId={} error", tnId, e);
            return false;
        }
    }

    public List<TnWhiteConfigData> getDataByPage(int start, int size) {
        try {
            return configMapper.getDataByPage(start, size);
        } catch (Exception e) {
            logger.error("getDataByPage start={} size={} error", start, size, e);
            return new ArrayList<>();
        }
    }

    public List<TnWhiteConfigData> getDataByPageByUid(String uid, int start, int size) {
        try {
            return configMapper.getDataByPageByUid(uid, start, size);
        } catch (Exception e) {
            logger.error("getDataByPage start={} size={} error", start, size, e);
            return new ArrayList<>();
        }
    }

    public List<TnWhiteConfigData> getDataByPageByTnId(String tnID, int start, int size) {
        try {
            return configMapper.getDataByPageByTnId(tnID, start, size);
        } catch (Exception e) {
            logger.error("getDataByPage start={} size={} error", start, size, e);
            return new ArrayList<>();
        }
    }

    public int deleteItem(String tnId) {
        try {
            return configMapper.deleteItem(tnId);
        } catch (Exception e) {
            logger.error("deleteItem tnId={} error", tnId, e);
            return 0;
        }
    }
}
