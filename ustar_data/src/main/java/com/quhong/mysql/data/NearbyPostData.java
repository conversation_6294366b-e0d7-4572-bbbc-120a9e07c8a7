package com.quhong.mysql.data;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_nearby_post")
public class NearbyPostData {
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 发布者
     */
    private String uid;
    /**
     * 帖子id
     */
    private String momentId;
    /**
     * 帖子所属区域
     */
    private String region;
    /**
     * 帖子所属国家
     */
    private String countryCode;
    /**
     * 人工干预权重
     */
    private double interveneWeight;
    /**
     * 帖子曝光权重: 0:未曝光 <0: 曝光权重
     */
    private double exposureWeight;
    /**
     * 帖子总分数
     */
    private double score;
    /**
     * 帖子点赞获得分数
     */
    private double likeScore;
    /**
     * 评论帖子的人(用于去重)
     */
    private String commentUser;
    /**
     * 帖子评论获得分数
     */
    private double commentScore;
    /**
     * 展示状态 0: 不展示 1: 展示
     */
    private int status;
    /**
     * 创建时间/帖子发布时间
     */
    private int ctime;
    /**
     * 修改时间
     */
    private int mtime;
    /**
     * 时间衰减日期
     */
    private String decayDate;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getMomentId() {
        return momentId;
    }

    public void setMomentId(String momentId) {
        this.momentId = momentId;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public double getInterveneWeight() {
        return interveneWeight;
    }

    public void setInterveneWeight(double interveneWeight) {
        this.interveneWeight = interveneWeight;
    }

    public double getExposureWeight() {
        return exposureWeight;
    }

    public void setExposureWeight(double exposureWeight) {
        this.exposureWeight = exposureWeight;
    }

    public double getScore() {
        return score;
    }

    public void setScore(double score) {
        this.score = score;
    }

    public double getLikeScore() {
        return likeScore;
    }

    public void setLikeScore(double likeScore) {
        this.likeScore = likeScore;
    }

    public double getCommentScore() {
        return commentScore;
    }

    public void setCommentScore(double commentScore) {
        this.commentScore = commentScore;
    }

    public String getCommentUser() {
        return commentUser;
    }

    public void setCommentUser(String commentUser) {
        this.commentUser = commentUser;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public int getMtime() {
        return mtime;
    }

    public void setMtime(int mtime) {
        this.mtime = mtime;
    }

    public String getDecayDate() {
        return decayDate;
    }

    public void setDecayDate(String decayDate) {
        this.decayDate = decayDate;
    }
}
