package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;

/**
 * 充值优惠卷
 *
 * <AUTHOR>
 * @date 2024/3/15
 */
@TableName(value = "t_recharge_coupon")
public class RechargeCouponData {

    /**
     *  主键id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 标题
     */
    private String title;

    /**
     * 标题阿语
     */
    private String titleAr;

    /**
     * 类型： 0需要获得 1上线后所以人都有
     */
    private Integer type;

    /**
     * 有效期开始时间
     */
    private Integer startTime;

    /**
     * 有效期结束时间
     */
    private Integer endTime;

    /**
     * 有效天数
     */
    private Integer validDay;

    /**
     * 额外奖励比例
     */
    private BigDecimal extraProp;

    /**
     * 状态 0无效 1有效
     */
    private Integer status;

    /**
     * 操作者
     */
    private String operator;

    /**
     * 修改时间
     */
    private Integer mtime;

    /**
     * 创建时间
     */
    private Integer ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitleAr() {
        return titleAr;
    }

    public void setTitleAr(String titleAr) {
        this.titleAr = titleAr;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public Integer getValidDay() {
        return validDay;
    }

    public void setValidDay(Integer validDay) {
        this.validDay = validDay;
    }

    public BigDecimal getExtraProp() {
        return extraProp;
    }

    public void setExtraProp(BigDecimal extraProp) {
        this.extraProp = extraProp;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
