package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * 房间活动订阅表
 *
 * <AUTHOR>
 * @date 2022/12/7
 */
public class RoomEventSubData {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 房间活动id
     */
    private Integer eventId;

    /**
     * 订阅的用户id
     */
    private String uid;

    /**
     * 是否是机器人 0否 1是
     */
    private Integer isRobot;

    /**
     * 活动开始时间
     */
    private Integer startTime;

    /**
     * 活动结束时间
     */
    private Integer endTime;

    /**
     * 创建时间
     */
    private Integer ctime;

    public RoomEventSubData() {
    }

    public RoomEventSubData(Integer eventId, String uid, Integer isRobot, Integer startTime, Integer endTime, Integer ctime) {
        this.eventId = eventId;
        this.uid = uid;
        this.isRobot = isRobot;
        this.startTime = startTime;
        this.endTime = endTime;
        this.ctime = ctime;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getIsRobot() {
        return isRobot;
    }

    public void setIsRobot(Integer isRobot) {
        this.isRobot = isRobot;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }
}
