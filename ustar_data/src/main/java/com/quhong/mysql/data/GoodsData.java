package com.quhong.mysql.data;

import java.util.Date;

public class GoodsData {
    private Integer rid;

    /**
     * google play product id
     */

    private String productId;

    /**
     * beans
     */
    private Integer beans;

    /**
     * first reward value
     */
    private Integer reward;

    /**
     * 1 valid; 0 invalid
     */
    private Byte fstatus;

    /**
     * goods price, for query
     */
    private String price;

    /**
     * 展示顺序
     */
    private Integer forder;

    /**
     * for show
     */
    private String showInfo;

    private Date ctime;

    private Date mtime;

    private GoodsShowInfoData showInfoData;

    /**
     * @return rid
     */
    public Integer getRid() {
        return rid;
    }

    /**
     * @param rid
     */
    public void setRid(Integer rid) {
        this.rid = rid;
    }

    /**
     * 获取google play product id
     *
     * @return productId - google play product id
     */
    public String getProductId() {
        return productId;
    }

    /**
     * 设置google play product id
     *
     * @param productId google play product id
     */
    public void setProductId(String productId) {
        this.productId = productId;
    }

    /**
     * 获取beans
     *
     * @return beans - beans
     */
    public Integer getBeans() {
        return beans;
    }

    /**
     * 设置beans
     *
     * @param beans beans
     */
    public void setBeans(Integer beans) {
        this.beans = beans;
    }

    /**
     * 获取first reward value
     *
     * @return reward - first reward value
     */
    public Integer getReward() {
        return reward;
    }

    /**
     * 设置first reward value
     *
     * @param reward first reward value
     */
    public void setReward(Integer reward) {
        this.reward = reward;
    }

    /**
     * 获取1 valid; 0 invalid
     *
     * @return fstatus - 1 valid; 0 invalid
     */
    public Byte getFstatus() {
        return fstatus;
    }

    /**
     * 设置1 valid; 0 invalid
     *
     * @param fstatus 1 valid; 0 invalid
     */
    public void setFstatus(Byte fstatus) {
        this.fstatus = fstatus;
    }

    /**
     * 获取goods price, for query
     *
     * @return price - goods price, for query
     */
    public String getPrice() {
        return price;
    }

    /**
     * 设置goods price, for query
     *
     * @param price goods price, for query
     */
    public void setPrice(String price) {
        this.price = price;
    }

    /**
     * 获取展示顺序
     *
     * @return forder - 展示顺序
     */
    public Integer getForder() {
        return forder;
    }

    /**
     * 设置展示顺序
     *
     * @param forder 展示顺序
     */
    public void setForder(Integer forder) {
        this.forder = forder;
    }

    /**
     * 获取for show
     *
     * @return showInfo - for show
     */
    public String getShowInfo() {
        return showInfo;
    }

    /**
     * 设置for show
     *
     * @param showInfo for show
     */
    public void setShowInfo(String showInfo) {
        this.showInfo = showInfo;
    }

    /**
     * @return ctime
     */
    public Date getCtime() {
        return ctime;
    }

    /**
     * @param ctime
     */
    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    /**
     * @return mtime
     */
    public Date getMtime() {
        return mtime;
    }

    /**
     * @param mtime
     */
    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }

    public GoodsShowInfoData getShowInfoData() {
        return showInfoData;
    }

    public void setShowInfoData(GoodsShowInfoData showInfoData) {
        this.showInfoData = showInfoData;
    }

    @Override
    public String toString() {
        return "goods{" +
                "rid=" + rid +
                ", productId='" + productId + '\'' +
                ", beans=" + beans +
                ", reward=" + reward +
                ", fstatus=" + fstatus +
                ", price='" + price + '\'' +
                ", forder=" + forder +
                ", showInfo='" + showInfo + '\'' +
                ", ctime=" + ctime +
                ", mtime=" + mtime +
                '}';
    }
}
