package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 被守护表
 *
 * <AUTHOR>
 * @date 2023/4/19
 */
@TableName("t_guarded")
public class GuardedData {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 被守护者uid
     */
    private String uid;

    /**
     * 守护者uid
     */
    private String guardian;

    /**
     * 守护值
     */
    private Long guardValue;

    /**
     * 修改时间
     */
    private Integer mtime;

    public GuardedData() {
    }


    public GuardedData(String uid, String guardian, Long guardValue, Integer mtime) {
        this.uid = uid;
        this.guardian = guardian;
        this.guardValue = guardValue;
        this.mtime = mtime;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getGuardian() {
        return guardian;
    }

    public void setGuardian(String guardian) {
        this.guardian = guardian;
    }

    public Long getGuardValue() {
        return guardValue;
    }

    public void setGuardValue(Long guardValue) {
        this.guardValue = guardValue;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }
}
