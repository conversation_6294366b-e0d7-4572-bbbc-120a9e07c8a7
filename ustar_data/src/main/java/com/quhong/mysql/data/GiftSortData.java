package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <AUTHOR>
 * @date 2024/6/3
 */
@TableName("t_gift_sort")
public class GiftSortData {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer showType; // 展示类型： 0语聊房 1私信 2游戏房

    private Integer panelType; // 礼物面板类型 1=Hot,2=series,3=celebrity,4=action,5=Privilege,6=lucky

    private Integer giftId; // 礼物id

    private Integer panelOrder; // 礼物面板里的排序

    public GiftSortData() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getShowType() {
        return showType;
    }

    public void setShowType(Integer showType) {
        this.showType = showType;
    }

    public Integer getPanelType() {
        return panelType;
    }

    public void setPanelType(Integer panelType) {
        this.panelType = panelType;
    }

    public Integer getGiftId() {
        return giftId;
    }

    public void setGiftId(Integer giftId) {
        this.giftId = giftId;
    }

    public Integer getPanelOrder() {
        return panelOrder;
    }

    public void setPanelOrder(Integer panelOrder) {
        this.panelOrder = panelOrder;
    }
}
