package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;


@TableName("t_invite_fission")
public class InviteFissionData {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 邀请者
     */
    private String uid;

    /**
     * 被邀请者，键唯一
     */
    private String aid;

    /**
     * 设备id，键唯一
     */
    private String deviceId;

    /**
     * 总奖励邀请者钻石
     */
    private Integer totalReward;

    /**
     * 注册奖励邀请者钻石
     */
    private Integer regReward;

    /**
     * 充值奖励邀请者钻石(总)
     */
    private Integer rechargeReward;

    /**
     * 被邀请者日活天数奖励邀请者钻石
     */
    private Integer aliveDayReward;

    /**
     * 被邀请者日活天数，取值2,3,7（新版本为已领取的天数）
     */
    private Integer aliveDay;

    /**
     * 创建时间
     */
    private Integer ctime;

    /**
     * 修改时间
     */
    private Integer mtime;

    /**
     * 被邀请者发礼物奖励邀请者钻石(总)
     */
    private Integer giftReward;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public Integer getTotalReward() {
        return totalReward;
    }

    public void setTotalReward(Integer totalReward) {
        this.totalReward = totalReward;
    }

    public Integer getRechargeReward() {
        return rechargeReward;
    }

    public void setRechargeReward(Integer rechargeReward) {
        this.rechargeReward = rechargeReward;
    }

    public Integer getAliveDayReward() {
        return aliveDayReward;
    }

    public void setAliveDayReward(Integer aliveDayReward) {
        this.aliveDayReward = aliveDayReward;
    }

    public Integer getAliveDay() {
        return aliveDay;
    }

    public void setAliveDay(Integer aliveDay) {
        this.aliveDay = aliveDay;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public Integer getRegReward() {
        return regReward;
    }

    public void setRegReward(Integer regReward) {
        this.regReward = regReward;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public Integer getGiftReward() {
        return giftReward;
    }
//
    public void setGiftReward(Integer giftReward) {
        this.giftReward = giftReward;
    }
}
