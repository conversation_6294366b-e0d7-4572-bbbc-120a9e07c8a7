package com.quhong.msg;

import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.proto.YoustarProtoBase;

/**
 * 专门用于处理gate
 */
@Message(cmd = Cmd.CLIENT_RECV_ACK)
public class ClientRecvAck extends MarsServerMsg {
    private long clientMsgId;
    private int clientCmd;

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoBase.ClientRecvAck msg = YoustarProtoBase.ClientRecvAck.parseFrom(bytes);
        this.clientMsgId = msg.getMsgId();
        this.clientCmd = msg.getCmd();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoBase.ClientRecvAck.Builder builder = YoustarProtoBase.ClientRecvAck.newBuilder();
        builder.setMsgId(clientMsgId);
        builder.setCmd(clientCmd);
        return builder.build().toByteArray();
    }

    public long getClientMsgId() {
        return clientMsgId;
    }

    public void setClientMsgId(long clientMsgId) {
        this.clientMsgId = clientMsgId;
    }

    public int getClientCmd() {
        return clientCmd;
    }

    public void setClientCmd(int clientCmd) {
        this.clientCmd = clientCmd;
    }
}
