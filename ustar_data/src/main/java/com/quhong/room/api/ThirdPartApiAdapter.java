package com.quhong.room.api;

import com.quhong.constant.RoomConstant;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.room.api.agora.AgoraApi;
import com.quhong.room.api.data.MicChangeDTO;
import com.quhong.room.api.data.MsgDTO;
import com.quhong.room.api.zego.ZegoApi;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Set;

@Lazy
@Service
public class ThirdPartApiAdapter {
    private static final Logger logger = LoggerFactory.getLogger(ThirdPartApiAdapter.class);

    @Resource
    private ZegoApi zegoApi;
    @Resource
    private AgoraApi agoraApi;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private ThirdPartApiRedis thirdPartApiRedis;


    /**
     * 获取第三方api，默认当作zego处理
     *
     * @param rtcType 房间类型
     */
    private ThirdPartApi getApi(String roomId, Integer rtcType) {
        int roomRtcType = 0;
        if (null == rtcType) {
            MongoRoomData roomData = mongoRoomDao.findData(roomId);
            if (null == roomData) {
                logger.error("cannot find room data roomId={}", roomId);
            } else {
                roomRtcType = roomData.getRtc_type();
            }
        } else {
            roomRtcType = rtcType;
        }
        // 默认当作zego处理
        return RoomConstant.RTC_AGORA == roomRtcType ? agoraApi : zegoApi;
    }

    /**
     * 麦位变化
     */
    public void micChange(MicChangeDTO dto, Integer rtcType) {
        getApi(dto.getRoomId(), rtcType).micChange(dto);
    }

    public void sendBroadcastMessage(String roomId, String type, String msg) {
        getApi(roomId, RoomConstant.RTC_ZEGO).sendBroadcastMessage(roomId, type, msg);
    }

    /**
     * 房间流检验
     */
    public void checkStreamList(String roomId, Integer rtcType, CheckCallback checkCallback) {
        getApi(roomId, rtcType).checkStreamList(roomId, checkCallback);
    }

    /**
     * 删除流
     *
     * @param thirdPartId 第三方推拉流id
     */
    public void deleteStream(String roomId, String uid, String thirdPartId, Integer rtcType) {
//        if (ServerConfig.isNotProduct()) {
//            return;
//        }
        // 先记录删除流事件，防止报警误判
        thirdPartApiRedis.setDelStreamRecord(roomId, uid);
        getApi(roomId, rtcType).deleteStream(roomId, uid, thirdPartId);
    }

    /**
     * 记录第三方id（串流时已不再麦上）
     */
    public void setUid(String roomId, String uid, String thirdPartId) {
        thirdPartApiRedis.setUid(roomId, uid, thirdPartId);
    }

    public void clearMicCrossTimes(String roomId, String uid) {
        thirdPartApiRedis.clearMicCrossTimes(roomId, uid);
    }

    /**
     * 踢出用户
     */
    public void kickOut(String roomId, String uid, Integer rtcType) {
        getApi(roomId, rtcType).kickOut(roomId, uid);
    }

    /**
     * 发送IM消息
     *
     * @param roomId  房间id，一对一发送时可为空，不为空时发送给全房间
     * @param fromUid 发送者uid
     * @param toUid   接收者uid，发送给全房间时可为空
     * @param msgBody 消息内容
     */
    public void sendMsg(String roomId, String fromUid, Set<String> toUid, MsgDTO msgBody) {
        getApi(roomId, RoomConstant.RTC_ZEGO).sendMsg(roomId, fromUid, toUid, msgBody);
    }

    public String generateActorStreamId(Integer ownerRid, Integer rid, String aid, int rtcType) {
        return getApi(null, rtcType).generateActorStreamId(ownerRid, rid, aid);
    }
}
