package com.quhong.net.cache;

import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.timers.LoopTask;
import com.quhong.core.timers.SecondTask;
import com.quhong.core.timers.TimerService;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.Cmd;
import com.quhong.msg.ClientRecvAck;
import com.quhong.msg.MarsCacheMsg;
import com.quhong.mysql.dao.MsgStatDao;
import com.quhong.mysql.data.MsgStatData;
import com.quhong.net.sender.PlayerMsgSender;
import com.quhong.player.cache.Player;
import com.quhong.player.cache.PlayerCacheMap;
import com.quhong.service.mysql.ClientLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CopyOnWriteArrayList;

public class ActorMsgCache extends AbstractMsgCache {
    private static final Logger logger = LoggerFactory.getLogger(ActorMsgCache.class);

    private static final int RESEND_INTERVAL = 8 * 1000;
    private static final int RESEND_LIMIT = 3;
    private static final int CACHE_LIMIT = 30;
    private static final int TEST_EXPIRE_TIME = 30 * 1000; //发送过期时间
    private static final int CACHE_EXPIRE_EXPIRE_TIME = 60 * 1000; //cacheData过期时间
    private static final int TICK_INTERVAL = 60 * 1000;

    public static int STATUS_NONE = 0;
    public static int STATUS_SUCCESS = 1;
    public static int STATUS_EXPAND_LIMIT = 2;

    @Autowired
    protected PlayerMsgSender msgSender;
    @Autowired
    protected MsgStatDao msgStatDao;
    @Autowired
    protected PlayerCacheMap playerCacheMap;
    @Autowired
    protected ClientLogService clientLogService;

    private ConcurrentMap<String, MsgCacheActor> cacheMap = new ConcurrentHashMap<>();
    private ConcurrentLinkedQueue<CacheMsgData> msgQueue = new ConcurrentLinkedQueue<>();
    /**
     * 重发次数
     */
    private int reSendLimit;
    /**
     * 消息重发
     */
    private int reSendInterval;

    private int cacheLimit;

    public ActorMsgCache() {
        this(RESEND_LIMIT, RESEND_INTERVAL, CACHE_LIMIT);
    }

    public ActorMsgCache(int reSendLimit, int reSendInterval, int cacheLimit) {
        this.reSendLimit = reSendLimit;
        this.reSendInterval = reSendInterval;
        this.cacheLimit = cacheLimit;
    }

    @Override
    protected void start() {
        TimerService.getService().addTicker(new SecondTask(this.taskQueue) {
            @Override
            protected void execute() {
                try {
                    dealMsgQueue();
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                }
            }
        });
        TimerService.getService().addDelay(new LoopTask(this.taskQueue, TICK_INTERVAL) {
            @Override
            protected void execute() {
                checkAndDeleteData();
            }
        });
    }

    private void dealMsgQueue() {
        while (true) {
            CacheMsgData msgData = msgQueue.poll();
            if (msgData == null) {
                break;
            }
            // 检查msgData是否可以抛弃
            if (checkDiscard(msgData)) {
                continue;
            }
            if (!msgData.checkCanSend()) {
                msgQueue.add(msgData);
                break;
            }
            boolean sendResult = false;
            try {
                // 发送前重置发送时间
                msgData.setSendTime(System.currentTimeMillis());
                sendResult = msgSender.reSendMsg(msgData.getToUid(), msgData.getMsg());
            } catch (Exception e) {
                logger.error("resend msg error. toUid={} cmd={} msgId={} {}", msgData.getToUid(), msgData.getMsg().getCmd(), msgData.getMsg().getMsgId(), e);
            }
            if (sendResult) {
                // 增加玩家在线次数
                msgData.incReSendOnlineCount();
            }
            msgData.updateNextSendTime();
            msgData.incReSendCount();
            if (msgData.getReSendCount() >= reSendLimit) {
                continue;
            }
            msgQueue.add(msgData);
        }
    }

    private boolean checkDiscard(CacheMsgData msgData) {
        if (msgData.isDispose()) {
            return true;
        }
        if (msgData.getReSendCount() >= reSendLimit) {
            return true;
        }
        return false;
    }

    private void checkAndDeleteData() {
        for (Map.Entry<String, MsgCacheActor> entry : cacheMap.entrySet()) {
            checkAndClear(entry.getValue());
        }
    }

    private boolean canDeleteMsgData(CacheMsgData msgData) {
        if (msgData.isDispose()) {
            return true;
        }
        long interval = System.currentTimeMillis() - msgData.getSendTime();
        if (interval > CACHE_EXPIRE_EXPIRE_TIME) {
            return true;
        } else if (interval > TEST_EXPIRE_TIME) {
            if (msgData.getReSendCount() >= reSendLimit) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void addMsg(String toUid, MarsCacheMsg msg) {
        Player player = playerCacheMap.getPlayer(toUid);
        if (player == null) {
            logger.info("add msg to actor msg cache error. uid={}", toUid);
            return;
        }
        CacheMsgData msgData = new CacheMsgData(toUid, player.getOs(), msg, this.reSendInterval);
        addToCacheUnit(toUid, msgData);
        msgQueue.add(msgData);
//        logger.info("add msg to cache. fromUid={} toUid={} cmd={} msgId={}", msg.getFromUid(), toUid, msg.getCmd(), msg.getMsgId());
    }

    @Override
    public void addMsg(MarsCacheMsg msg) {
        throw new RuntimeException("unsupported Exception");
    }

    @Override
    public void receiveAck(Player player, ClientRecvAck ack) {
        String uid = player.getUid();
        int cmd = ack.getClientCmd();
        long msgId = ack.getClientMsgId();
        MsgCacheActor cacheActor = cacheMap.get(uid);
        if (cacheActor == null) {
            logger.info("receive ack. can not find cacheUnit. uid+{} cmd={} msgId={}", uid, cmd, msgId);
            return;
        }
        CopyOnWriteArrayList<CacheMsgData> msgList = cacheActor.getMsgList();
        Iterator<CacheMsgData> iter = msgList.iterator();
        while (iter.hasNext()) {
            CacheMsgData msgData = iter.next();
            if (msgData.getMsg().getMsgId() == msgId) {
                msgData.receiveAck();
                msgList.remove(msgData);
                // saveToDB(msgData, false);
                if (cmd == Cmd.CLIENT_LOG_REPORT_MSG) {
                    clientLogService.delClientLogReqFlag(uid);
                }
                return;
            }
        }
        logger.info("receive ack. can not find msgData. uid+{} cmd={} msgId={}", uid, cmd, msgId);
    }

    @Override
    public void requestUnSendMsg(Player player, String roomId, long lastMsgId) {
        String uid = player.getUid();
        MsgCacheActor cacheActor = cacheMap.get(uid);
        if (cacheActor == null) {
            logger.info("request actor unSend msg. can not find cacheActor. uid+{} lastMsgId={}", uid, lastMsgId);
            return;
        }
        CopyOnWriteArrayList<CacheMsgData> msgDataList = cacheActor.getMsgList();
        Iterator<CacheMsgData> iter = msgDataList.iterator();
        while (iter.hasNext()) {
            CacheMsgData msgData = iter.next();
            // 上线时，请求重发，只改变发送时间，不增加次数
            msgData.updateNextSendTime();
            msgSender.reSendMsg(msgData.getToUid(), msgData.getMsg());
        }
    }

    private void addToCacheUnit(String toUid, CacheMsgData msgData) {
        MsgCacheActor cacheActor = cacheMap.get(toUid);
        if (cacheActor == null) {
            synchronized (cacheMap) {
                cacheActor = cacheMap.get(toUid);
                // double check
                if (cacheActor == null) {
                    cacheActor = new MsgCacheActor(toUid);
                    cacheMap.put(toUid, cacheActor);
                }
            }
        }
        CopyOnWriteArrayList<CacheMsgData> msgList = cacheActor.getMsgList();
        msgList.add(msgData);
        while (msgList.size() >= cacheLimit) {
            CacheMsgData removeData = msgList.remove(0);
            if (removeData != null) {
                removeData.dispose();
                // 存储到数据库
                // saveToDB(msgData, true);
            }
        }
    }

    private void checkAndClear(MsgCacheActor cacheActor) {
        CopyOnWriteArrayList<CacheMsgData> msgList = cacheActor.getMsgList();
        Iterator<CacheMsgData> iter = msgList.iterator();
        while (iter.hasNext()) {
            CacheMsgData msgData = iter.next();
            if (canDeleteMsgData(msgData)) {
                msgData.dispose();
                msgList.remove(msgData);
                // 存储到数据库
                // saveToDB(msgData, false);
            }
        }
        if (msgList.isEmpty()) {
            cacheMap.remove(cacheActor.getUid(), cacheActor);
        }
    }

    private void saveToDB(CacheMsgData cacheData, boolean expandLimit) {
        if (cacheData.getMsg().getCmd() != Cmd.ENTER_ROOM_PUSH) {
            return;
        }
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                doSaveToDB(cacheData, expandLimit);
            }
        });
    }

    private void doSaveToDB(CacheMsgData cacheData, boolean expandLimit) {
        try {
            MsgStatData statData = new MsgStatData();
            statData.setCmd(cacheData.getMsg().getCmd());
            statData.setMsgId(cacheData.getMsg().getMsgId());
            statData.setToUid(cacheData.getToUid());
            statData.setRoomId(cacheData.getMsg().getRoomId());
            statData.setSendTestCount(cacheData.getReSendCount());
            statData.setOs(cacheData.getOs());
            boolean sendSuccess = cacheData.isReceiveAck();
            if (sendSuccess) {
                // 收到回包
                statData.setStatus(STATUS_SUCCESS);
                statData.setCostTimeMillis((int) (cacheData.getReceiveTime() - cacheData.getResendTime()));
            } else {
                if (expandLimit) {
                    statData.setStatus(STATUS_EXPAND_LIMIT);
                } else if (cacheData.getReSendOnlineCount() == 0) {
                    // 重发时，没有一次在线， 不做统计
                    return;
                }
            }
            statData.setCtime(DateHelper.getNowSeconds());
            msgStatDao.insert(statData);
        } catch (Exception e) {
            logger.error("msg stat save to db error. cmd={} msgId={} {}", cacheData.getMsg().getCmd(), cacheData.getMsg().getMsgId(), e.getMessage(), e);
        }
    }
}

