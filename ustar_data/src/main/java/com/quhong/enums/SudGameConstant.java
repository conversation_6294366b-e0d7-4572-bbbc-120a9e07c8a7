package com.quhong.enums;


/**
 * <AUTHOR>
 * @date 2022/6/30
 */
public class SudGameConstant {

    // 房间创建游戏的权限
    public static final int EVERYONE = 0; // 房间内的所以人都可以创建游戏
    public static final int MEMBER = 1; // 仅房间会员以上用户可创建
    public static final int ADMIN = 2; // 房间内的管理员以上身份可创建
    public static final int ONLY_ROOM_OWNER = 3; // 仅房主一个人可创建

    // 拉取即构接口的url
    public static final int GET_MSG_LIST_URL = 1; // 拉取游戏列表接口地址
    public static final int GET_MSG_INFO_URL = 2; // 拉取游戏信息接口地址
    public static final int GET_GAME_REPORT_INFO_URL = 3; // 查询游戏局信息接口地址
    public static final int GET_GAME_REPORT_INFO_URL_PAGE = 4; // 分页查询游戏局信息接口地址
    public static final int PUSH_EVENT = 5;
    public static final int CREATE_ORDER_URL = 6;

    // 即构小游戏
    public static final int BUMPER_BLASTER_GAME = 1; // 碰碰我最强
    public static final int LUDO_GAME = 2; // 飞行棋
    public static final int UMO_GAME = 3; // UMO
    public static final int MONSTER_CRUSH_GAME = 4; // 消消乐
    public static final int DOMINO_GAME = 5; // 多米诺
    public static final int CARROM_POOL_GAME = 6; // 克罗姆
    public static final int BILLIARD_GAME = 7; // 桌球-8球
    public static final int JACKAROO_GAME = 8; // Jackaroo游戏
    public static final int BALOOT_GAME = 9; // Baloot游戏
    public static final int WOISSPY_GAME = 10; // 谁是卧底游戏

    // sud游戏表里的状态
    public static final int GAME_MATCHING = 1; // 匹配中
    public static final int GAME_PROCESSING = 2; // 进行中
    public static final int GAME_FINISH = 3; // 正常结束
    public static final int GAME_CLOSED = 4; // 游戏关闭(创建者解散，超时关闭等)
    public static final int GAME_MATCHING_PAUSE = 5; // 匹配中手动点暂停

    // 操作游戏消息
    public static final int GAME_CREATE_MSG = 1; // 创建游戏
    public static final int GAME_CLOSED_MSG = 2; // 关闭游戏
    public static final int GAME_TIME_OUT_MSG = 3; // 游戏超时关闭
    public static final int GAME_START_MSG = 4; // 开始游戏
    public static final int GAME_REMIND_MSG = 5;// remind 提示消息

    // 上报类型 report_type
    public static final String GAME_START_REPORT_TYPE = "game_start"; // 战斗开始通知
    public static final String GAME_SETTLE_REPORT_TYPE = "game_settle"; // 	战斗结算通知

    public static final String CREATE_ORDER_LUDO = "reroll_the_dice"; // ludo重掷骰子
    public static final String CREATE_ORDER_UMO = "auto_umo"; // 	自动喊UMO

    // 游戏的房间类型
    public static final int VOICE_ROOM = 1; // 语音房
    public static final int GAME_ROOM = 5; // 游戏房

    public static final String EMPTY_LEADER_UID = "";

    // https://static.youstar.live/gameRoomGame/?enterRoomType=%s&gameType=%s&gameId=%s&currencyType=%s&currency=%s
    public static final String ROOM_GAME_URL = "https://static.youstar.live/gameRoomGame/";

    public static final String TEST_ROOM_GAME_URL = "https://test2.qmovies.tv/gameRoomGame/";
}
