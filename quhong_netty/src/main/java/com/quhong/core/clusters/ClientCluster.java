package com.quhong.core.clusters;

import com.quhong.core.annotation.MessageGroup;
import com.quhong.core.annotation.MsgExecutorGroup;
import com.quhong.core.clusters.balance.HashBalance;
import com.quhong.core.clusters.balance.IPlayerBalance;
import com.quhong.core.clusters.clients.ClusterClientConnector;
import com.quhong.core.clusters.clients.ClusterClientGroup;
import com.quhong.core.clusters.clients.ClusterSocketClient;
import com.quhong.core.clusters.servers.ClusterSocketServer;
import com.quhong.core.concurrency.queues.TaskQueue;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.register.RedisDiscovery;
import com.quhong.core.timers.LoopTask;
import com.quhong.core.timers.TimerService;
import com.quhong.core.utils.SpringUtils;
import com.quhong.datas.ServerData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BinaryOperator;

public class ClientCluster extends TaskQueue {
    private static final Logger logger = LoggerFactory.getLogger(ClientCluster.class);

    protected static final int INTERVAL = 10 * 1000;
    protected static final int clientSize = 10;

    @Autowired
    private RedisDiscovery redisDiscovery;

    private Map<Integer, ClusterClientGroup> socketClientMap;

    @Autowired
    protected MessageGroup messageGroup;
    @Autowired
    protected MsgExecutorGroup executorGroup;

    protected HashBalance hashBalance;

    protected Cluster cluster;

    private int clusterId;

    public ClientCluster(int clusterId, boolean useBalance){
        this.clusterId = clusterId;
        this.socketClientMap = new ConcurrentHashMap<>();
        this.cluster = ClusterGroup.getGroup().getCluster(clusterId);
        if(useBalance){
            hashBalance = SpringUtils.getBean(HashBalance.class);
        }
        if(this.cluster == null){
            this.cluster = new MapListClusterImpl(clusterId, hashBalance);
            ClusterGroup.getGroup().addCluster(this.cluster);
        }
    }

    @PostConstruct
    public void postInit(){
        List<ServerData> list = redisDiscovery.getServerList(clusterId);
        if(list == null){
            list = new ArrayList<>();
        }
        for(ServerData serverData : list){
            ClusterClientGroup socketClient = createGroup(serverData);
            socketClient.start();
            this.socketClientMap.put(serverData.getServerId(), socketClient);
            logger.info("{} -> {} find new server.", ServerConfig.getServerID(), serverData.getServerId());
        }
        TimerService.getService().addDelay(new LoopTask(this, INTERVAL) {
            @Override
            protected void execute() {
                compareAndUpdate();
            }
        });
    }

    public void compareAndUpdate(){
        List<ServerData> list = redisDiscovery.getServerList(clusterId);
        if(list == null){
            list = new ArrayList<>();
        }
        Map<Integer, ServerData> map = new HashMap<>();
        for(ServerData serverData : list){
            map.put(serverData.getServerId(), serverData);
            ClusterClientGroup client = this.socketClientMap.get(serverData.getServerId());
            if(client == null){
                client = createGroup(serverData);
                client.start();
                this.socketClientMap.put(serverData.getServerId(), client);
                logger.info("{} -> {} find new server.", ServerConfig.getServerID(), serverData.getServerId());
            }else{
                if(client.getServerData().isSame(serverData)){
                    continue;
                }else{
                    this.socketClientMap.remove(serverData.getServerId());
                    client.dispose();
                    // close the old.create a new client.
                    client = createGroup(serverData);
                    client.start();
                    this.socketClientMap.put(serverData.getServerId(), client);
                    logger.info("{} -> {} server is change. refresh the connector.", ServerConfig.getServerID(), serverData.getServerId());
                }
            }
        }
        if(this.socketClientMap.size() <= 1){
            // 只有一个链接，不删除
            return;
        }
        for(ClusterClientGroup client : this.socketClientMap.values()){
            int serverId = client.getServerData().getServerId();
            if(map.containsKey(serverId)){
                continue;
            }else{
                logger.info("{} -> {} server is disappear.connector will dispose.", ServerConfig.getServerID(), serverId);
                this.socketClientMap.remove(serverId);
                client.dispose();
            }
        }
    }

    protected ClusterClientGroup createGroup(ServerData serverData){
        return new ClusterClientGroup(serverData, this, clientSize);
    }

    public ClusterSocketClient createClient(ServerData serverData){
        return new ClusterSocketClient(serverData, cluster, messageGroup, executorGroup);
    }

    public void initSocketClient(ClusterSocketClient client){
        client.init(new ClusterClientConnector(cluster, client.getServerData(), messageGroup, executorGroup, true));
    }
}
