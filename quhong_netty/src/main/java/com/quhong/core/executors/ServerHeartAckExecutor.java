package com.quhong.core.executors;

import com.quhong.core.annotation.MsgExecutor;
import com.quhong.core.msg.Msg;
import com.quhong.core.net.connect.IConnector;
import com.quhong.enums.BaseServerCmd;

@MsgExecutor
public class ServerHeartAckExecutor extends AbstractMsgExecutor {
    public ServerHeartAckExecutor() {
        super(BaseServerCmd.SERVER_HEART_ACK);
    }

    @Override
    public void execute(IConnector connector, Msg msg) {

    }
}
