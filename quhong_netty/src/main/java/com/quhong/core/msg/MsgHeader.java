package com.quhong.core.msg;

import com.quhong.core.errors.CodecException;
import io.netty.buffer.ByteBuf;

public abstract class MsgHeader {
    protected int headerLength; //头长度 1字节


    public MsgHeader() {

    }

    public abstract void fromHeader(ByteBuf buf) throws CodecException;

    public abstract void toHeader(ByteBuf buf) throws CodecException;

    public int getHeaderLength() {
        return headerLength;
    }

    public void setHeaderLength(int headerLength) {
        this.headerLength = headerLength;
    }
}
