package com.quhong.utils;

import com.quhong.core.timers.LoopTask;
import com.quhong.core.timers.TimerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

public class DirtyUtils {
    private static final Logger logger = LoggerFactory.getLogger(DirtyUtils.class);

    private static List<String> dirtyList = new ArrayList<>();

    private static String dirtyFilePath;

    public static void start(String filePath){
        dirtyFilePath = filePath;
        loadFile(dirtyFilePath);
        // 一小时加载一次
        TimerService.getService().addDelay(new LoopTask(null, 3600 * 1000) {
            @Override
            protected void execute() {
                loadFile(dirtyFilePath);
            }
        });
    }

    public static void loadFile(String filePath){
        Path path = Paths.get(filePath);
        if(!Files.exists(path)){
            logger.info("dirty code not exist");
            return;
        }
        try {
            List<String> tempList = new ArrayList<>();
            List<String> fileList = Files.readAllLines(path, Charset.forName("UTF-8"));
            for(String line : fileList){
                String[] codeArr = line.split(",");
                for(String code : codeArr){
                    if(StringUtils.isEmpty(code)){
                        continue;
                    }
                    code.trim();
                    if(code.length() == 0){
                        continue;
                    }
                    tempList.add(code);
                }
            }
            dirtyList = tempList;
            logger.info("load dirty file path. size:{}", dirtyList.size());
        }catch (Exception e){
            logger.error("load dirty code path error. {}", e.getMessage(), e);
        }
    }

    /**
     * 是否包含脏字符
     * @param msgBody
     * @return
     */
    public static boolean containerDirty(String msgBody){
        if(StringUtils.isEmpty(msgBody)){
            return false;
        }
        String temp = msgBody.toLowerCase();
        for(String code : dirtyList){
            if(temp.indexOf(code) > -1){
                return true;
            }
        }
        return false;
    }
}
