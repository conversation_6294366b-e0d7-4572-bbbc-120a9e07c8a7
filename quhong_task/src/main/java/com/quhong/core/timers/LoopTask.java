package com.quhong.core.timers;


import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.queues.ITaskQueue;
import com.quhong.core.concurrency.tasks.AbstractTask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * create by ma<PERSON><PERSON> on 2019/1/9
 */
public abstract class LoopTask extends AbstractTask implements Delayed {
    protected static final Logger logger = LoggerFactory.getLogger(LoopTask.class);

    private long delayTime;
    private long expireTime;
    private long loopCount = -1;
    /** 是否正处在触发未执行完状态 **/
    private AtomicBoolean attaching = new AtomicBoolean();
    private long addTime;
    private long discardDuration = 5000;

    /**
     *
     * @param delayTime 延时 单位 毫秒
     */
    public LoopTask(int delayTime){
        this(null, delayTime, 0);
    }

    /**
     *
     * @param delayTime 延时 单位 毫秒
     * @param loopCount 循环次数
     */
    public LoopTask(int delayTime, int loopCount){
        this(null, delayTime, loopCount);
    }

    /**
     *
     * @param queue 约束队列
     * @param delayTime 单位 ms
     */
    public LoopTask(ITaskQueue queue, int delayTime){
        this(queue, delayTime, 0);
    }

    /**
     * @param queue 约束队列
     * @param delayTime 单位 ms
     * @param loopCount 循环次数 小于等于0表示无限循环
     */
    public LoopTask(ITaskQueue queue, int delayTime, int loopCount){
        this.queue = queue;
        if(delayTime < 100){
            logger.error("delayTime less then 100 ms.please check the param.delayTime:{}", delayTime);
        }
        this.addTime = System.currentTimeMillis();
        this.delayTime = delayTime;
        this.expireTime = System.currentTimeMillis() + this.delayTime;
        this.loopCount = loopCount;
        if(this.loopCount <= 0){
            this.loopCount = -1;
        }
    }

    @Override
    public long getDelay(TimeUnit unit) {
        return unit.convert(this.expireTime - System.currentTimeMillis(), unit);
    }

    /**
     * 触发
     */
    public void attach(){
        if(isCancel()) {
            return;
        }
        // 如果没有正在触发，则添加执行，否则，不执行，防止雪崩。
        if(attaching.compareAndSet(false, true)) {
            this.addTime = System.currentTimeMillis();
            if (this.queue != null) {
                this.queue.add(this);
            } else {
                BaseTaskFactory.getFactory().addSlow(this);
            }
        }
    }

    @Override
    public int compareTo(Delayed o) {
        return (int)(this.getDelay(TimeUnit.SECONDS) - o.getDelay(TimeUnit.SECONDS));
    }

    /**
     * 取消执行
     */
    public void cancel() {
        this.loopCount = 0;
    }

    public boolean isCancel() {
        return this.loopCount == 0;
    }

    /**
     * 执行下一次
     * @return true:可以进行下一次，false，不能进行下一次
     */
    public boolean next() {
        this.expireTime += this.delayTime;
        if(loopCount < 0){
            return true;
        }
        if(loopCount == 0) {
            return false;
        }
        loopCount --;
        if(this.loopCount > 0){
            return true;
        }
        return false;
    }

    @Override
    public void run() {
        if(checkDiscard()){
            try {
                discard();
            }catch (Exception e){
                logger.error("discard error={}", e.getMessage(), e);
            }
        }
        super.run();
        // 执行完，修改attaching状态为false.
        attaching.set(false);
    }

    protected boolean checkDiscard(){
        if(System.currentTimeMillis() - this.addTime > discardDuration){
            return true;
        }
        return false;
    }

    /**
     * 超时丢弃
     */
    protected void discard(){
        logger.error("{} was discarded", this.toString());
    }
}
