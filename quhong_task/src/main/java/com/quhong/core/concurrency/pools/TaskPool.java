package com.quhong.core.concurrency.pools;


import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.queues.ITaskQueue;
import com.quhong.core.concurrency.tasks.AbstractTask;
import com.quhong.core.concurrency.tasks.ITask;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.dispose.Disposer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 任务池，对jdk线程池的包装
 * create by maoyule on 2019/1/7
 */
public class TaskPool extends Disposer {
    private static final Logger logger = LoggerFactory.getLogger(TaskPool.class);

    private static final int WARNING_BLOCK_COUNT = 2000;

    private final AtomicInteger poolNumber = new AtomicInteger(0);
    private ExecutorService taskService;
    private String name;
    private int threadCount = 8;
    private int warningBlockCount = WARNING_BLOCK_COUNT;

    public TaskPool() {
        this(WARNING_BLOCK_COUNT);
    }

    public TaskPool(int warningBlockCount) {
        if (warningBlockCount > 0) {
            this.warningBlockCount = warningBlockCount;
        }
    }

    @Override
    protected void doDispose() {
        taskService.shutdown();
    }

    /**
     * 初始化
     *
     * @param taskService
     */
    public TaskPool init(ExecutorService taskService) {
        this.taskService = taskService;
        BaseTaskFactory.getFactory().addTaskPool(this);
        return this;
    }

    /**
     * 初始化
     *
     * @param threadCount 线程池中线程数量
     * @param name        线程池名称
     */
    public TaskPool init(int threadCount, String name) {
        this.name = name;
        this.threadCount = threadCount;
        MyThreadPoolExecutor executor = newThreadPool(threadCount, name);
        executor.setWarningBlockCount(warningBlockCount);
        ExecutorService oldService = this.taskService;
        this.taskService = executor;
        if (oldService != null) {
            if (oldService instanceof ThreadPoolExecutor) {
                ThreadPoolExecutor tmpService = (ThreadPoolExecutor) oldService;
                List<Runnable> runList = new ArrayList<>();
                tmpService.getQueue().drainTo(runList);
                logger.error("old task service shutdown runList.size={}. taskName={}", runList.size(), this.name);
                for (Runnable runnable : runList) {
                    this.taskService.execute(runnable);
                }
            }
            Task task = new Task() {
                @Override
                protected void execute() {
                    logger.error("old task service shutdown. taskName={}", TaskPool.this.name);
                    oldService.shutdownNow();
                    logger.error("old task service shutdown success. taskName={}", TaskPool.this.name);
                }
            };
            if (this != BaseTaskFactory.getFactory().getSystemPool()) {

                // 用系统线程去执行，避免卡死
                BaseTaskFactory.getFactory().addSystem(task);
            } else {
                task.run();
            }
        }
        BaseTaskFactory.getFactory().addTaskPool(this);
        return this;
    }

    public TaskPool init(int corePoolSize,
                         int maximumPoolSize, String name) {
        this.name = name;
        this.threadCount = corePoolSize;
        MyThreadPoolExecutor executor = newThreadPool(corePoolSize, name);
        executor.setWarningBlockCount(warningBlockCount);
        this.taskService = executor;
        BaseTaskFactory.getFactory().addTaskPool(this);
        return this;
    }

    /**
     * 初始化单个单个线程池
     *
     * @param name
     * @return
     */
    public TaskPool initSingle(String name) {
        this.name = name;
        this.threadCount = 1;
        this.taskService = Executors.newSingleThreadExecutor(new MyThreadFactory(name));
        return this;
    }

    public void tick() {
        if (this.taskService instanceof MyThreadPoolExecutor) {
            ((MyThreadPoolExecutor) this.taskService).tickMonitor();
        }
    }

    /**
     * 创建线程数固定的线程池
     *
     * @param corePoolSize 线程数量
     * @param sThreadName
     * @return
     */
    private MyThreadPoolExecutor newThreadPool(int corePoolSize, String sThreadName) {
        int poolIdx = poolNumber.incrementAndGet();
        if (poolIdx > 1) {
            // 线程池名字做区分
            sThreadName = sThreadName + poolIdx;
        }
        return new MyThreadPoolExecutor(this, sThreadName, corePoolSize, corePoolSize, 0L,
                TimeUnit.MINUTES, new LinkedBlockingQueue<>(),
                new MyThreadFactory(sThreadName), new ThreadPoolExecutor.AbortPolicy());
    }

    public void add(ITask task) {
        taskService.execute(task);
    }

    public void add(ITaskQueue queue) {
        taskService.execute(queue);
    }

    /**
     * 增加任务
     *
     * @param runnable
     */
    public void submit(Runnable runnable) {
        taskService.execute(runnable);
    }

    /**
     * 停止某个线程，强烈建议仅在发现死锁时调用
     *
     * @param threadName
     */
    public void stopThread(String threadName) {
        if (taskService instanceof MyThreadPoolExecutor) {
            ((MyThreadPoolExecutor) taskService).stopThread(threadName);
        }
    }

    public void refresh() {
        this.init(this.threadCount, this.name);
    }
}
