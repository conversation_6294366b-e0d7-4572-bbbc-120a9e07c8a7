package com.quhong.core.distribution;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

public class ConstantLock extends DistributeLock {
    private static final Logger logger = LoggerFactory.getLogger(ConstantLock.class);

    public ConstantLock(String key, String tailValue) {
        super(key);
        Assert.notNull(tailValue, "redis tailValue is null");
        this.lockValue = "lock_" + tailValue;
    }

    public ConstantLock(String name, long expireTime, String tailValue){
        super(name, expireTime);
        Assert.notNull(tailValue, "redis tailValue is null");
        this.lockValue = "lock_" + tailValue;
    }

    @Override
    public boolean keepLocking(){
        return doKeepLocking();
    }

    @Override
    protected String generateLockValue(){
        return this.lockValue;
    }
}
