package com.quhong.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class UrlEncodeHelper {

    private static final Logger logger = LoggerFactory.getLogger(UrlEncodeHelper.class);

    /**
     * 通过map对象创建encode链接
     * @param params
     * @return
     * @throws UnsupportedEncodingException
     */
    public static String createLinkByMap(Map<String, String> params) throws UnsupportedEncodingException {
        List<String> keys = new ArrayList<String>(params.keySet());
        Collections.sort(keys);
        String link = "";
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = params.get(key);
            value = URLEncoder.encode(value, "UTF-8");
            if (i == keys.size() - 1) {//拼接时，不包括最后一个&字符
                link = link + key + "=" + value;
            } else {
                link = link + key + "=" + value + "&";
            }
        }
        return link;
    }

    /**
     * 通过对象创建encode链接
     * @param object
     * @return
     * @throws UnsupportedEncodingException
     */
    public static String createLinkByObject(Object object) throws UnsupportedEncodingException {
        String str = JSON.toJSONString(object);
        Map map = JSONObject.parseObject(str, Map.class);
        return createLinkByMap(map);
    }

}
