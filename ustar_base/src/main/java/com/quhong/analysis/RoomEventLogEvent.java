package com.quhong.analysis;

/**
 * 房间活动触发事件
 *
 * <AUTHOR>
 * @date 2023/2/14
 */
public class RoomEventLogEvent extends UserEvent {

    /**
     * 房间id
     */
    private String room_id;

    /**
     * 房间活动id
     */
    private int room_event_id;

    /**
     * 房间活动名称
     */
    private String room_event_name;

    /**
     * 房间活动类型
     * 1 Party 2 Chat 3 Game 4 Singing 5 Competition 6 Poetry 7 Other
     */
    private int room_event_type;

    /**
     * 房间活动开始时间
     */
    private int room_event_start_time;

    /**
     * 房间活动结束时间
     */
    private int room_event_end_time;

    /**
     * 房间活动持续时长（单位：时）
     */
    private int room_event_duration;

    /**
     * 房间活动消耗钻石数
     */
    private long room_event_cost_beans;

    /**
     * 房间活动订阅人数
     */
    private int room_event_sub_num;

    /**
     * 活动期间送礼人数
     */
    private int room_event_send_gift_sum;

    /**
     * 活动期间收礼人数
     */
    private int room_event_receive_gift_sum;

    /**
     * 房间活动创建者
     */
    private String room_event_creator;

    /**
     * 房间活动创建者身份 1房主 2管理员
     */
    private int room_event_creator_identity;

    /**
     * 房间活动海报url
     */
    private String room_event_poster_url;

    /**
     * 房间活动封面url
     */
    private String room_event_cover_url;

    /**
     * 是否删除 0未删除 1已删除
     */
    private int is_delete;
    /**
     * 是否申请扶持补贴
     */
    private int is_apply_support;
    /**
     * 创建时间
     */
    private int ctime;

    @Override
    public String getEventName() {
        return "room_event_log";
    }

    @Override
    public int getEventTime() {
        return ctime;
    }

    public String getRoom_id() {
        return room_id;
    }

    public void setRoom_id(String room_id) {
        this.room_id = room_id;
    }

    public int getRoom_event_id() {
        return room_event_id;
    }

    public void setRoom_event_id(int room_event_id) {
        this.room_event_id = room_event_id;
    }

    public String getRoom_event_name() {
        return room_event_name;
    }

    public void setRoom_event_name(String room_event_name) {
        this.room_event_name = room_event_name;
    }

    public int getRoom_event_type() {
        return room_event_type;
    }

    public void setRoom_event_type(int room_event_type) {
        this.room_event_type = room_event_type;
    }

    public int getRoom_event_start_time() {
        return room_event_start_time;
    }

    public void setRoom_event_start_time(int room_event_start_time) {
        this.room_event_start_time = room_event_start_time;
    }

    public int getRoom_event_end_time() {
        return room_event_end_time;
    }

    public void setRoom_event_end_time(int room_event_end_time) {
        this.room_event_end_time = room_event_end_time;
    }

    public int getRoom_event_duration() {
        return room_event_duration;
    }

    public void setRoom_event_duration(int room_event_duration) {
        this.room_event_duration = room_event_duration;
    }

    public long getRoom_event_cost_beans() {
        return room_event_cost_beans;
    }

    public void setRoom_event_cost_beans(long room_event_cost_beans) {
        this.room_event_cost_beans = room_event_cost_beans;
    }

    public int getRoom_event_sub_num() {
        return room_event_sub_num;
    }

    public void setRoom_event_sub_num(int room_event_sub_num) {
        this.room_event_sub_num = room_event_sub_num;
    }

    public String getRoom_event_creator() {
        return room_event_creator;
    }

    public void setRoom_event_creator(String room_event_creator) {
        this.room_event_creator = room_event_creator;
    }

    public int getRoom_event_creator_identity() {
        return room_event_creator_identity;
    }

    public void setRoom_event_creator_identity(int room_event_creator_identity) {
        this.room_event_creator_identity = room_event_creator_identity;
    }

    public String getRoom_event_poster_url() {
        return room_event_poster_url;
    }

    public void setRoom_event_poster_url(String room_event_poster_url) {
        this.room_event_poster_url = room_event_poster_url;
    }

    public String getRoom_event_cover_url() {
        return room_event_cover_url;
    }

    public void setRoom_event_cover_url(String room_event_cover_url) {
        this.room_event_cover_url = room_event_cover_url;
    }

    public int getIs_delete() {
        return is_delete;
    }

    public void setIs_delete(int is_delete) {
        this.is_delete = is_delete;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public int getRoom_event_send_gift_sum() {
        return room_event_send_gift_sum;
    }

    public void setRoom_event_send_gift_sum(int room_event_send_gift_sum) {
        this.room_event_send_gift_sum = room_event_send_gift_sum;
    }

    public int getRoom_event_receive_gift_sum() {
        return room_event_receive_gift_sum;
    }

    public void setRoom_event_receive_gift_sum(int room_event_receive_gift_sum) {
        this.room_event_receive_gift_sum = room_event_receive_gift_sum;
    }

    public int getIs_apply_support() {
        return is_apply_support;
    }

    public void setIs_apply_support(int is_apply_support) {
        this.is_apply_support = is_apply_support;
    }
}
