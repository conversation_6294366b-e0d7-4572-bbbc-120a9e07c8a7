package com.quhong.analysis;

/**
 *  每小时活跃房间最大人数
 *
 */
public class RoomMaxOnlineUsersEvent extends UserEvent{

    private String room_id;
    private String date;
    private int max_online_users;
    private int ctime;

    @Override
    public String getEventName() {
        return "room_max_online_users";
    }

    @Override
    public int getEventTime() {
        return ctime;
    }


    public String getRoom_id() {
        return room_id;
    }

    public void setRoom_id(String room_id) {
        this.room_id = room_id;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public int getMax_online_users() {
        return max_online_users;
    }

    public void setMax_online_users(int max_online_users) {
        this.max_online_users = max_online_users;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}
