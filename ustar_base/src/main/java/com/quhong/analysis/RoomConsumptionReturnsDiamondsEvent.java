package com.quhong.analysis;


public class RoomConsumptionReturnsDiamondsEvent extends UserEvent {
    private int ctime;
    private Integer atype;
    private Integer diamonds_num;


    @Override
    public String getEventName() {
        return "room_consumption_returns_diamonds";
    }

    @Override
    public int getEventTime() {
        return ctime;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public Integer getAtype() {
        return atype;
    }

    public void setAtype(Integer atype) {
        this.atype = atype;
    }

    public Integer getDiamonds_num() {
        return diamonds_num;
    }

    public void setDiamonds_num(Integer diamonds_num) {
        this.diamonds_num = diamonds_num;
    }
}
