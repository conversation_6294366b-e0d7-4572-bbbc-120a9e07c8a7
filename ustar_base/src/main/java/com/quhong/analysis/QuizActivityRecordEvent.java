package com.quhong.analysis;

/**
 * <AUTHOR>
 * @date 2023/1/3
 */
public class QuizActivityRecordEvent extends UserEvent{

    /**
     * 答题行为 1为开始答题 2为结束答题 3为选择复活
     */
    private int quiz_action;
    /**
     * 开始作答时间
     */
    private int ctime;
    /**
     * 答题分数  仅结束答题时上报
     */
    private int quiz_score;
    /**
     * 作答时长（单位秒）  仅结束答题时上报
     */
    private int quiz_time;
    /**
     * 通关关卡
     */
    private int quiz_level;
    /**
     * 仅结束答题时上报 1答题成功通关 2答题失败
     */
    private int quit_end_reason;
    /**
     * 答题复活价格 仅选择复活时上报 免费复活则为0
     */
    private int quiz_rebirth_price;
    /**
     * 答题复活价格类型  仅选择复活时上报 0: 免费 1: 钻石 2: 心心
     */
    private int quiz_rebirth_price_type;


    @Override
    public String getEventName() {
        return "quiz_activity_record";
    }

    @Override
    public int getEventTime() {
        return ctime;
    }

    public int getQuiz_action() {
        return quiz_action;
    }

    public void setQuiz_action(int quiz_action) {
        this.quiz_action = quiz_action;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public int getQuiz_score() {
        return quiz_score;
    }

    public void setQuiz_score(int quiz_score) {
        this.quiz_score = quiz_score;
    }

    public int getQuiz_time() {
        return quiz_time;
    }

    public void setQuiz_time(int quiz_time) {
        this.quiz_time = quiz_time;
    }

    public int getQuiz_level() {
        return quiz_level;
    }

    public void setQuiz_level(int quiz_level) {
        this.quiz_level = quiz_level;
    }

    public int getQuit_end_reason() {
        return quit_end_reason;
    }

    public void setQuit_end_reason(int quit_end_reason) {
        this.quit_end_reason = quit_end_reason;
    }

    public int getQuiz_rebirth_price() {
        return quiz_rebirth_price;
    }

    public void setQuiz_rebirth_price(int quiz_rebirth_price) {
        this.quiz_rebirth_price = quiz_rebirth_price;
    }

    public int getQuiz_rebirth_price_type() {
        return quiz_rebirth_price_type;
    }

    public void setQuiz_rebirth_price_type(int quiz_rebirth_price_type) {
        this.quiz_rebirth_price_type = quiz_rebirth_price_type;
    }
}
