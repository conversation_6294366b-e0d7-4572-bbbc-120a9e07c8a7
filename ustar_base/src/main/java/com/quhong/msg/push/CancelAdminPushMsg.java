package com.quhong.msg.push;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.proto.YoustarProtoUser;

@Message(cmd = Cmd.CANCEL_ADMIN_MESSAGE)
public class CancelAdminPushMsg extends MarsServerMsg {
    private String aid;
    private int now_utype; // 0 观众，1 会员, 2 管理员
    private int admin; // 1是管理员，2是非管理员
    private int role; //身份标识

    @Override
    public void fillFrom(JSONObject object) {
        JSONObject data = object.getJSONObject("data");
        if (null == data) {
            return;
        }
        this.aid = data.getString("aid");
        this.now_utype = data.getIntValue("now_utype");
        this.admin = data.getIntValue("admin");
        this.role = data.getIntValue("role");
    }

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoUser.CancelAdminMessage msg = YoustarProtoUser.CancelAdminMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.aid = msg.getAid();
        this.now_utype = msg.getNowUtype();
        this.admin = msg.getAdmin();
        this.role = msg.getRole();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoUser.CancelAdminMessage.Builder builder = YoustarProtoUser.CancelAdminMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setAid(aid == null ? "" : aid);
        builder.setNowUtype(now_utype);
        builder.setAdmin(admin);
        builder.setRole(role);
        return builder.build().toByteArray();
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public int getNow_utype() {
        return now_utype;
    }

    public void setNow_utype(int now_utype) {
        this.now_utype = now_utype;
    }

    public int getAdmin() {
        return admin;
    }

    public void setAdmin(int admin) {
        this.admin = admin;
    }

    public int getRole() {
        return role;
    }

    public void setRole(int role) {
        this.role = role;
    }
}
