package com.quhong.msg.push;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.annotation.Message;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.proto.YoustarProtoUser;

@Message(cmd = Cmd.USER_MONITOR_PUSH)
public class UserMonitorPushMsg extends MarsServerMsg {
    private int type; // 1 警告 2 冻结 3 禁止 4 解除
    private String reason; //原因
    private long sev_cur_time;
    private long releaseAt; //解除时间 单位 秒
    private int block_term;  //解除时间类型  冻结（1 :3 hours 2:24 hours 3:7 days ） 封号（1：24hours  2:7 days 3:parmanent 5 30 days）

    public void fillFrom(JSONObject src, int type){
        this.type = type;
        this.reason = src.getString("reason");
        this.releaseAt = src.getIntValue("release_at");
        this.sev_cur_time = DateHelper.getNowSeconds();
        this.block_term = src.getIntValue("block_term");
    }

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoUser.UserMonitorMsg msg = YoustarProtoUser.UserMonitorMsg.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.type = msg.getType();
        this.reason = msg.getReason();
        this.releaseAt = msg.getReleaseAt();
        this.sev_cur_time = msg.getSevCurTime();
        this.block_term = msg.getBlockTerm();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoUser.UserMonitorMsg.Builder builder = YoustarProtoUser.UserMonitorMsg.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setType(type);
        builder.setReason(reason == null ? "" : reason);
        builder.setReleaseAt(releaseAt);
        builder.setSevCurTime(sev_cur_time);
        builder.setBlockTerm(block_term);
        return builder.build().toByteArray();
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public long getReleaseAt() {
        return releaseAt;
    }

    public void setReleaseAt(long releaseAt) {
        this.releaseAt = releaseAt;
    }

    public long getSev_cur_time() {
        return sev_cur_time;
    }

    public void setSev_cur_time(long sev_cur_time) {
        this.sev_cur_time = sev_cur_time;
    }

    public int getBlock_term() {
        return block_term;
    }

    public void setBlock_term(int block_term) {
        this.block_term = block_term;
    }
}
