package com.quhong.msg.push;

import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.proto.YoustarProtoUser;

@Message(cmd = Cmd.GIFT_NAMING_MSG)
public class GiftNamingMsg extends MarsServerMsg {
    //    private String gift_name;
//    private String gift_icon;
    private String gift_num;
    private long sort_num;
    //    private int comp;
    private Integer giftId;
    private String sendUserName; //冠名用户名字
    private String sendUserHead; // 冠名用户头像
    private long sendNum;// 冠名礼物数量,为0不展示
    private long remainNum; //再送多少个超过冠名用户,为0不展示
    private int state; // 当前礼物是否可发送 1可发送 0不可发送
    private String sendUserMicUrl; //冠名用户的麦位框
    private String aid;// 收礼用户的uid
    private String sendUserUid; //冠名用户的uid

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoUser.GiftNamingMessage msg = YoustarProtoUser.GiftNamingMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
//        this.gift_name = msg.getGiftName();
//        this.gift_icon = msg.getGiftIcon();
        this.gift_num = msg.getGiftNum();
        this.sort_num = msg.getSortNum();
//        this.comp = msg.getComp();
        this.giftId = msg.getGiftId();
        this.sendUserName = msg.getSendUserName();
        this.sendUserHead = msg.getSendUserHead();
        this.sendNum = msg.getSendNum();
        this.remainNum = msg.getRemainNum();
        this.state = msg.getState();
        this.sendUserMicUrl = msg.getSendUserMicUrl();
        this.aid = msg.getAid();
        this.sendUserUid = msg.getSendUserUid();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoUser.GiftNamingMessage.Builder builder = YoustarProtoUser.GiftNamingMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
//        builder.setGiftName(gift_name);
//        builder.setGiftIcon(gift_icon);
        builder.setGiftNum(gift_num == null ? "0" : gift_num);
        builder.setSortNum(sort_num);
//        builder.setComp(comp);
        builder.setGiftId(giftId);
        builder.setSendUserName(sendUserName == null ? "" : sendUserName);
        builder.setSendUserHead(sendUserHead == null ? "" : sendUserHead);
        builder.setSendNum(sendNum);
        builder.setRemainNum(remainNum);
        builder.setState(state);
        builder.setSendUserMicUrl(sendUserMicUrl == null ? "" : sendUserName);
        builder.setAid(aid == null ? "" : aid);
        builder.setSendUserUid(sendUserUid == null ? "" : sendUserUid);
        return builder.build().toByteArray();
    }


    public String getGift_num() {
        return gift_num;
    }

    public void setGift_num(String gift_num) {
        this.gift_num = gift_num;
    }

    public long getSort_num() {
        return sort_num;
    }

    public void setSort_num(long sort_num) {
        this.sort_num = sort_num;
    }


    public Integer getGiftId() {
        return giftId;
    }

    public void setGiftId(Integer giftId) {
        this.giftId = giftId;
    }

    public String getSendUserName() {
        return sendUserName;
    }

    public void setSendUserName(String sendUserName) {
        this.sendUserName = sendUserName;
    }

    public String getSendUserHead() {
        return sendUserHead;
    }

    public void setSendUserHead(String sendUserHead) {
        this.sendUserHead = sendUserHead;
    }

    public Long getSendNum() {
        return sendNum;
    }

    public void setSendNum(Long sendNum) {
        this.sendNum = sendNum;
    }

    public Long getRemainNum() {
        return remainNum;
    }

    public void setRemainNum(Long remainNum) {
        this.remainNum = remainNum;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getSendUserMicUrl() {
        return sendUserMicUrl;
    }

    public void setSendUserMicUrl(String sendUserMicUrl) {
        this.sendUserMicUrl = sendUserMicUrl;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getSendUserUid() {
        return sendUserUid;
    }

    public void setSendUserUid(String sendUserUid) {
        this.sendUserUid = sendUserUid;
    }

    @Override
    public String toString() {
        return "GiftNamingMsg{" +
                "gift_num='" + gift_num + '\'' +
                ", sort_num=" + sort_num +
                ", giftId=" + giftId +
                ", sendUserName='" + sendUserName + '\'' +
                ", sendUserHead='" + sendUserHead + '\'' +
                ", sendNum=" + sendNum +
                ", remainNum=" + remainNum +
                ", state=" + state +
                ", sendUserMicUrl='" + sendUserMicUrl + '\'' +
                ", aid='" + aid + '\'' +
                ", sendUserUid='" + sendUserUid + '\'' +
                '}';
    }
}
