package com.quhong.msg.obj;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.msg.IProto;
import com.quhong.proto.YoustarProtoUname;

public class RankInfoObject implements IProto<YoustarProtoUname.RankInfo> {

    private int rank; //排行名次 从1开始
    private String name;
    private String head;
    private int rid; //0 用户 1 房间
    private RidInfoObject ridInfo; // 靓号
    private String room_id;// 房间榜才有，房间id
    private int tagType; // 1 发送榜 2 接收榜 3 房间榜

    public void fillFrom(JSONObject object) {
        this.rank = object.getIntValue("rank");
        this.name = object.getString("name");
        this.head = object.getString("head");
        this.rid = object.getIntValue("rid");
        this.room_id = object.getString("room_id");
        this.tagType = object.getIntValue("tagType");
    }

    @Override
    public void doFromBody(YoustarProtoUname.RankInfo proto) {
        this.rank = proto.getRank();
        this.name = proto.getName();
        this.head = proto.getHead();
        this.rid = proto.getRid();
        this.room_id = proto.getRoomId();
        this.tagType = proto.getTagType();
        this.ridInfo = new RidInfoObject();
        this.ridInfo.doFromBody(proto.getRidInfo());
    }

    @Override
    public YoustarProtoUname.RankInfo.Builder doToBody() {
        YoustarProtoUname.RankInfo.Builder builder = YoustarProtoUname.RankInfo.newBuilder();
        builder.setRank(rank);
        builder.setName(name == null ? "" : name);
        builder.setHead(head == null ? "" : head);
        builder.setRid(rid);
        builder.setRoomId(room_id == null ? "" : room_id);
        builder.setTagType(tagType);
        if (this.ridInfo != null) {
            builder.setRidInfo(this.ridInfo.doToBody());
        }
        return builder;
    }

    public int getRank() {
        return rank;
    }

    public void setRank(int rank) {
        this.rank = rank;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public String getRoom_id() {
        return room_id;
    }

    public void setRoom_id(String room_id) {
        this.room_id = room_id;
    }

    public int getTagType() {
        return tagType;
    }

    public void setTagType(int tagType) {
        this.tagType = tagType;
    }

    public RidInfoObject getRidInfo() {
        return ridInfo;
    }

    public void setRidInfo(RidInfoObject ridInfo) {
        this.ridInfo = ridInfo;
    }
}
