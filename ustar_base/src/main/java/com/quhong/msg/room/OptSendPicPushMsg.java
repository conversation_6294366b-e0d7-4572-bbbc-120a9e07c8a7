package com.quhong.msg.room;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.msg.obj.UserInfoObject;
import com.quhong.proto.YoustarProtoRoom;

@Message(cmd = Cmd.OPT_SEND_PIC_PUSH)
public class OptSendPicPushMsg extends MarsServerMsg {
    private int opt; //1 关闭   1打开
    private UserInfoObject opt_user;

    @Override
    public void fillFrom(JSONObject object){
        this.opt = object.getIntValue("opt");
        JSONObject userObj = object.getJSONObject("opt_user");
        if(userObj != null){
            opt_user = new UserInfoObject();
            opt_user.fillFrom(userObj);
        }
    }

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoRoom.OptSendPicMessage msg = YoustarProtoRoom.OptSendPicMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.opt = msg.getOpt();
        if(msg.getOptUser() != null){
            this.opt_user = new UserInfoObject();
            this.opt_user.doFromBody(msg.getOptUser());
        }
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoRoom.OptSendPicMessage.Builder builder = YoustarProtoRoom.OptSendPicMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setOpt(this.opt);
        if(this.opt_user != null){
            builder.setOptUser(this.opt_user.doToBody());
        }
        return builder.build().toByteArray();
    }
}
