package com.quhong.msg.room;

import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.msg.obj.RoomMicInfoObject;
import com.quhong.proto.YoustarProtoRoom;
import com.quhong.proto.YoustarProtoRoomMicInfo;

import java.util.ArrayList;
import java.util.List;

@Message(cmd = Cmd.ROOM_MIC_CHANGE)
public class RoomMicChangePushMsg extends MarsServerMsg {
    private int version;
    private List<RoomMicInfoObject> list;

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoRoom.RoomMicInfoChangeMessage msg = YoustarProtoRoom.RoomMicInfoChangeMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.version = msg.getVersion();
        list = new ArrayList<>();
        for (YoustarProtoRoomMicInfo.RoomMicInfo micInfo : msg.getListList()) {
            RoomMicInfoObject object = new RoomMicInfoObject();
            object.doFromBody(micInfo);
            list.add(object);
        }
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoRoom.RoomMicInfoChangeMessage.Builder builder = YoustarProtoRoom.RoomMicInfoChangeMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        if (this.list != null) {
            for (RoomMicInfoObject infoObject : this.list) {
                builder.addList(infoObject.doToBody());
            }
        }
        builder.setVersion(version);
        return builder.build().toByteArray();
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public List<RoomMicInfoObject> getList() {
        return list;
    }

    public void setList(List<RoomMicInfoObject> list) {
        this.list = list;
    }
}
