package com.quhong.msg.room;

import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.proto.YoustarProtoRoom;

@Message(cmd = Cmd.ENTER_ROOM_REQ)
public class EnterRoomMsg extends MarsServerMsg {
    protected String content = "";

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoRoom.EnterRoomMessage msg = YoustarProtoRoom.EnterRoomMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.content = msg.getContent();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoRoom.EnterRoomMessage.Builder builder = YoustarProtoRoom.EnterRoomMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setContent(content);
        return builder.build().toByteArray();
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
