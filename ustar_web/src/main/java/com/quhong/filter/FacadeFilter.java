package com.quhong.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.SpringUtils;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.enums.LogType;
import com.quhong.enums.SLangType;
import com.quhong.monitor.MonitorChecker;
import com.quhong.utils.AESUtils;
import com.quhong.utils.RequestUtils;
import io.netty.util.CharsetUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.http.MediaType;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StreamUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;


/**
 * 提供参数解密、token校验
 */
public class FacadeFilter implements Filter {

    private static final Logger logger = LoggerFactory.getLogger(FacadeFilter.class);
    private static final Logger msgLogger = LoggerFactory.getLogger(LogType.MESSAGE_LOG);
    private MonitorChecker monitorChecker;
    private BasePlayerRedis playerRedis;
    private static final long DEFAULT_WARNING_TIME = 2000;
    public static final String CLIENT_LANG = "client_lang";
    public static AntPathMatcher matcher = new AntPathMatcher();
    /**
     * 该List的路径不会进行Post参数解密和token校验，如第三方回调等，健康检查...
     */
    public List<String> excludePaths = new ArrayList<>();

    /**
     * 该List的路径不会进行token校验，但会进行参数解密，如登录接口...
     */
    public List<String> anonymousPath = new ArrayList<>();

    /**
     * 接口告警时间
     */
    protected Map<String, Long> requestWarnDurationMap;

    public boolean isExcludePaths(String path) {
        for (String pattern : excludePaths) {
            if (matcher.match(pattern, path)) {
                return true;
            }
        }
        return false;
    }

    public boolean isAnonymousPath(String path) {
        for (String pattern : anonymousPath) {
            if (matcher.match(pattern, path)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        if (null == playerRedis) {
            playerRedis = SpringUtils.getBean(BasePlayerRedis.class);
        }
        this.monitorChecker = new MonitorChecker("request_too_long");
        excludePaths = new ArrayList<>(excludePaths);
        anonymousPath = new ArrayList<>(anonymousPath);
        excludePaths.add("/**/healthy");
        excludePaths.add("/**/active");
        excludePaths.add("/**/system/health_check");
        excludePaths.add("/**/inner/**");
        anonymousPath.add("/**/visitor/**");
    }

    private JSONObject getBody(HttpServletRequest request) {
        try {
            String body = new String(StreamUtils.copyToByteArray(request.getInputStream()), StandardCharsets.UTF_8);
            int vc = RequestUtils.getParamInt(request, "new_versioncode");
            int debug = RequestUtils.getParamInt(request, "debug");
            // 测试服app历史问题
            JSONObject jsonObject = JSON.parseObject(body);
            if (1 == debug && ServerConfig.isNotProduct()) {
                jsonObject.put("debug", 1);
                return jsonObject;
            }
            String encryptedBody = jsonObject.getString("send_data");
            if (!ObjectUtils.isEmpty(encryptedBody)) {
                // app发送的数据解密
                return JSON.parseObject(AESUtils.decryptServerData(encryptedBody, vc));
            } else {
                // h5请求，参数不加密
                return jsonObject;
            }
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        String uri = request.getRequestURI();
        // 不解密不验证token，直接放行
        if (isExcludePaths(uri)) {
            MDC.put(RequestUtils.REQUEST_ID, genRequestId("visitor"));
            chain.doFilter(request, servletResponse);
            return;
        }
        // 和getParameter互斥，body只能读取一次问题
        JSONObject body = getBody(request);
        // H5请求uid、token、slang参数在url中
        String uid = request.getParameter("uid");
        if (!ObjectUtils.isEmpty(uid)) {
            // H5请求
            response.setHeader("Access-Control-Allow-Methods", "GET,POST,OPTIONS,PUT,DELETE");
            response.setHeader("Access-Control-Max-Age", "3600");
            response.setHeader("Access-control-Allow-Origin", request.getHeader("Origin"));
            response.setHeader("Access-Control-Allow-Headers", request.getHeader("Access-Control-Request-Headers"));
            DecryptRequestWrapper requestWrapper = new DecryptRequestWrapper(request, body, true);
            String token = request.getParameter("token");
            if (notVerifyToken(request, response, uid, token, uri)) {
                return;
            }
            MDC.put(RequestUtils.REQUEST_ID, genRequestId(uid));
            msgLogger.info("h5 request pre handler. ip={} path={}", RequestUtils.getIpAddress(request), request.getRequestURI());
            setLocale(request, parseSlang(request));
            long startTime = System.currentTimeMillis();
            chain.doFilter(requestWrapper, servletResponse);
            checkRequestTime(System.currentTimeMillis() - startTime, uri, null);
            MDC.clear();
        } else {
            // app请求，所有接口都是POST，body不能为空
            if (null == body) {
                throwFilterError(response, HttpCode.PARAM_ERROR);
                return;
            }
            DecryptRequestWrapper requestWrapper = new DecryptRequestWrapper(request, body, false);
            uid = requestWrapper.getJsonBody().getString("uid");
            String token = requestWrapper.getJsonBody().getString("token");
            if (notVerifyToken(request, response, uid, token, uri)) {
                return;
            }
            MDC.put(RequestUtils.REQUEST_ID, genRequestId(uid));
            msgLogger.info("request pre handler. ip={} path={}", RequestUtils.getIpAddress(request), request.getRequestURI());
            setLocale(request, requestWrapper.getJsonBody().getIntValue("slang"));
            long startTime = System.currentTimeMillis();
            chain.doFilter(requestWrapper, servletResponse);
            checkRequestTime(System.currentTimeMillis() - startTime, uri, requestWrapper.getJsonBody());
            MDC.clear();
        }
    }

    private boolean notVerifyToken(HttpServletRequest request, HttpServletResponse response, String uid, String token, String uri) {
        // 测试服方便测试
        if (ServerConfig.isNotProduct() && 1 == RequestUtils.getParamInt(request, "debug")) {
            return false;
        }
        // 无需验证token
        if (isAnonymousPath(uri)) {
            return false;
        }
        if (ObjectUtils.isEmpty(uid) || ObjectUtils.isEmpty(token)) {
            logger.info("verify token param empty. uid={} token={} uri={}", uid, token, uri);
            throwFilterError(response, HttpCode.PARAM_ERROR);
            return true;
        }
        String loginToken = getLoginToken(uid);
        if (!token.equals(loginToken)) {
            logger.info("verify token failed. token={} redisToken={} uri={} uid={}", token, loginToken, uri, uid);
            throwFilterError(response, HttpCode.SESSION_INVALID);
            return true;
        }
        return false;
    }

    private void checkRequestTime(long requestTime, String uri, JSONObject reqBody) {
        Long limitRequestTime = requestWarnDurationMap.get(uri);
        if (limitRequestTime == null) {
            limitRequestTime = DEFAULT_WARNING_TIME;
        }
        if (requestTime >= limitRequestTime) {
            if (reqBody != null) {
                String vname = reqBody.getString("vname");
                if (null != vname && vname.contains("dev")) {
                    return;
                }
            }
            String desc = "request: " + uri
                    + ": use too long time = " + requestTime
                    + ": min_time = " + limitRequestTime
                    + " [requestId]:" + MDC.get(RequestUtils.REQUEST_ID);
            monitorChecker.startWarning(desc, String.format("{req_data:%s}", null == reqBody ? "" : reqBody.toString()));
            logger.info("start warn request time too long. url={} time={}", uri, requestTime);
        }
    }

    public String genRequestId(String uid) {
        return (uid == null ? "anonymous" : uid) + "-" + UUID.randomUUID().toString().substring(0, 8);
    }

    public void setLocale(HttpServletRequest request, int slang) {
        try {
            request.setAttribute(CLIENT_LANG, slang == SLangType.ENGLISH ? "en_US" : "ar");
        } catch (Exception e) {
            logger.error("set locale error, slang={} error message={}", slang, e.getMessage(), e);
        }
    }

    private void throwFilterError(HttpServletResponse response, HttpCode httpCode) {
        try {
            HttpResult<Object> result = new HttpResult<>();
            result.setCode(httpCode.getCode());
            result.setMsg(httpCode.getMsg());
            response.setCharacterEncoding(String.valueOf(CharsetUtil.UTF_8));
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            response.setStatus(200);
            response.getWriter().append(JSONObject.toJSONString(result));
        } catch (IOException e) {
            logger.error("response error. {}", e.getMessage(), e);
        }
    }

    private int parseSlang(HttpServletRequest request) {
        int slang = 1;
        try {
            String slangStr = request.getParameter("slang");
            if (null != slangStr) {
                slang = Integer.parseInt(slangStr);
            }
        } catch (Exception e) {
            logger.error("parse slang error, slang={}", request.getParameter("slang"), e);
        }
        return slang;
    }

    private String getLoginToken(String uid) {
        return playerRedis.getToken(uid);
    }

    public void setExcludePaths(List<String> excludePaths) {
        this.excludePaths = excludePaths;
    }

    public void setAnonymousPath(List<String> anonymousPath) {
        this.anonymousPath = anonymousPath;
    }

    public void setRequestWarnDurationMap(Map<String, Long> requestWarnDurationMap) {
        this.requestWarnDurationMap = requestWarnDurationMap;
    }
}
