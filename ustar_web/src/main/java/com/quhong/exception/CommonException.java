package com.quhong.exception;

import com.quhong.enums.HttpCode;

public class CommonException extends RuntimeException {

    protected HttpCode httpCode;
    protected Object[] args;
    protected Object data;

    /**
     * 默认为 server error
     */
    public CommonException() {
        this.httpCode = HttpCode.SERVER_ERROR;
    }

    /**
     * 抛出正常的业务处理
     */
    public CommonException(Object data) {
        this.httpCode = HttpCode.SUCCESS;
        this.data = data;
    }

    public CommonException(HttpCode httpCode) {
        this.httpCode = httpCode;
    }

    /**
     * 传入带参数的异常
     *
     * @param httpCode httpCode
     * @param args     国际化额外参数，对应国际化内容中的{0}、{1}、{2}...
     */
    public CommonException(HttpCode httpCode, Object... args) {
        this.httpCode = httpCode;
        this.args = args;
    }

    /**
     * 自定义异常异常
     *
     * @param code 错误编码
     * @param msg  错误信息
     */
    public CommonException(int code, String msg) {
        httpCode = new HttpCode();
        this.httpCode.setCode(code);
        this.httpCode.setMsg(msg);
    }

    public HttpCode getHttpCode() {
        return httpCode;
    }

    public void setHttpCode(HttpCode httpCode) {
        this.httpCode = httpCode;
    }

    public Object[] getArgs() {
        return args;
    }

    public void setArgs(Object[] args) {
        this.args = args;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
