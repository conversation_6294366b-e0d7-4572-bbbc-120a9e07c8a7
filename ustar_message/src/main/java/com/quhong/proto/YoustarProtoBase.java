// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: base.proto

package com.quhong.proto;

public final class YoustarProtoBase {
  private YoustarProtoBase() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface MsgHeaderOrBuilder extends
      // @@protoc_insertion_point(interface_extends:MsgHeader)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *消息id，用于消息确认和去重
     * </pre>
     *
     * <code>int64 msgId = 1;</code>
     */
    long getMsgId();

    /**
     * <pre>
     *发送者uid
     * </pre>
     *
     * <code>string fromUid = 2;</code>
     */
    java.lang.String getFromUid();
    /**
     * <pre>
     *发送者uid
     * </pre>
     *
     * <code>string fromUid = 2;</code>
     */
    com.google.protobuf.ByteString
        getFromUidBytes();

    /**
     * <pre>
     *token
     * </pre>
     *
     * <code>string token = 3;</code>
     */
    java.lang.String getToken();
    /**
     * <pre>
     *token
     * </pre>
     *
     * <code>string token = 3;</code>
     */
    com.google.protobuf.ByteString
        getTokenBytes();

    /**
     * <pre>
     *房间ID
     * </pre>
     *
     * <code>string roomID = 4;</code>
     */
    java.lang.String getRoomID();
    /**
     * <pre>
     *房间ID
     * </pre>
     *
     * <code>string roomID = 4;</code>
     */
    com.google.protobuf.ByteString
        getRoomIDBytes();

    /**
     * <pre>
     *是否回包
     * </pre>
     *
     * <code>bool responseAck = 5;</code>
     */
    boolean getResponseAck();

    /**
     * <pre>
     *版本
     * </pre>
     *
     * <code>string versionName = 6;</code>
     */
    java.lang.String getVersionName();
    /**
     * <pre>
     *版本
     * </pre>
     *
     * <code>string versionName = 6;</code>
     */
    com.google.protobuf.ByteString
        getVersionNameBytes();

    /**
     * <pre>
     *客户端请求请求时间搓（毫秒）
     * </pre>
     *
     * <code>int64 requestTime = 7;</code>
     */
    long getRequestTime();
  }
  /**
   * Protobuf type {@code MsgHeader}
   */
  public  static final class MsgHeader extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:MsgHeader)
      MsgHeaderOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MsgHeader.newBuilder() to construct.
    private MsgHeader(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MsgHeader() {
      msgId_ = 0L;
      fromUid_ = "";
      token_ = "";
      roomID_ = "";
      responseAck_ = false;
      versionName_ = "";
      requestTime_ = 0L;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MsgHeader(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              msgId_ = input.readInt64();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              fromUid_ = s;
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              token_ = s;
              break;
            }
            case 34: {
              java.lang.String s = input.readStringRequireUtf8();

              roomID_ = s;
              break;
            }
            case 40: {

              responseAck_ = input.readBool();
              break;
            }
            case 50: {
              java.lang.String s = input.readStringRequireUtf8();

              versionName_ = s;
              break;
            }
            case 56: {

              requestTime_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.quhong.proto.YoustarProtoBase.internal_static_MsgHeader_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.quhong.proto.YoustarProtoBase.internal_static_MsgHeader_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.quhong.proto.YoustarProtoBase.MsgHeader.class, com.quhong.proto.YoustarProtoBase.MsgHeader.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private long msgId_;
    /**
     * <pre>
     *消息id，用于消息确认和去重
     * </pre>
     *
     * <code>int64 msgId = 1;</code>
     */
    public long getMsgId() {
      return msgId_;
    }

    public static final int FROMUID_FIELD_NUMBER = 2;
    private volatile java.lang.Object fromUid_;
    /**
     * <pre>
     *发送者uid
     * </pre>
     *
     * <code>string fromUid = 2;</code>
     */
    public java.lang.String getFromUid() {
      java.lang.Object ref = fromUid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        fromUid_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *发送者uid
     * </pre>
     *
     * <code>string fromUid = 2;</code>
     */
    public com.google.protobuf.ByteString
        getFromUidBytes() {
      java.lang.Object ref = fromUid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fromUid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TOKEN_FIELD_NUMBER = 3;
    private volatile java.lang.Object token_;
    /**
     * <pre>
     *token
     * </pre>
     *
     * <code>string token = 3;</code>
     */
    public java.lang.String getToken() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        token_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *token
     * </pre>
     *
     * <code>string token = 3;</code>
     */
    public com.google.protobuf.ByteString
        getTokenBytes() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        token_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ROOMID_FIELD_NUMBER = 4;
    private volatile java.lang.Object roomID_;
    /**
     * <pre>
     *房间ID
     * </pre>
     *
     * <code>string roomID = 4;</code>
     */
    public java.lang.String getRoomID() {
      java.lang.Object ref = roomID_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        roomID_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *房间ID
     * </pre>
     *
     * <code>string roomID = 4;</code>
     */
    public com.google.protobuf.ByteString
        getRoomIDBytes() {
      java.lang.Object ref = roomID_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        roomID_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int RESPONSEACK_FIELD_NUMBER = 5;
    private boolean responseAck_;
    /**
     * <pre>
     *是否回包
     * </pre>
     *
     * <code>bool responseAck = 5;</code>
     */
    public boolean getResponseAck() {
      return responseAck_;
    }

    public static final int VERSIONNAME_FIELD_NUMBER = 6;
    private volatile java.lang.Object versionName_;
    /**
     * <pre>
     *版本
     * </pre>
     *
     * <code>string versionName = 6;</code>
     */
    public java.lang.String getVersionName() {
      java.lang.Object ref = versionName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        versionName_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *版本
     * </pre>
     *
     * <code>string versionName = 6;</code>
     */
    public com.google.protobuf.ByteString
        getVersionNameBytes() {
      java.lang.Object ref = versionName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        versionName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int REQUESTTIME_FIELD_NUMBER = 7;
    private long requestTime_;
    /**
     * <pre>
     *客户端请求请求时间搓（毫秒）
     * </pre>
     *
     * <code>int64 requestTime = 7;</code>
     */
    public long getRequestTime() {
      return requestTime_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgId_ != 0L) {
        output.writeInt64(1, msgId_);
      }
      if (!getFromUidBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, fromUid_);
      }
      if (!getTokenBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, token_);
      }
      if (!getRoomIDBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, roomID_);
      }
      if (responseAck_ != false) {
        output.writeBool(5, responseAck_);
      }
      if (!getVersionNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, versionName_);
      }
      if (requestTime_ != 0L) {
        output.writeInt64(7, requestTime_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, msgId_);
      }
      if (!getFromUidBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, fromUid_);
      }
      if (!getTokenBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, token_);
      }
      if (!getRoomIDBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, roomID_);
      }
      if (responseAck_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(5, responseAck_);
      }
      if (!getVersionNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, versionName_);
      }
      if (requestTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(7, requestTime_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.quhong.proto.YoustarProtoBase.MsgHeader)) {
        return super.equals(obj);
      }
      com.quhong.proto.YoustarProtoBase.MsgHeader other = (com.quhong.proto.YoustarProtoBase.MsgHeader) obj;

      boolean result = true;
      result = result && (getMsgId()
          == other.getMsgId());
      result = result && getFromUid()
          .equals(other.getFromUid());
      result = result && getToken()
          .equals(other.getToken());
      result = result && getRoomID()
          .equals(other.getRoomID());
      result = result && (getResponseAck()
          == other.getResponseAck());
      result = result && getVersionName()
          .equals(other.getVersionName());
      result = result && (getRequestTime()
          == other.getRequestTime());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getMsgId());
      hash = (37 * hash) + FROMUID_FIELD_NUMBER;
      hash = (53 * hash) + getFromUid().hashCode();
      hash = (37 * hash) + TOKEN_FIELD_NUMBER;
      hash = (53 * hash) + getToken().hashCode();
      hash = (37 * hash) + ROOMID_FIELD_NUMBER;
      hash = (53 * hash) + getRoomID().hashCode();
      hash = (37 * hash) + RESPONSEACK_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getResponseAck());
      hash = (37 * hash) + VERSIONNAME_FIELD_NUMBER;
      hash = (53 * hash) + getVersionName().hashCode();
      hash = (37 * hash) + REQUESTTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRequestTime());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.quhong.proto.YoustarProtoBase.MsgHeader parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.MsgHeader parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.MsgHeader parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.MsgHeader parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.MsgHeader parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.MsgHeader parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.MsgHeader parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.MsgHeader parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.MsgHeader parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.MsgHeader parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.MsgHeader parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.MsgHeader parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.quhong.proto.YoustarProtoBase.MsgHeader prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code MsgHeader}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:MsgHeader)
        com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.quhong.proto.YoustarProtoBase.internal_static_MsgHeader_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.quhong.proto.YoustarProtoBase.internal_static_MsgHeader_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.quhong.proto.YoustarProtoBase.MsgHeader.class, com.quhong.proto.YoustarProtoBase.MsgHeader.Builder.class);
      }

      // Construct using com.quhong.proto.YoustarProtoBase.MsgHeader.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        msgId_ = 0L;

        fromUid_ = "";

        token_ = "";

        roomID_ = "";

        responseAck_ = false;

        versionName_ = "";

        requestTime_ = 0L;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.quhong.proto.YoustarProtoBase.internal_static_MsgHeader_descriptor;
      }

      public com.quhong.proto.YoustarProtoBase.MsgHeader getDefaultInstanceForType() {
        return com.quhong.proto.YoustarProtoBase.MsgHeader.getDefaultInstance();
      }

      public com.quhong.proto.YoustarProtoBase.MsgHeader build() {
        com.quhong.proto.YoustarProtoBase.MsgHeader result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public com.quhong.proto.YoustarProtoBase.MsgHeader buildPartial() {
        com.quhong.proto.YoustarProtoBase.MsgHeader result = new com.quhong.proto.YoustarProtoBase.MsgHeader(this);
        result.msgId_ = msgId_;
        result.fromUid_ = fromUid_;
        result.token_ = token_;
        result.roomID_ = roomID_;
        result.responseAck_ = responseAck_;
        result.versionName_ = versionName_;
        result.requestTime_ = requestTime_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.quhong.proto.YoustarProtoBase.MsgHeader) {
          return mergeFrom((com.quhong.proto.YoustarProtoBase.MsgHeader)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.quhong.proto.YoustarProtoBase.MsgHeader other) {
        if (other == com.quhong.proto.YoustarProtoBase.MsgHeader.getDefaultInstance()) return this;
        if (other.getMsgId() != 0L) {
          setMsgId(other.getMsgId());
        }
        if (!other.getFromUid().isEmpty()) {
          fromUid_ = other.fromUid_;
          onChanged();
        }
        if (!other.getToken().isEmpty()) {
          token_ = other.token_;
          onChanged();
        }
        if (!other.getRoomID().isEmpty()) {
          roomID_ = other.roomID_;
          onChanged();
        }
        if (other.getResponseAck() != false) {
          setResponseAck(other.getResponseAck());
        }
        if (!other.getVersionName().isEmpty()) {
          versionName_ = other.versionName_;
          onChanged();
        }
        if (other.getRequestTime() != 0L) {
          setRequestTime(other.getRequestTime());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.quhong.proto.YoustarProtoBase.MsgHeader parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.quhong.proto.YoustarProtoBase.MsgHeader) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long msgId_ ;
      /**
       * <pre>
       *消息id，用于消息确认和去重
       * </pre>
       *
       * <code>int64 msgId = 1;</code>
       */
      public long getMsgId() {
        return msgId_;
      }
      /**
       * <pre>
       *消息id，用于消息确认和去重
       * </pre>
       *
       * <code>int64 msgId = 1;</code>
       */
      public Builder setMsgId(long value) {
        
        msgId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *消息id，用于消息确认和去重
       * </pre>
       *
       * <code>int64 msgId = 1;</code>
       */
      public Builder clearMsgId() {
        
        msgId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object fromUid_ = "";
      /**
       * <pre>
       *发送者uid
       * </pre>
       *
       * <code>string fromUid = 2;</code>
       */
      public java.lang.String getFromUid() {
        java.lang.Object ref = fromUid_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          fromUid_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *发送者uid
       * </pre>
       *
       * <code>string fromUid = 2;</code>
       */
      public com.google.protobuf.ByteString
          getFromUidBytes() {
        java.lang.Object ref = fromUid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fromUid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *发送者uid
       * </pre>
       *
       * <code>string fromUid = 2;</code>
       */
      public Builder setFromUid(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        fromUid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *发送者uid
       * </pre>
       *
       * <code>string fromUid = 2;</code>
       */
      public Builder clearFromUid() {
        
        fromUid_ = getDefaultInstance().getFromUid();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *发送者uid
       * </pre>
       *
       * <code>string fromUid = 2;</code>
       */
      public Builder setFromUidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        fromUid_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object token_ = "";
      /**
       * <pre>
       *token
       * </pre>
       *
       * <code>string token = 3;</code>
       */
      public java.lang.String getToken() {
        java.lang.Object ref = token_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          token_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *token
       * </pre>
       *
       * <code>string token = 3;</code>
       */
      public com.google.protobuf.ByteString
          getTokenBytes() {
        java.lang.Object ref = token_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          token_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *token
       * </pre>
       *
       * <code>string token = 3;</code>
       */
      public Builder setToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        token_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *token
       * </pre>
       *
       * <code>string token = 3;</code>
       */
      public Builder clearToken() {
        
        token_ = getDefaultInstance().getToken();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *token
       * </pre>
       *
       * <code>string token = 3;</code>
       */
      public Builder setTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        token_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object roomID_ = "";
      /**
       * <pre>
       *房间ID
       * </pre>
       *
       * <code>string roomID = 4;</code>
       */
      public java.lang.String getRoomID() {
        java.lang.Object ref = roomID_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          roomID_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *房间ID
       * </pre>
       *
       * <code>string roomID = 4;</code>
       */
      public com.google.protobuf.ByteString
          getRoomIDBytes() {
        java.lang.Object ref = roomID_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          roomID_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *房间ID
       * </pre>
       *
       * <code>string roomID = 4;</code>
       */
      public Builder setRoomID(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        roomID_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *房间ID
       * </pre>
       *
       * <code>string roomID = 4;</code>
       */
      public Builder clearRoomID() {
        
        roomID_ = getDefaultInstance().getRoomID();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *房间ID
       * </pre>
       *
       * <code>string roomID = 4;</code>
       */
      public Builder setRoomIDBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        roomID_ = value;
        onChanged();
        return this;
      }

      private boolean responseAck_ ;
      /**
       * <pre>
       *是否回包
       * </pre>
       *
       * <code>bool responseAck = 5;</code>
       */
      public boolean getResponseAck() {
        return responseAck_;
      }
      /**
       * <pre>
       *是否回包
       * </pre>
       *
       * <code>bool responseAck = 5;</code>
       */
      public Builder setResponseAck(boolean value) {
        
        responseAck_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *是否回包
       * </pre>
       *
       * <code>bool responseAck = 5;</code>
       */
      public Builder clearResponseAck() {
        
        responseAck_ = false;
        onChanged();
        return this;
      }

      private java.lang.Object versionName_ = "";
      /**
       * <pre>
       *版本
       * </pre>
       *
       * <code>string versionName = 6;</code>
       */
      public java.lang.String getVersionName() {
        java.lang.Object ref = versionName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          versionName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *版本
       * </pre>
       *
       * <code>string versionName = 6;</code>
       */
      public com.google.protobuf.ByteString
          getVersionNameBytes() {
        java.lang.Object ref = versionName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          versionName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *版本
       * </pre>
       *
       * <code>string versionName = 6;</code>
       */
      public Builder setVersionName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        versionName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *版本
       * </pre>
       *
       * <code>string versionName = 6;</code>
       */
      public Builder clearVersionName() {
        
        versionName_ = getDefaultInstance().getVersionName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *版本
       * </pre>
       *
       * <code>string versionName = 6;</code>
       */
      public Builder setVersionNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        versionName_ = value;
        onChanged();
        return this;
      }

      private long requestTime_ ;
      /**
       * <pre>
       *客户端请求请求时间搓（毫秒）
       * </pre>
       *
       * <code>int64 requestTime = 7;</code>
       */
      public long getRequestTime() {
        return requestTime_;
      }
      /**
       * <pre>
       *客户端请求请求时间搓（毫秒）
       * </pre>
       *
       * <code>int64 requestTime = 7;</code>
       */
      public Builder setRequestTime(long value) {
        
        requestTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *客户端请求请求时间搓（毫秒）
       * </pre>
       *
       * <code>int64 requestTime = 7;</code>
       */
      public Builder clearRequestTime() {
        
        requestTime_ = 0L;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:MsgHeader)
    }

    // @@protoc_insertion_point(class_scope:MsgHeader)
    private static final com.quhong.proto.YoustarProtoBase.MsgHeader DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.quhong.proto.YoustarProtoBase.MsgHeader();
    }

    public static com.quhong.proto.YoustarProtoBase.MsgHeader getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MsgHeader>
        PARSER = new com.google.protobuf.AbstractParser<MsgHeader>() {
      public MsgHeader parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new MsgHeader(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MsgHeader> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MsgHeader> getParserForType() {
      return PARSER;
    }

    public com.quhong.proto.YoustarProtoBase.MsgHeader getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GateMsgOrBuilder extends
      // @@protoc_insertion_point(interface_extends:GateMsg)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.MsgHeader header = 1;</code>
     */
    boolean hasHeader();
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    com.quhong.proto.YoustarProtoBase.MsgHeader getHeader();
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder getHeaderOrBuilder();
  }
  /**
   * Protobuf type {@code GateMsg}
   */
  public  static final class GateMsg extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:GateMsg)
      GateMsgOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GateMsg.newBuilder() to construct.
    private GateMsg(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GateMsg() {
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GateMsg(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              com.quhong.proto.YoustarProtoBase.MsgHeader.Builder subBuilder = null;
              if (header_ != null) {
                subBuilder = header_.toBuilder();
              }
              header_ = input.readMessage(com.quhong.proto.YoustarProtoBase.MsgHeader.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(header_);
                header_ = subBuilder.buildPartial();
              }

              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.quhong.proto.YoustarProtoBase.internal_static_GateMsg_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.quhong.proto.YoustarProtoBase.internal_static_GateMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.quhong.proto.YoustarProtoBase.GateMsg.class, com.quhong.proto.YoustarProtoBase.GateMsg.Builder.class);
    }

    public static final int HEADER_FIELD_NUMBER = 1;
    private com.quhong.proto.YoustarProtoBase.MsgHeader header_;
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    public boolean hasHeader() {
      return header_ != null;
    }
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    public com.quhong.proto.YoustarProtoBase.MsgHeader getHeader() {
      return header_ == null ? com.quhong.proto.YoustarProtoBase.MsgHeader.getDefaultInstance() : header_;
    }
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    public com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder getHeaderOrBuilder() {
      return getHeader();
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (header_ != null) {
        output.writeMessage(1, getHeader());
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (header_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getHeader());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.quhong.proto.YoustarProtoBase.GateMsg)) {
        return super.equals(obj);
      }
      com.quhong.proto.YoustarProtoBase.GateMsg other = (com.quhong.proto.YoustarProtoBase.GateMsg) obj;

      boolean result = true;
      result = result && (hasHeader() == other.hasHeader());
      if (hasHeader()) {
        result = result && getHeader()
            .equals(other.getHeader());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeader()) {
        hash = (37 * hash) + HEADER_FIELD_NUMBER;
        hash = (53 * hash) + getHeader().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.quhong.proto.YoustarProtoBase.GateMsg parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.GateMsg parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.GateMsg parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.GateMsg parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.GateMsg parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.GateMsg parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.GateMsg parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.GateMsg parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.GateMsg parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.GateMsg parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.GateMsg parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.GateMsg parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.quhong.proto.YoustarProtoBase.GateMsg prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code GateMsg}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:GateMsg)
        com.quhong.proto.YoustarProtoBase.GateMsgOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.quhong.proto.YoustarProtoBase.internal_static_GateMsg_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.quhong.proto.YoustarProtoBase.internal_static_GateMsg_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.quhong.proto.YoustarProtoBase.GateMsg.class, com.quhong.proto.YoustarProtoBase.GateMsg.Builder.class);
      }

      // Construct using com.quhong.proto.YoustarProtoBase.GateMsg.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        if (headerBuilder_ == null) {
          header_ = null;
        } else {
          header_ = null;
          headerBuilder_ = null;
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.quhong.proto.YoustarProtoBase.internal_static_GateMsg_descriptor;
      }

      public com.quhong.proto.YoustarProtoBase.GateMsg getDefaultInstanceForType() {
        return com.quhong.proto.YoustarProtoBase.GateMsg.getDefaultInstance();
      }

      public com.quhong.proto.YoustarProtoBase.GateMsg build() {
        com.quhong.proto.YoustarProtoBase.GateMsg result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public com.quhong.proto.YoustarProtoBase.GateMsg buildPartial() {
        com.quhong.proto.YoustarProtoBase.GateMsg result = new com.quhong.proto.YoustarProtoBase.GateMsg(this);
        if (headerBuilder_ == null) {
          result.header_ = header_;
        } else {
          result.header_ = headerBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.quhong.proto.YoustarProtoBase.GateMsg) {
          return mergeFrom((com.quhong.proto.YoustarProtoBase.GateMsg)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.quhong.proto.YoustarProtoBase.GateMsg other) {
        if (other == com.quhong.proto.YoustarProtoBase.GateMsg.getDefaultInstance()) return this;
        if (other.hasHeader()) {
          mergeHeader(other.getHeader());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.quhong.proto.YoustarProtoBase.GateMsg parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.quhong.proto.YoustarProtoBase.GateMsg) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.quhong.proto.YoustarProtoBase.MsgHeader header_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoBase.MsgHeader, com.quhong.proto.YoustarProtoBase.MsgHeader.Builder, com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder> headerBuilder_;
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public boolean hasHeader() {
        return headerBuilder_ != null || header_ != null;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public com.quhong.proto.YoustarProtoBase.MsgHeader getHeader() {
        if (headerBuilder_ == null) {
          return header_ == null ? com.quhong.proto.YoustarProtoBase.MsgHeader.getDefaultInstance() : header_;
        } else {
          return headerBuilder_.getMessage();
        }
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder setHeader(com.quhong.proto.YoustarProtoBase.MsgHeader value) {
        if (headerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          header_ = value;
          onChanged();
        } else {
          headerBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder setHeader(
          com.quhong.proto.YoustarProtoBase.MsgHeader.Builder builderForValue) {
        if (headerBuilder_ == null) {
          header_ = builderForValue.build();
          onChanged();
        } else {
          headerBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder mergeHeader(com.quhong.proto.YoustarProtoBase.MsgHeader value) {
        if (headerBuilder_ == null) {
          if (header_ != null) {
            header_ =
              com.quhong.proto.YoustarProtoBase.MsgHeader.newBuilder(header_).mergeFrom(value).buildPartial();
          } else {
            header_ = value;
          }
          onChanged();
        } else {
          headerBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder clearHeader() {
        if (headerBuilder_ == null) {
          header_ = null;
          onChanged();
        } else {
          header_ = null;
          headerBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public com.quhong.proto.YoustarProtoBase.MsgHeader.Builder getHeaderBuilder() {
        
        onChanged();
        return getHeaderFieldBuilder().getBuilder();
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder getHeaderOrBuilder() {
        if (headerBuilder_ != null) {
          return headerBuilder_.getMessageOrBuilder();
        } else {
          return header_ == null ?
              com.quhong.proto.YoustarProtoBase.MsgHeader.getDefaultInstance() : header_;
        }
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoBase.MsgHeader, com.quhong.proto.YoustarProtoBase.MsgHeader.Builder, com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder> 
          getHeaderFieldBuilder() {
        if (headerBuilder_ == null) {
          headerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.quhong.proto.YoustarProtoBase.MsgHeader, com.quhong.proto.YoustarProtoBase.MsgHeader.Builder, com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder>(
                  getHeader(),
                  getParentForChildren(),
                  isClean());
          header_ = null;
        }
        return headerBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:GateMsg)
    }

    // @@protoc_insertion_point(class_scope:GateMsg)
    private static final com.quhong.proto.YoustarProtoBase.GateMsg DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.quhong.proto.YoustarProtoBase.GateMsg();
    }

    public static com.quhong.proto.YoustarProtoBase.GateMsg getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<GateMsg>
        PARSER = new com.google.protobuf.AbstractParser<GateMsg>() {
      public GateMsg parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new GateMsg(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GateMsg> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GateMsg> getParserForType() {
      return PARSER;
    }

    public com.quhong.proto.YoustarProtoBase.GateMsg getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResponseAckOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ResponseAck)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.MsgHeader header = 1;</code>
     */
    boolean hasHeader();
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    com.quhong.proto.YoustarProtoBase.MsgHeader getHeader();
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder getHeaderOrBuilder();

    /**
     * <pre>
     *0 成功
     * </pre>
     *
     * <code>int32 code = 2;</code>
     */
    int getCode();

    /**
     * <code>string msg = 3;</code>
     */
    java.lang.String getMsg();
    /**
     * <code>string msg = 3;</code>
     */
    com.google.protobuf.ByteString
        getMsgBytes();
  }
  /**
   * Protobuf type {@code ResponseAck}
   */
  public  static final class ResponseAck extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ResponseAck)
      ResponseAckOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResponseAck.newBuilder() to construct.
    private ResponseAck(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResponseAck() {
      code_ = 0;
      msg_ = "";
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseAck(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              com.quhong.proto.YoustarProtoBase.MsgHeader.Builder subBuilder = null;
              if (header_ != null) {
                subBuilder = header_.toBuilder();
              }
              header_ = input.readMessage(com.quhong.proto.YoustarProtoBase.MsgHeader.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(header_);
                header_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              code_ = input.readInt32();
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              msg_ = s;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.quhong.proto.YoustarProtoBase.internal_static_ResponseAck_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.quhong.proto.YoustarProtoBase.internal_static_ResponseAck_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.quhong.proto.YoustarProtoBase.ResponseAck.class, com.quhong.proto.YoustarProtoBase.ResponseAck.Builder.class);
    }

    public static final int HEADER_FIELD_NUMBER = 1;
    private com.quhong.proto.YoustarProtoBase.MsgHeader header_;
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    public boolean hasHeader() {
      return header_ != null;
    }
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    public com.quhong.proto.YoustarProtoBase.MsgHeader getHeader() {
      return header_ == null ? com.quhong.proto.YoustarProtoBase.MsgHeader.getDefaultInstance() : header_;
    }
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    public com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder getHeaderOrBuilder() {
      return getHeader();
    }

    public static final int CODE_FIELD_NUMBER = 2;
    private int code_;
    /**
     * <pre>
     *0 成功
     * </pre>
     *
     * <code>int32 code = 2;</code>
     */
    public int getCode() {
      return code_;
    }

    public static final int MSG_FIELD_NUMBER = 3;
    private volatile java.lang.Object msg_;
    /**
     * <code>string msg = 3;</code>
     */
    public java.lang.String getMsg() {
      java.lang.Object ref = msg_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        msg_ = s;
        return s;
      }
    }
    /**
     * <code>string msg = 3;</code>
     */
    public com.google.protobuf.ByteString
        getMsgBytes() {
      java.lang.Object ref = msg_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        msg_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (header_ != null) {
        output.writeMessage(1, getHeader());
      }
      if (code_ != 0) {
        output.writeInt32(2, code_);
      }
      if (!getMsgBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, msg_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (header_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getHeader());
      }
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, code_);
      }
      if (!getMsgBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, msg_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.quhong.proto.YoustarProtoBase.ResponseAck)) {
        return super.equals(obj);
      }
      com.quhong.proto.YoustarProtoBase.ResponseAck other = (com.quhong.proto.YoustarProtoBase.ResponseAck) obj;

      boolean result = true;
      result = result && (hasHeader() == other.hasHeader());
      if (hasHeader()) {
        result = result && getHeader()
            .equals(other.getHeader());
      }
      result = result && (getCode()
          == other.getCode());
      result = result && getMsg()
          .equals(other.getMsg());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeader()) {
        hash = (37 * hash) + HEADER_FIELD_NUMBER;
        hash = (53 * hash) + getHeader().hashCode();
      }
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (37 * hash) + MSG_FIELD_NUMBER;
      hash = (53 * hash) + getMsg().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.quhong.proto.YoustarProtoBase.ResponseAck parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.ResponseAck parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.ResponseAck parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.ResponseAck parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.ResponseAck parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.ResponseAck parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.ResponseAck parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.ResponseAck parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.ResponseAck parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.ResponseAck parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.ResponseAck parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.ResponseAck parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.quhong.proto.YoustarProtoBase.ResponseAck prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code ResponseAck}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ResponseAck)
        com.quhong.proto.YoustarProtoBase.ResponseAckOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.quhong.proto.YoustarProtoBase.internal_static_ResponseAck_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.quhong.proto.YoustarProtoBase.internal_static_ResponseAck_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.quhong.proto.YoustarProtoBase.ResponseAck.class, com.quhong.proto.YoustarProtoBase.ResponseAck.Builder.class);
      }

      // Construct using com.quhong.proto.YoustarProtoBase.ResponseAck.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        if (headerBuilder_ == null) {
          header_ = null;
        } else {
          header_ = null;
          headerBuilder_ = null;
        }
        code_ = 0;

        msg_ = "";

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.quhong.proto.YoustarProtoBase.internal_static_ResponseAck_descriptor;
      }

      public com.quhong.proto.YoustarProtoBase.ResponseAck getDefaultInstanceForType() {
        return com.quhong.proto.YoustarProtoBase.ResponseAck.getDefaultInstance();
      }

      public com.quhong.proto.YoustarProtoBase.ResponseAck build() {
        com.quhong.proto.YoustarProtoBase.ResponseAck result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public com.quhong.proto.YoustarProtoBase.ResponseAck buildPartial() {
        com.quhong.proto.YoustarProtoBase.ResponseAck result = new com.quhong.proto.YoustarProtoBase.ResponseAck(this);
        if (headerBuilder_ == null) {
          result.header_ = header_;
        } else {
          result.header_ = headerBuilder_.build();
        }
        result.code_ = code_;
        result.msg_ = msg_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.quhong.proto.YoustarProtoBase.ResponseAck) {
          return mergeFrom((com.quhong.proto.YoustarProtoBase.ResponseAck)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.quhong.proto.YoustarProtoBase.ResponseAck other) {
        if (other == com.quhong.proto.YoustarProtoBase.ResponseAck.getDefaultInstance()) return this;
        if (other.hasHeader()) {
          mergeHeader(other.getHeader());
        }
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (!other.getMsg().isEmpty()) {
          msg_ = other.msg_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.quhong.proto.YoustarProtoBase.ResponseAck parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.quhong.proto.YoustarProtoBase.ResponseAck) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.quhong.proto.YoustarProtoBase.MsgHeader header_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoBase.MsgHeader, com.quhong.proto.YoustarProtoBase.MsgHeader.Builder, com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder> headerBuilder_;
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public boolean hasHeader() {
        return headerBuilder_ != null || header_ != null;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public com.quhong.proto.YoustarProtoBase.MsgHeader getHeader() {
        if (headerBuilder_ == null) {
          return header_ == null ? com.quhong.proto.YoustarProtoBase.MsgHeader.getDefaultInstance() : header_;
        } else {
          return headerBuilder_.getMessage();
        }
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder setHeader(com.quhong.proto.YoustarProtoBase.MsgHeader value) {
        if (headerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          header_ = value;
          onChanged();
        } else {
          headerBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder setHeader(
          com.quhong.proto.YoustarProtoBase.MsgHeader.Builder builderForValue) {
        if (headerBuilder_ == null) {
          header_ = builderForValue.build();
          onChanged();
        } else {
          headerBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder mergeHeader(com.quhong.proto.YoustarProtoBase.MsgHeader value) {
        if (headerBuilder_ == null) {
          if (header_ != null) {
            header_ =
              com.quhong.proto.YoustarProtoBase.MsgHeader.newBuilder(header_).mergeFrom(value).buildPartial();
          } else {
            header_ = value;
          }
          onChanged();
        } else {
          headerBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder clearHeader() {
        if (headerBuilder_ == null) {
          header_ = null;
          onChanged();
        } else {
          header_ = null;
          headerBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public com.quhong.proto.YoustarProtoBase.MsgHeader.Builder getHeaderBuilder() {
        
        onChanged();
        return getHeaderFieldBuilder().getBuilder();
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder getHeaderOrBuilder() {
        if (headerBuilder_ != null) {
          return headerBuilder_.getMessageOrBuilder();
        } else {
          return header_ == null ?
              com.quhong.proto.YoustarProtoBase.MsgHeader.getDefaultInstance() : header_;
        }
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoBase.MsgHeader, com.quhong.proto.YoustarProtoBase.MsgHeader.Builder, com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder> 
          getHeaderFieldBuilder() {
        if (headerBuilder_ == null) {
          headerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.quhong.proto.YoustarProtoBase.MsgHeader, com.quhong.proto.YoustarProtoBase.MsgHeader.Builder, com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder>(
                  getHeader(),
                  getParentForChildren(),
                  isClean());
          header_ = null;
        }
        return headerBuilder_;
      }

      private int code_ ;
      /**
       * <pre>
       *0 成功
       * </pre>
       *
       * <code>int32 code = 2;</code>
       */
      public int getCode() {
        return code_;
      }
      /**
       * <pre>
       *0 成功
       * </pre>
       *
       * <code>int32 code = 2;</code>
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *0 成功
       * </pre>
       *
       * <code>int32 code = 2;</code>
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object msg_ = "";
      /**
       * <code>string msg = 3;</code>
       */
      public java.lang.String getMsg() {
        java.lang.Object ref = msg_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          msg_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string msg = 3;</code>
       */
      public com.google.protobuf.ByteString
          getMsgBytes() {
        java.lang.Object ref = msg_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          msg_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string msg = 3;</code>
       */
      public Builder setMsg(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        msg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string msg = 3;</code>
       */
      public Builder clearMsg() {
        
        msg_ = getDefaultInstance().getMsg();
        onChanged();
        return this;
      }
      /**
       * <code>string msg = 3;</code>
       */
      public Builder setMsgBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        msg_ = value;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ResponseAck)
    }

    // @@protoc_insertion_point(class_scope:ResponseAck)
    private static final com.quhong.proto.YoustarProtoBase.ResponseAck DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.quhong.proto.YoustarProtoBase.ResponseAck();
    }

    public static com.quhong.proto.YoustarProtoBase.ResponseAck getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResponseAck>
        PARSER = new com.google.protobuf.AbstractParser<ResponseAck>() {
      public ResponseAck parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new ResponseAck(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResponseAck> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseAck> getParserForType() {
      return PARSER;
    }

    public com.quhong.proto.YoustarProtoBase.ResponseAck getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface LoginOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Login)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.MsgHeader header = 1;</code>
     */
    boolean hasHeader();
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    com.quhong.proto.YoustarProtoBase.MsgHeader getHeader();
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder getHeaderOrBuilder();

    /**
     * <pre>
     *客户端版本号
     * </pre>
     *
     * <code>int32 clientVersion = 2;</code>
     */
    int getClientVersion();

    /**
     * <pre>
     *客户端系统  android 1 ios 2
     * </pre>
     *
     * <code>int32 clientSystem = 3;</code>
     */
    int getClientSystem();

    /**
     * <pre>
     * 设备id，标明使用设备，其他设备区分开
     * </pre>
     *
     * <code>string deviceId = 4;</code>
     */
    java.lang.String getDeviceId();
    /**
     * <pre>
     * 设备id，标明使用设备，其他设备区分开
     * </pre>
     *
     * <code>string deviceId = 4;</code>
     */
    com.google.protobuf.ByteString
        getDeviceIdBytes();

    /**
     * <pre>
     *房间内上次获取的msgId
     * </pre>
     *
     * <code>int64 roomMsgId = 5;</code>
     */
    long getRoomMsgId();

    /**
     * <pre>
     *麦位信息版本
     * </pre>
     *
     * <code>int32 micVersion = 6;</code>
     */
    int getMicVersion();

    /**
     * <pre>
     *音乐列表版本
     * </pre>
     *
     * <code>int32 musicVersion = 7;</code>
     */
    int getMusicVersion();

    /**
     * <pre>
     *app语言 1 英语 2 阿语 3 土耳其语
     * </pre>
     *
     * <code>int32 slang = 8;</code>
     */
    int getSlang();
  }
  /**
   * <pre>
   * msgType=1001 握手消息
   * </pre>
   *
   * Protobuf type {@code Login}
   */
  public  static final class Login extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Login)
      LoginOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Login.newBuilder() to construct.
    private Login(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Login() {
      clientVersion_ = 0;
      clientSystem_ = 0;
      deviceId_ = "";
      roomMsgId_ = 0L;
      micVersion_ = 0;
      musicVersion_ = 0;
      slang_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Login(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              com.quhong.proto.YoustarProtoBase.MsgHeader.Builder subBuilder = null;
              if (header_ != null) {
                subBuilder = header_.toBuilder();
              }
              header_ = input.readMessage(com.quhong.proto.YoustarProtoBase.MsgHeader.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(header_);
                header_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              clientVersion_ = input.readInt32();
              break;
            }
            case 24: {

              clientSystem_ = input.readInt32();
              break;
            }
            case 34: {
              java.lang.String s = input.readStringRequireUtf8();

              deviceId_ = s;
              break;
            }
            case 40: {

              roomMsgId_ = input.readInt64();
              break;
            }
            case 48: {

              micVersion_ = input.readInt32();
              break;
            }
            case 56: {

              musicVersion_ = input.readInt32();
              break;
            }
            case 64: {

              slang_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.quhong.proto.YoustarProtoBase.internal_static_Login_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.quhong.proto.YoustarProtoBase.internal_static_Login_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.quhong.proto.YoustarProtoBase.Login.class, com.quhong.proto.YoustarProtoBase.Login.Builder.class);
    }

    public static final int HEADER_FIELD_NUMBER = 1;
    private com.quhong.proto.YoustarProtoBase.MsgHeader header_;
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    public boolean hasHeader() {
      return header_ != null;
    }
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    public com.quhong.proto.YoustarProtoBase.MsgHeader getHeader() {
      return header_ == null ? com.quhong.proto.YoustarProtoBase.MsgHeader.getDefaultInstance() : header_;
    }
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    public com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder getHeaderOrBuilder() {
      return getHeader();
    }

    public static final int CLIENTVERSION_FIELD_NUMBER = 2;
    private int clientVersion_;
    /**
     * <pre>
     *客户端版本号
     * </pre>
     *
     * <code>int32 clientVersion = 2;</code>
     */
    public int getClientVersion() {
      return clientVersion_;
    }

    public static final int CLIENTSYSTEM_FIELD_NUMBER = 3;
    private int clientSystem_;
    /**
     * <pre>
     *客户端系统  android 1 ios 2
     * </pre>
     *
     * <code>int32 clientSystem = 3;</code>
     */
    public int getClientSystem() {
      return clientSystem_;
    }

    public static final int DEVICEID_FIELD_NUMBER = 4;
    private volatile java.lang.Object deviceId_;
    /**
     * <pre>
     * 设备id，标明使用设备，其他设备区分开
     * </pre>
     *
     * <code>string deviceId = 4;</code>
     */
    public java.lang.String getDeviceId() {
      java.lang.Object ref = deviceId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deviceId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 设备id，标明使用设备，其他设备区分开
     * </pre>
     *
     * <code>string deviceId = 4;</code>
     */
    public com.google.protobuf.ByteString
        getDeviceIdBytes() {
      java.lang.Object ref = deviceId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deviceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ROOMMSGID_FIELD_NUMBER = 5;
    private long roomMsgId_;
    /**
     * <pre>
     *房间内上次获取的msgId
     * </pre>
     *
     * <code>int64 roomMsgId = 5;</code>
     */
    public long getRoomMsgId() {
      return roomMsgId_;
    }

    public static final int MICVERSION_FIELD_NUMBER = 6;
    private int micVersion_;
    /**
     * <pre>
     *麦位信息版本
     * </pre>
     *
     * <code>int32 micVersion = 6;</code>
     */
    public int getMicVersion() {
      return micVersion_;
    }

    public static final int MUSICVERSION_FIELD_NUMBER = 7;
    private int musicVersion_;
    /**
     * <pre>
     *音乐列表版本
     * </pre>
     *
     * <code>int32 musicVersion = 7;</code>
     */
    public int getMusicVersion() {
      return musicVersion_;
    }

    public static final int SLANG_FIELD_NUMBER = 8;
    private int slang_;
    /**
     * <pre>
     *app语言 1 英语 2 阿语 3 土耳其语
     * </pre>
     *
     * <code>int32 slang = 8;</code>
     */
    public int getSlang() {
      return slang_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (header_ != null) {
        output.writeMessage(1, getHeader());
      }
      if (clientVersion_ != 0) {
        output.writeInt32(2, clientVersion_);
      }
      if (clientSystem_ != 0) {
        output.writeInt32(3, clientSystem_);
      }
      if (!getDeviceIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, deviceId_);
      }
      if (roomMsgId_ != 0L) {
        output.writeInt64(5, roomMsgId_);
      }
      if (micVersion_ != 0) {
        output.writeInt32(6, micVersion_);
      }
      if (musicVersion_ != 0) {
        output.writeInt32(7, musicVersion_);
      }
      if (slang_ != 0) {
        output.writeInt32(8, slang_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (header_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getHeader());
      }
      if (clientVersion_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, clientVersion_);
      }
      if (clientSystem_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, clientSystem_);
      }
      if (!getDeviceIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, deviceId_);
      }
      if (roomMsgId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, roomMsgId_);
      }
      if (micVersion_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, micVersion_);
      }
      if (musicVersion_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, musicVersion_);
      }
      if (slang_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, slang_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.quhong.proto.YoustarProtoBase.Login)) {
        return super.equals(obj);
      }
      com.quhong.proto.YoustarProtoBase.Login other = (com.quhong.proto.YoustarProtoBase.Login) obj;

      boolean result = true;
      result = result && (hasHeader() == other.hasHeader());
      if (hasHeader()) {
        result = result && getHeader()
            .equals(other.getHeader());
      }
      result = result && (getClientVersion()
          == other.getClientVersion());
      result = result && (getClientSystem()
          == other.getClientSystem());
      result = result && getDeviceId()
          .equals(other.getDeviceId());
      result = result && (getRoomMsgId()
          == other.getRoomMsgId());
      result = result && (getMicVersion()
          == other.getMicVersion());
      result = result && (getMusicVersion()
          == other.getMusicVersion());
      result = result && (getSlang()
          == other.getSlang());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeader()) {
        hash = (37 * hash) + HEADER_FIELD_NUMBER;
        hash = (53 * hash) + getHeader().hashCode();
      }
      hash = (37 * hash) + CLIENTVERSION_FIELD_NUMBER;
      hash = (53 * hash) + getClientVersion();
      hash = (37 * hash) + CLIENTSYSTEM_FIELD_NUMBER;
      hash = (53 * hash) + getClientSystem();
      hash = (37 * hash) + DEVICEID_FIELD_NUMBER;
      hash = (53 * hash) + getDeviceId().hashCode();
      hash = (37 * hash) + ROOMMSGID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRoomMsgId());
      hash = (37 * hash) + MICVERSION_FIELD_NUMBER;
      hash = (53 * hash) + getMicVersion();
      hash = (37 * hash) + MUSICVERSION_FIELD_NUMBER;
      hash = (53 * hash) + getMusicVersion();
      hash = (37 * hash) + SLANG_FIELD_NUMBER;
      hash = (53 * hash) + getSlang();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.quhong.proto.YoustarProtoBase.Login parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.Login parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.Login parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.Login parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.Login parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.Login parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.Login parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.Login parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.Login parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.Login parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.Login parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.Login parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.quhong.proto.YoustarProtoBase.Login prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * msgType=1001 握手消息
     * </pre>
     *
     * Protobuf type {@code Login}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Login)
        com.quhong.proto.YoustarProtoBase.LoginOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.quhong.proto.YoustarProtoBase.internal_static_Login_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.quhong.proto.YoustarProtoBase.internal_static_Login_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.quhong.proto.YoustarProtoBase.Login.class, com.quhong.proto.YoustarProtoBase.Login.Builder.class);
      }

      // Construct using com.quhong.proto.YoustarProtoBase.Login.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        if (headerBuilder_ == null) {
          header_ = null;
        } else {
          header_ = null;
          headerBuilder_ = null;
        }
        clientVersion_ = 0;

        clientSystem_ = 0;

        deviceId_ = "";

        roomMsgId_ = 0L;

        micVersion_ = 0;

        musicVersion_ = 0;

        slang_ = 0;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.quhong.proto.YoustarProtoBase.internal_static_Login_descriptor;
      }

      public com.quhong.proto.YoustarProtoBase.Login getDefaultInstanceForType() {
        return com.quhong.proto.YoustarProtoBase.Login.getDefaultInstance();
      }

      public com.quhong.proto.YoustarProtoBase.Login build() {
        com.quhong.proto.YoustarProtoBase.Login result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public com.quhong.proto.YoustarProtoBase.Login buildPartial() {
        com.quhong.proto.YoustarProtoBase.Login result = new com.quhong.proto.YoustarProtoBase.Login(this);
        if (headerBuilder_ == null) {
          result.header_ = header_;
        } else {
          result.header_ = headerBuilder_.build();
        }
        result.clientVersion_ = clientVersion_;
        result.clientSystem_ = clientSystem_;
        result.deviceId_ = deviceId_;
        result.roomMsgId_ = roomMsgId_;
        result.micVersion_ = micVersion_;
        result.musicVersion_ = musicVersion_;
        result.slang_ = slang_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.quhong.proto.YoustarProtoBase.Login) {
          return mergeFrom((com.quhong.proto.YoustarProtoBase.Login)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.quhong.proto.YoustarProtoBase.Login other) {
        if (other == com.quhong.proto.YoustarProtoBase.Login.getDefaultInstance()) return this;
        if (other.hasHeader()) {
          mergeHeader(other.getHeader());
        }
        if (other.getClientVersion() != 0) {
          setClientVersion(other.getClientVersion());
        }
        if (other.getClientSystem() != 0) {
          setClientSystem(other.getClientSystem());
        }
        if (!other.getDeviceId().isEmpty()) {
          deviceId_ = other.deviceId_;
          onChanged();
        }
        if (other.getRoomMsgId() != 0L) {
          setRoomMsgId(other.getRoomMsgId());
        }
        if (other.getMicVersion() != 0) {
          setMicVersion(other.getMicVersion());
        }
        if (other.getMusicVersion() != 0) {
          setMusicVersion(other.getMusicVersion());
        }
        if (other.getSlang() != 0) {
          setSlang(other.getSlang());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.quhong.proto.YoustarProtoBase.Login parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.quhong.proto.YoustarProtoBase.Login) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.quhong.proto.YoustarProtoBase.MsgHeader header_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoBase.MsgHeader, com.quhong.proto.YoustarProtoBase.MsgHeader.Builder, com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder> headerBuilder_;
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public boolean hasHeader() {
        return headerBuilder_ != null || header_ != null;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public com.quhong.proto.YoustarProtoBase.MsgHeader getHeader() {
        if (headerBuilder_ == null) {
          return header_ == null ? com.quhong.proto.YoustarProtoBase.MsgHeader.getDefaultInstance() : header_;
        } else {
          return headerBuilder_.getMessage();
        }
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder setHeader(com.quhong.proto.YoustarProtoBase.MsgHeader value) {
        if (headerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          header_ = value;
          onChanged();
        } else {
          headerBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder setHeader(
          com.quhong.proto.YoustarProtoBase.MsgHeader.Builder builderForValue) {
        if (headerBuilder_ == null) {
          header_ = builderForValue.build();
          onChanged();
        } else {
          headerBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder mergeHeader(com.quhong.proto.YoustarProtoBase.MsgHeader value) {
        if (headerBuilder_ == null) {
          if (header_ != null) {
            header_ =
              com.quhong.proto.YoustarProtoBase.MsgHeader.newBuilder(header_).mergeFrom(value).buildPartial();
          } else {
            header_ = value;
          }
          onChanged();
        } else {
          headerBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder clearHeader() {
        if (headerBuilder_ == null) {
          header_ = null;
          onChanged();
        } else {
          header_ = null;
          headerBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public com.quhong.proto.YoustarProtoBase.MsgHeader.Builder getHeaderBuilder() {
        
        onChanged();
        return getHeaderFieldBuilder().getBuilder();
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder getHeaderOrBuilder() {
        if (headerBuilder_ != null) {
          return headerBuilder_.getMessageOrBuilder();
        } else {
          return header_ == null ?
              com.quhong.proto.YoustarProtoBase.MsgHeader.getDefaultInstance() : header_;
        }
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoBase.MsgHeader, com.quhong.proto.YoustarProtoBase.MsgHeader.Builder, com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder> 
          getHeaderFieldBuilder() {
        if (headerBuilder_ == null) {
          headerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.quhong.proto.YoustarProtoBase.MsgHeader, com.quhong.proto.YoustarProtoBase.MsgHeader.Builder, com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder>(
                  getHeader(),
                  getParentForChildren(),
                  isClean());
          header_ = null;
        }
        return headerBuilder_;
      }

      private int clientVersion_ ;
      /**
       * <pre>
       *客户端版本号
       * </pre>
       *
       * <code>int32 clientVersion = 2;</code>
       */
      public int getClientVersion() {
        return clientVersion_;
      }
      /**
       * <pre>
       *客户端版本号
       * </pre>
       *
       * <code>int32 clientVersion = 2;</code>
       */
      public Builder setClientVersion(int value) {
        
        clientVersion_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *客户端版本号
       * </pre>
       *
       * <code>int32 clientVersion = 2;</code>
       */
      public Builder clearClientVersion() {
        
        clientVersion_ = 0;
        onChanged();
        return this;
      }

      private int clientSystem_ ;
      /**
       * <pre>
       *客户端系统  android 1 ios 2
       * </pre>
       *
       * <code>int32 clientSystem = 3;</code>
       */
      public int getClientSystem() {
        return clientSystem_;
      }
      /**
       * <pre>
       *客户端系统  android 1 ios 2
       * </pre>
       *
       * <code>int32 clientSystem = 3;</code>
       */
      public Builder setClientSystem(int value) {
        
        clientSystem_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *客户端系统  android 1 ios 2
       * </pre>
       *
       * <code>int32 clientSystem = 3;</code>
       */
      public Builder clearClientSystem() {
        
        clientSystem_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object deviceId_ = "";
      /**
       * <pre>
       * 设备id，标明使用设备，其他设备区分开
       * </pre>
       *
       * <code>string deviceId = 4;</code>
       */
      public java.lang.String getDeviceId() {
        java.lang.Object ref = deviceId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          deviceId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 设备id，标明使用设备，其他设备区分开
       * </pre>
       *
       * <code>string deviceId = 4;</code>
       */
      public com.google.protobuf.ByteString
          getDeviceIdBytes() {
        java.lang.Object ref = deviceId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          deviceId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 设备id，标明使用设备，其他设备区分开
       * </pre>
       *
       * <code>string deviceId = 4;</code>
       */
      public Builder setDeviceId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        deviceId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备id，标明使用设备，其他设备区分开
       * </pre>
       *
       * <code>string deviceId = 4;</code>
       */
      public Builder clearDeviceId() {
        
        deviceId_ = getDefaultInstance().getDeviceId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备id，标明使用设备，其他设备区分开
       * </pre>
       *
       * <code>string deviceId = 4;</code>
       */
      public Builder setDeviceIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        deviceId_ = value;
        onChanged();
        return this;
      }

      private long roomMsgId_ ;
      /**
       * <pre>
       *房间内上次获取的msgId
       * </pre>
       *
       * <code>int64 roomMsgId = 5;</code>
       */
      public long getRoomMsgId() {
        return roomMsgId_;
      }
      /**
       * <pre>
       *房间内上次获取的msgId
       * </pre>
       *
       * <code>int64 roomMsgId = 5;</code>
       */
      public Builder setRoomMsgId(long value) {
        
        roomMsgId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *房间内上次获取的msgId
       * </pre>
       *
       * <code>int64 roomMsgId = 5;</code>
       */
      public Builder clearRoomMsgId() {
        
        roomMsgId_ = 0L;
        onChanged();
        return this;
      }

      private int micVersion_ ;
      /**
       * <pre>
       *麦位信息版本
       * </pre>
       *
       * <code>int32 micVersion = 6;</code>
       */
      public int getMicVersion() {
        return micVersion_;
      }
      /**
       * <pre>
       *麦位信息版本
       * </pre>
       *
       * <code>int32 micVersion = 6;</code>
       */
      public Builder setMicVersion(int value) {
        
        micVersion_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *麦位信息版本
       * </pre>
       *
       * <code>int32 micVersion = 6;</code>
       */
      public Builder clearMicVersion() {
        
        micVersion_ = 0;
        onChanged();
        return this;
      }

      private int musicVersion_ ;
      /**
       * <pre>
       *音乐列表版本
       * </pre>
       *
       * <code>int32 musicVersion = 7;</code>
       */
      public int getMusicVersion() {
        return musicVersion_;
      }
      /**
       * <pre>
       *音乐列表版本
       * </pre>
       *
       * <code>int32 musicVersion = 7;</code>
       */
      public Builder setMusicVersion(int value) {
        
        musicVersion_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *音乐列表版本
       * </pre>
       *
       * <code>int32 musicVersion = 7;</code>
       */
      public Builder clearMusicVersion() {
        
        musicVersion_ = 0;
        onChanged();
        return this;
      }

      private int slang_ ;
      /**
       * <pre>
       *app语言 1 英语 2 阿语 3 土耳其语
       * </pre>
       *
       * <code>int32 slang = 8;</code>
       */
      public int getSlang() {
        return slang_;
      }
      /**
       * <pre>
       *app语言 1 英语 2 阿语 3 土耳其语
       * </pre>
       *
       * <code>int32 slang = 8;</code>
       */
      public Builder setSlang(int value) {
        
        slang_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *app语言 1 英语 2 阿语 3 土耳其语
       * </pre>
       *
       * <code>int32 slang = 8;</code>
       */
      public Builder clearSlang() {
        
        slang_ = 0;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Login)
    }

    // @@protoc_insertion_point(class_scope:Login)
    private static final com.quhong.proto.YoustarProtoBase.Login DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.quhong.proto.YoustarProtoBase.Login();
    }

    public static com.quhong.proto.YoustarProtoBase.Login getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Login>
        PARSER = new com.google.protobuf.AbstractParser<Login>() {
      public Login parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new Login(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Login> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Login> getParserForType() {
      return PARSER;
    }

    public com.quhong.proto.YoustarProtoBase.Login getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface HeartBeatOrBuilder extends
      // @@protoc_insertion_point(interface_extends:HeartBeat)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.MsgHeader header = 1;</code>
     */
    boolean hasHeader();
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    com.quhong.proto.YoustarProtoBase.MsgHeader getHeader();
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder getHeaderOrBuilder();

    /**
     * <pre>
     *场景 0 其他 -1 后台  2 房间
     * </pre>
     *
     * <code>int32 scene = 2;</code>
     */
    int getScene();

    /**
     * <pre>
     *房间内上次获取的msgId
     * </pre>
     *
     * <code>int64 roomMsgId = 3;</code>
     */
    long getRoomMsgId();

    /**
     * <pre>
     *麦位信息版本
     * </pre>
     *
     * <code>int32 micVersion = 4;</code>
     */
    int getMicVersion();

    /**
     * <pre>
     *音乐列表版本
     * </pre>
     *
     * <code>int32 musicVersion = 5;</code>
     */
    int getMusicVersion();

    /**
     * <pre>
     *app语言 1 英语 2 阿语 3 土耳其语
     * </pre>
     *
     * <code>int32 slang = 6;</code>
     */
    int getSlang();

    /**
     * <pre>
     * 0 有人声，1 有声音没有人声，2 没有声音
     * </pre>
     *
     * <code>int32 vad = 7;</code>
     */
    int getVad();
  }
  /**
   * <pre>
   * msgType=1003 心跳消息
   * </pre>
   *
   * Protobuf type {@code HeartBeat}
   */
  public  static final class HeartBeat extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:HeartBeat)
      HeartBeatOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use HeartBeat.newBuilder() to construct.
    private HeartBeat(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private HeartBeat() {
      scene_ = 0;
      roomMsgId_ = 0L;
      micVersion_ = 0;
      musicVersion_ = 0;
      slang_ = 0;
      vad_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private HeartBeat(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              com.quhong.proto.YoustarProtoBase.MsgHeader.Builder subBuilder = null;
              if (header_ != null) {
                subBuilder = header_.toBuilder();
              }
              header_ = input.readMessage(com.quhong.proto.YoustarProtoBase.MsgHeader.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(header_);
                header_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              scene_ = input.readInt32();
              break;
            }
            case 24: {

              roomMsgId_ = input.readInt64();
              break;
            }
            case 32: {

              micVersion_ = input.readInt32();
              break;
            }
            case 40: {

              musicVersion_ = input.readInt32();
              break;
            }
            case 48: {

              slang_ = input.readInt32();
              break;
            }
            case 56: {

              vad_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.quhong.proto.YoustarProtoBase.internal_static_HeartBeat_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.quhong.proto.YoustarProtoBase.internal_static_HeartBeat_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.quhong.proto.YoustarProtoBase.HeartBeat.class, com.quhong.proto.YoustarProtoBase.HeartBeat.Builder.class);
    }

    public static final int HEADER_FIELD_NUMBER = 1;
    private com.quhong.proto.YoustarProtoBase.MsgHeader header_;
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    public boolean hasHeader() {
      return header_ != null;
    }
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    public com.quhong.proto.YoustarProtoBase.MsgHeader getHeader() {
      return header_ == null ? com.quhong.proto.YoustarProtoBase.MsgHeader.getDefaultInstance() : header_;
    }
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    public com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder getHeaderOrBuilder() {
      return getHeader();
    }

    public static final int SCENE_FIELD_NUMBER = 2;
    private int scene_;
    /**
     * <pre>
     *场景 0 其他 -1 后台  2 房间
     * </pre>
     *
     * <code>int32 scene = 2;</code>
     */
    public int getScene() {
      return scene_;
    }

    public static final int ROOMMSGID_FIELD_NUMBER = 3;
    private long roomMsgId_;
    /**
     * <pre>
     *房间内上次获取的msgId
     * </pre>
     *
     * <code>int64 roomMsgId = 3;</code>
     */
    public long getRoomMsgId() {
      return roomMsgId_;
    }

    public static final int MICVERSION_FIELD_NUMBER = 4;
    private int micVersion_;
    /**
     * <pre>
     *麦位信息版本
     * </pre>
     *
     * <code>int32 micVersion = 4;</code>
     */
    public int getMicVersion() {
      return micVersion_;
    }

    public static final int MUSICVERSION_FIELD_NUMBER = 5;
    private int musicVersion_;
    /**
     * <pre>
     *音乐列表版本
     * </pre>
     *
     * <code>int32 musicVersion = 5;</code>
     */
    public int getMusicVersion() {
      return musicVersion_;
    }

    public static final int SLANG_FIELD_NUMBER = 6;
    private int slang_;
    /**
     * <pre>
     *app语言 1 英语 2 阿语 3 土耳其语
     * </pre>
     *
     * <code>int32 slang = 6;</code>
     */
    public int getSlang() {
      return slang_;
    }

    public static final int VAD_FIELD_NUMBER = 7;
    private int vad_;
    /**
     * <pre>
     * 0 有人声，1 有声音没有人声，2 没有声音
     * </pre>
     *
     * <code>int32 vad = 7;</code>
     */
    public int getVad() {
      return vad_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (header_ != null) {
        output.writeMessage(1, getHeader());
      }
      if (scene_ != 0) {
        output.writeInt32(2, scene_);
      }
      if (roomMsgId_ != 0L) {
        output.writeInt64(3, roomMsgId_);
      }
      if (micVersion_ != 0) {
        output.writeInt32(4, micVersion_);
      }
      if (musicVersion_ != 0) {
        output.writeInt32(5, musicVersion_);
      }
      if (slang_ != 0) {
        output.writeInt32(6, slang_);
      }
      if (vad_ != 0) {
        output.writeInt32(7, vad_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (header_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getHeader());
      }
      if (scene_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, scene_);
      }
      if (roomMsgId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, roomMsgId_);
      }
      if (micVersion_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, micVersion_);
      }
      if (musicVersion_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, musicVersion_);
      }
      if (slang_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, slang_);
      }
      if (vad_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, vad_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.quhong.proto.YoustarProtoBase.HeartBeat)) {
        return super.equals(obj);
      }
      com.quhong.proto.YoustarProtoBase.HeartBeat other = (com.quhong.proto.YoustarProtoBase.HeartBeat) obj;

      boolean result = true;
      result = result && (hasHeader() == other.hasHeader());
      if (hasHeader()) {
        result = result && getHeader()
            .equals(other.getHeader());
      }
      result = result && (getScene()
          == other.getScene());
      result = result && (getRoomMsgId()
          == other.getRoomMsgId());
      result = result && (getMicVersion()
          == other.getMicVersion());
      result = result && (getMusicVersion()
          == other.getMusicVersion());
      result = result && (getSlang()
          == other.getSlang());
      result = result && (getVad()
          == other.getVad());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeader()) {
        hash = (37 * hash) + HEADER_FIELD_NUMBER;
        hash = (53 * hash) + getHeader().hashCode();
      }
      hash = (37 * hash) + SCENE_FIELD_NUMBER;
      hash = (53 * hash) + getScene();
      hash = (37 * hash) + ROOMMSGID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRoomMsgId());
      hash = (37 * hash) + MICVERSION_FIELD_NUMBER;
      hash = (53 * hash) + getMicVersion();
      hash = (37 * hash) + MUSICVERSION_FIELD_NUMBER;
      hash = (53 * hash) + getMusicVersion();
      hash = (37 * hash) + SLANG_FIELD_NUMBER;
      hash = (53 * hash) + getSlang();
      hash = (37 * hash) + VAD_FIELD_NUMBER;
      hash = (53 * hash) + getVad();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.quhong.proto.YoustarProtoBase.HeartBeat parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.HeartBeat parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.HeartBeat parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.HeartBeat parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.HeartBeat parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.HeartBeat parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.HeartBeat parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.HeartBeat parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.HeartBeat parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.HeartBeat parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.HeartBeat parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.HeartBeat parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.quhong.proto.YoustarProtoBase.HeartBeat prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * msgType=1003 心跳消息
     * </pre>
     *
     * Protobuf type {@code HeartBeat}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:HeartBeat)
        com.quhong.proto.YoustarProtoBase.HeartBeatOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.quhong.proto.YoustarProtoBase.internal_static_HeartBeat_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.quhong.proto.YoustarProtoBase.internal_static_HeartBeat_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.quhong.proto.YoustarProtoBase.HeartBeat.class, com.quhong.proto.YoustarProtoBase.HeartBeat.Builder.class);
      }

      // Construct using com.quhong.proto.YoustarProtoBase.HeartBeat.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        if (headerBuilder_ == null) {
          header_ = null;
        } else {
          header_ = null;
          headerBuilder_ = null;
        }
        scene_ = 0;

        roomMsgId_ = 0L;

        micVersion_ = 0;

        musicVersion_ = 0;

        slang_ = 0;

        vad_ = 0;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.quhong.proto.YoustarProtoBase.internal_static_HeartBeat_descriptor;
      }

      public com.quhong.proto.YoustarProtoBase.HeartBeat getDefaultInstanceForType() {
        return com.quhong.proto.YoustarProtoBase.HeartBeat.getDefaultInstance();
      }

      public com.quhong.proto.YoustarProtoBase.HeartBeat build() {
        com.quhong.proto.YoustarProtoBase.HeartBeat result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public com.quhong.proto.YoustarProtoBase.HeartBeat buildPartial() {
        com.quhong.proto.YoustarProtoBase.HeartBeat result = new com.quhong.proto.YoustarProtoBase.HeartBeat(this);
        if (headerBuilder_ == null) {
          result.header_ = header_;
        } else {
          result.header_ = headerBuilder_.build();
        }
        result.scene_ = scene_;
        result.roomMsgId_ = roomMsgId_;
        result.micVersion_ = micVersion_;
        result.musicVersion_ = musicVersion_;
        result.slang_ = slang_;
        result.vad_ = vad_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.quhong.proto.YoustarProtoBase.HeartBeat) {
          return mergeFrom((com.quhong.proto.YoustarProtoBase.HeartBeat)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.quhong.proto.YoustarProtoBase.HeartBeat other) {
        if (other == com.quhong.proto.YoustarProtoBase.HeartBeat.getDefaultInstance()) return this;
        if (other.hasHeader()) {
          mergeHeader(other.getHeader());
        }
        if (other.getScene() != 0) {
          setScene(other.getScene());
        }
        if (other.getRoomMsgId() != 0L) {
          setRoomMsgId(other.getRoomMsgId());
        }
        if (other.getMicVersion() != 0) {
          setMicVersion(other.getMicVersion());
        }
        if (other.getMusicVersion() != 0) {
          setMusicVersion(other.getMusicVersion());
        }
        if (other.getSlang() != 0) {
          setSlang(other.getSlang());
        }
        if (other.getVad() != 0) {
          setVad(other.getVad());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.quhong.proto.YoustarProtoBase.HeartBeat parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.quhong.proto.YoustarProtoBase.HeartBeat) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.quhong.proto.YoustarProtoBase.MsgHeader header_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoBase.MsgHeader, com.quhong.proto.YoustarProtoBase.MsgHeader.Builder, com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder> headerBuilder_;
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public boolean hasHeader() {
        return headerBuilder_ != null || header_ != null;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public com.quhong.proto.YoustarProtoBase.MsgHeader getHeader() {
        if (headerBuilder_ == null) {
          return header_ == null ? com.quhong.proto.YoustarProtoBase.MsgHeader.getDefaultInstance() : header_;
        } else {
          return headerBuilder_.getMessage();
        }
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder setHeader(com.quhong.proto.YoustarProtoBase.MsgHeader value) {
        if (headerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          header_ = value;
          onChanged();
        } else {
          headerBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder setHeader(
          com.quhong.proto.YoustarProtoBase.MsgHeader.Builder builderForValue) {
        if (headerBuilder_ == null) {
          header_ = builderForValue.build();
          onChanged();
        } else {
          headerBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder mergeHeader(com.quhong.proto.YoustarProtoBase.MsgHeader value) {
        if (headerBuilder_ == null) {
          if (header_ != null) {
            header_ =
              com.quhong.proto.YoustarProtoBase.MsgHeader.newBuilder(header_).mergeFrom(value).buildPartial();
          } else {
            header_ = value;
          }
          onChanged();
        } else {
          headerBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder clearHeader() {
        if (headerBuilder_ == null) {
          header_ = null;
          onChanged();
        } else {
          header_ = null;
          headerBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public com.quhong.proto.YoustarProtoBase.MsgHeader.Builder getHeaderBuilder() {
        
        onChanged();
        return getHeaderFieldBuilder().getBuilder();
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder getHeaderOrBuilder() {
        if (headerBuilder_ != null) {
          return headerBuilder_.getMessageOrBuilder();
        } else {
          return header_ == null ?
              com.quhong.proto.YoustarProtoBase.MsgHeader.getDefaultInstance() : header_;
        }
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoBase.MsgHeader, com.quhong.proto.YoustarProtoBase.MsgHeader.Builder, com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder> 
          getHeaderFieldBuilder() {
        if (headerBuilder_ == null) {
          headerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.quhong.proto.YoustarProtoBase.MsgHeader, com.quhong.proto.YoustarProtoBase.MsgHeader.Builder, com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder>(
                  getHeader(),
                  getParentForChildren(),
                  isClean());
          header_ = null;
        }
        return headerBuilder_;
      }

      private int scene_ ;
      /**
       * <pre>
       *场景 0 其他 -1 后台  2 房间
       * </pre>
       *
       * <code>int32 scene = 2;</code>
       */
      public int getScene() {
        return scene_;
      }
      /**
       * <pre>
       *场景 0 其他 -1 后台  2 房间
       * </pre>
       *
       * <code>int32 scene = 2;</code>
       */
      public Builder setScene(int value) {
        
        scene_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *场景 0 其他 -1 后台  2 房间
       * </pre>
       *
       * <code>int32 scene = 2;</code>
       */
      public Builder clearScene() {
        
        scene_ = 0;
        onChanged();
        return this;
      }

      private long roomMsgId_ ;
      /**
       * <pre>
       *房间内上次获取的msgId
       * </pre>
       *
       * <code>int64 roomMsgId = 3;</code>
       */
      public long getRoomMsgId() {
        return roomMsgId_;
      }
      /**
       * <pre>
       *房间内上次获取的msgId
       * </pre>
       *
       * <code>int64 roomMsgId = 3;</code>
       */
      public Builder setRoomMsgId(long value) {
        
        roomMsgId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *房间内上次获取的msgId
       * </pre>
       *
       * <code>int64 roomMsgId = 3;</code>
       */
      public Builder clearRoomMsgId() {
        
        roomMsgId_ = 0L;
        onChanged();
        return this;
      }

      private int micVersion_ ;
      /**
       * <pre>
       *麦位信息版本
       * </pre>
       *
       * <code>int32 micVersion = 4;</code>
       */
      public int getMicVersion() {
        return micVersion_;
      }
      /**
       * <pre>
       *麦位信息版本
       * </pre>
       *
       * <code>int32 micVersion = 4;</code>
       */
      public Builder setMicVersion(int value) {
        
        micVersion_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *麦位信息版本
       * </pre>
       *
       * <code>int32 micVersion = 4;</code>
       */
      public Builder clearMicVersion() {
        
        micVersion_ = 0;
        onChanged();
        return this;
      }

      private int musicVersion_ ;
      /**
       * <pre>
       *音乐列表版本
       * </pre>
       *
       * <code>int32 musicVersion = 5;</code>
       */
      public int getMusicVersion() {
        return musicVersion_;
      }
      /**
       * <pre>
       *音乐列表版本
       * </pre>
       *
       * <code>int32 musicVersion = 5;</code>
       */
      public Builder setMusicVersion(int value) {
        
        musicVersion_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *音乐列表版本
       * </pre>
       *
       * <code>int32 musicVersion = 5;</code>
       */
      public Builder clearMusicVersion() {
        
        musicVersion_ = 0;
        onChanged();
        return this;
      }

      private int slang_ ;
      /**
       * <pre>
       *app语言 1 英语 2 阿语 3 土耳其语
       * </pre>
       *
       * <code>int32 slang = 6;</code>
       */
      public int getSlang() {
        return slang_;
      }
      /**
       * <pre>
       *app语言 1 英语 2 阿语 3 土耳其语
       * </pre>
       *
       * <code>int32 slang = 6;</code>
       */
      public Builder setSlang(int value) {
        
        slang_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *app语言 1 英语 2 阿语 3 土耳其语
       * </pre>
       *
       * <code>int32 slang = 6;</code>
       */
      public Builder clearSlang() {
        
        slang_ = 0;
        onChanged();
        return this;
      }

      private int vad_ ;
      /**
       * <pre>
       * 0 有人声，1 有声音没有人声，2 没有声音
       * </pre>
       *
       * <code>int32 vad = 7;</code>
       */
      public int getVad() {
        return vad_;
      }
      /**
       * <pre>
       * 0 有人声，1 有声音没有人声，2 没有声音
       * </pre>
       *
       * <code>int32 vad = 7;</code>
       */
      public Builder setVad(int value) {
        
        vad_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 0 有人声，1 有声音没有人声，2 没有声音
       * </pre>
       *
       * <code>int32 vad = 7;</code>
       */
      public Builder clearVad() {
        
        vad_ = 0;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:HeartBeat)
    }

    // @@protoc_insertion_point(class_scope:HeartBeat)
    private static final com.quhong.proto.YoustarProtoBase.HeartBeat DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.quhong.proto.YoustarProtoBase.HeartBeat();
    }

    public static com.quhong.proto.YoustarProtoBase.HeartBeat getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<HeartBeat>
        PARSER = new com.google.protobuf.AbstractParser<HeartBeat>() {
      public HeartBeat parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new HeartBeat(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<HeartBeat> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<HeartBeat> getParserForType() {
      return PARSER;
    }

    public com.quhong.proto.YoustarProtoBase.HeartBeat getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface HeartAckOrBuilder extends
      // @@protoc_insertion_point(interface_extends:HeartAck)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.MsgHeader header = 1;</code>
     */
    boolean hasHeader();
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    com.quhong.proto.YoustarProtoBase.MsgHeader getHeader();
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder getHeaderOrBuilder();

    /**
     * <pre>
     *麦位信息版本
     * </pre>
     *
     * <code>int32 micVersion = 2;</code>
     */
    int getMicVersion();
  }
  /**
   * <pre>
   * msgType=1004 心跳回包
   * </pre>
   *
   * Protobuf type {@code HeartAck}
   */
  public  static final class HeartAck extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:HeartAck)
      HeartAckOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use HeartAck.newBuilder() to construct.
    private HeartAck(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private HeartAck() {
      micVersion_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private HeartAck(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              com.quhong.proto.YoustarProtoBase.MsgHeader.Builder subBuilder = null;
              if (header_ != null) {
                subBuilder = header_.toBuilder();
              }
              header_ = input.readMessage(com.quhong.proto.YoustarProtoBase.MsgHeader.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(header_);
                header_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              micVersion_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.quhong.proto.YoustarProtoBase.internal_static_HeartAck_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.quhong.proto.YoustarProtoBase.internal_static_HeartAck_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.quhong.proto.YoustarProtoBase.HeartAck.class, com.quhong.proto.YoustarProtoBase.HeartAck.Builder.class);
    }

    public static final int HEADER_FIELD_NUMBER = 1;
    private com.quhong.proto.YoustarProtoBase.MsgHeader header_;
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    public boolean hasHeader() {
      return header_ != null;
    }
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    public com.quhong.proto.YoustarProtoBase.MsgHeader getHeader() {
      return header_ == null ? com.quhong.proto.YoustarProtoBase.MsgHeader.getDefaultInstance() : header_;
    }
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    public com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder getHeaderOrBuilder() {
      return getHeader();
    }

    public static final int MICVERSION_FIELD_NUMBER = 2;
    private int micVersion_;
    /**
     * <pre>
     *麦位信息版本
     * </pre>
     *
     * <code>int32 micVersion = 2;</code>
     */
    public int getMicVersion() {
      return micVersion_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (header_ != null) {
        output.writeMessage(1, getHeader());
      }
      if (micVersion_ != 0) {
        output.writeInt32(2, micVersion_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (header_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getHeader());
      }
      if (micVersion_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, micVersion_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.quhong.proto.YoustarProtoBase.HeartAck)) {
        return super.equals(obj);
      }
      com.quhong.proto.YoustarProtoBase.HeartAck other = (com.quhong.proto.YoustarProtoBase.HeartAck) obj;

      boolean result = true;
      result = result && (hasHeader() == other.hasHeader());
      if (hasHeader()) {
        result = result && getHeader()
            .equals(other.getHeader());
      }
      result = result && (getMicVersion()
          == other.getMicVersion());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeader()) {
        hash = (37 * hash) + HEADER_FIELD_NUMBER;
        hash = (53 * hash) + getHeader().hashCode();
      }
      hash = (37 * hash) + MICVERSION_FIELD_NUMBER;
      hash = (53 * hash) + getMicVersion();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.quhong.proto.YoustarProtoBase.HeartAck parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.HeartAck parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.HeartAck parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.HeartAck parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.HeartAck parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.HeartAck parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.HeartAck parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.HeartAck parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.HeartAck parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.HeartAck parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.HeartAck parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.HeartAck parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.quhong.proto.YoustarProtoBase.HeartAck prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * msgType=1004 心跳回包
     * </pre>
     *
     * Protobuf type {@code HeartAck}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:HeartAck)
        com.quhong.proto.YoustarProtoBase.HeartAckOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.quhong.proto.YoustarProtoBase.internal_static_HeartAck_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.quhong.proto.YoustarProtoBase.internal_static_HeartAck_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.quhong.proto.YoustarProtoBase.HeartAck.class, com.quhong.proto.YoustarProtoBase.HeartAck.Builder.class);
      }

      // Construct using com.quhong.proto.YoustarProtoBase.HeartAck.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        if (headerBuilder_ == null) {
          header_ = null;
        } else {
          header_ = null;
          headerBuilder_ = null;
        }
        micVersion_ = 0;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.quhong.proto.YoustarProtoBase.internal_static_HeartAck_descriptor;
      }

      public com.quhong.proto.YoustarProtoBase.HeartAck getDefaultInstanceForType() {
        return com.quhong.proto.YoustarProtoBase.HeartAck.getDefaultInstance();
      }

      public com.quhong.proto.YoustarProtoBase.HeartAck build() {
        com.quhong.proto.YoustarProtoBase.HeartAck result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public com.quhong.proto.YoustarProtoBase.HeartAck buildPartial() {
        com.quhong.proto.YoustarProtoBase.HeartAck result = new com.quhong.proto.YoustarProtoBase.HeartAck(this);
        if (headerBuilder_ == null) {
          result.header_ = header_;
        } else {
          result.header_ = headerBuilder_.build();
        }
        result.micVersion_ = micVersion_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.quhong.proto.YoustarProtoBase.HeartAck) {
          return mergeFrom((com.quhong.proto.YoustarProtoBase.HeartAck)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.quhong.proto.YoustarProtoBase.HeartAck other) {
        if (other == com.quhong.proto.YoustarProtoBase.HeartAck.getDefaultInstance()) return this;
        if (other.hasHeader()) {
          mergeHeader(other.getHeader());
        }
        if (other.getMicVersion() != 0) {
          setMicVersion(other.getMicVersion());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.quhong.proto.YoustarProtoBase.HeartAck parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.quhong.proto.YoustarProtoBase.HeartAck) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.quhong.proto.YoustarProtoBase.MsgHeader header_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoBase.MsgHeader, com.quhong.proto.YoustarProtoBase.MsgHeader.Builder, com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder> headerBuilder_;
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public boolean hasHeader() {
        return headerBuilder_ != null || header_ != null;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public com.quhong.proto.YoustarProtoBase.MsgHeader getHeader() {
        if (headerBuilder_ == null) {
          return header_ == null ? com.quhong.proto.YoustarProtoBase.MsgHeader.getDefaultInstance() : header_;
        } else {
          return headerBuilder_.getMessage();
        }
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder setHeader(com.quhong.proto.YoustarProtoBase.MsgHeader value) {
        if (headerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          header_ = value;
          onChanged();
        } else {
          headerBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder setHeader(
          com.quhong.proto.YoustarProtoBase.MsgHeader.Builder builderForValue) {
        if (headerBuilder_ == null) {
          header_ = builderForValue.build();
          onChanged();
        } else {
          headerBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder mergeHeader(com.quhong.proto.YoustarProtoBase.MsgHeader value) {
        if (headerBuilder_ == null) {
          if (header_ != null) {
            header_ =
              com.quhong.proto.YoustarProtoBase.MsgHeader.newBuilder(header_).mergeFrom(value).buildPartial();
          } else {
            header_ = value;
          }
          onChanged();
        } else {
          headerBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder clearHeader() {
        if (headerBuilder_ == null) {
          header_ = null;
          onChanged();
        } else {
          header_ = null;
          headerBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public com.quhong.proto.YoustarProtoBase.MsgHeader.Builder getHeaderBuilder() {
        
        onChanged();
        return getHeaderFieldBuilder().getBuilder();
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder getHeaderOrBuilder() {
        if (headerBuilder_ != null) {
          return headerBuilder_.getMessageOrBuilder();
        } else {
          return header_ == null ?
              com.quhong.proto.YoustarProtoBase.MsgHeader.getDefaultInstance() : header_;
        }
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoBase.MsgHeader, com.quhong.proto.YoustarProtoBase.MsgHeader.Builder, com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder> 
          getHeaderFieldBuilder() {
        if (headerBuilder_ == null) {
          headerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.quhong.proto.YoustarProtoBase.MsgHeader, com.quhong.proto.YoustarProtoBase.MsgHeader.Builder, com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder>(
                  getHeader(),
                  getParentForChildren(),
                  isClean());
          header_ = null;
        }
        return headerBuilder_;
      }

      private int micVersion_ ;
      /**
       * <pre>
       *麦位信息版本
       * </pre>
       *
       * <code>int32 micVersion = 2;</code>
       */
      public int getMicVersion() {
        return micVersion_;
      }
      /**
       * <pre>
       *麦位信息版本
       * </pre>
       *
       * <code>int32 micVersion = 2;</code>
       */
      public Builder setMicVersion(int value) {
        
        micVersion_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *麦位信息版本
       * </pre>
       *
       * <code>int32 micVersion = 2;</code>
       */
      public Builder clearMicVersion() {
        
        micVersion_ = 0;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:HeartAck)
    }

    // @@protoc_insertion_point(class_scope:HeartAck)
    private static final com.quhong.proto.YoustarProtoBase.HeartAck DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.quhong.proto.YoustarProtoBase.HeartAck();
    }

    public static com.quhong.proto.YoustarProtoBase.HeartAck getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<HeartAck>
        PARSER = new com.google.protobuf.AbstractParser<HeartAck>() {
      public HeartAck parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new HeartAck(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<HeartAck> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<HeartAck> getParserForType() {
      return PARSER;
    }

    public com.quhong.proto.YoustarProtoBase.HeartAck getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ClientRecvAckOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ClientRecvAck)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.MsgHeader header = 1;</code>
     */
    boolean hasHeader();
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    com.quhong.proto.YoustarProtoBase.MsgHeader getHeader();
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder getHeaderOrBuilder();

    /**
     * <pre>
     *确认的msgId
     * </pre>
     *
     * <code>int64 msgId = 2;</code>
     */
    long getMsgId();

    /**
     * <pre>
     *cmdid
     * </pre>
     *
     * <code>int32 cmd = 3;</code>
     */
    int getCmd();
  }
  /**
   * <pre>
   * msgType=1005 客户端收到消息确认
   * </pre>
   *
   * Protobuf type {@code ClientRecvAck}
   */
  public  static final class ClientRecvAck extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ClientRecvAck)
      ClientRecvAckOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ClientRecvAck.newBuilder() to construct.
    private ClientRecvAck(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ClientRecvAck() {
      msgId_ = 0L;
      cmd_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ClientRecvAck(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              com.quhong.proto.YoustarProtoBase.MsgHeader.Builder subBuilder = null;
              if (header_ != null) {
                subBuilder = header_.toBuilder();
              }
              header_ = input.readMessage(com.quhong.proto.YoustarProtoBase.MsgHeader.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(header_);
                header_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              msgId_ = input.readInt64();
              break;
            }
            case 24: {

              cmd_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.quhong.proto.YoustarProtoBase.internal_static_ClientRecvAck_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.quhong.proto.YoustarProtoBase.internal_static_ClientRecvAck_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.quhong.proto.YoustarProtoBase.ClientRecvAck.class, com.quhong.proto.YoustarProtoBase.ClientRecvAck.Builder.class);
    }

    public static final int HEADER_FIELD_NUMBER = 1;
    private com.quhong.proto.YoustarProtoBase.MsgHeader header_;
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    public boolean hasHeader() {
      return header_ != null;
    }
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    public com.quhong.proto.YoustarProtoBase.MsgHeader getHeader() {
      return header_ == null ? com.quhong.proto.YoustarProtoBase.MsgHeader.getDefaultInstance() : header_;
    }
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    public com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder getHeaderOrBuilder() {
      return getHeader();
    }

    public static final int MSGID_FIELD_NUMBER = 2;
    private long msgId_;
    /**
     * <pre>
     *确认的msgId
     * </pre>
     *
     * <code>int64 msgId = 2;</code>
     */
    public long getMsgId() {
      return msgId_;
    }

    public static final int CMD_FIELD_NUMBER = 3;
    private int cmd_;
    /**
     * <pre>
     *cmdid
     * </pre>
     *
     * <code>int32 cmd = 3;</code>
     */
    public int getCmd() {
      return cmd_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (header_ != null) {
        output.writeMessage(1, getHeader());
      }
      if (msgId_ != 0L) {
        output.writeInt64(2, msgId_);
      }
      if (cmd_ != 0) {
        output.writeInt32(3, cmd_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (header_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getHeader());
      }
      if (msgId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, msgId_);
      }
      if (cmd_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, cmd_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.quhong.proto.YoustarProtoBase.ClientRecvAck)) {
        return super.equals(obj);
      }
      com.quhong.proto.YoustarProtoBase.ClientRecvAck other = (com.quhong.proto.YoustarProtoBase.ClientRecvAck) obj;

      boolean result = true;
      result = result && (hasHeader() == other.hasHeader());
      if (hasHeader()) {
        result = result && getHeader()
            .equals(other.getHeader());
      }
      result = result && (getMsgId()
          == other.getMsgId());
      result = result && (getCmd()
          == other.getCmd());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeader()) {
        hash = (37 * hash) + HEADER_FIELD_NUMBER;
        hash = (53 * hash) + getHeader().hashCode();
      }
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getMsgId());
      hash = (37 * hash) + CMD_FIELD_NUMBER;
      hash = (53 * hash) + getCmd();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.quhong.proto.YoustarProtoBase.ClientRecvAck parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.ClientRecvAck parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.ClientRecvAck parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.ClientRecvAck parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.ClientRecvAck parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.ClientRecvAck parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.ClientRecvAck parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.ClientRecvAck parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.ClientRecvAck parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.ClientRecvAck parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.ClientRecvAck parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.ClientRecvAck parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.quhong.proto.YoustarProtoBase.ClientRecvAck prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * msgType=1005 客户端收到消息确认
     * </pre>
     *
     * Protobuf type {@code ClientRecvAck}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ClientRecvAck)
        com.quhong.proto.YoustarProtoBase.ClientRecvAckOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.quhong.proto.YoustarProtoBase.internal_static_ClientRecvAck_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.quhong.proto.YoustarProtoBase.internal_static_ClientRecvAck_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.quhong.proto.YoustarProtoBase.ClientRecvAck.class, com.quhong.proto.YoustarProtoBase.ClientRecvAck.Builder.class);
      }

      // Construct using com.quhong.proto.YoustarProtoBase.ClientRecvAck.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        if (headerBuilder_ == null) {
          header_ = null;
        } else {
          header_ = null;
          headerBuilder_ = null;
        }
        msgId_ = 0L;

        cmd_ = 0;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.quhong.proto.YoustarProtoBase.internal_static_ClientRecvAck_descriptor;
      }

      public com.quhong.proto.YoustarProtoBase.ClientRecvAck getDefaultInstanceForType() {
        return com.quhong.proto.YoustarProtoBase.ClientRecvAck.getDefaultInstance();
      }

      public com.quhong.proto.YoustarProtoBase.ClientRecvAck build() {
        com.quhong.proto.YoustarProtoBase.ClientRecvAck result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public com.quhong.proto.YoustarProtoBase.ClientRecvAck buildPartial() {
        com.quhong.proto.YoustarProtoBase.ClientRecvAck result = new com.quhong.proto.YoustarProtoBase.ClientRecvAck(this);
        if (headerBuilder_ == null) {
          result.header_ = header_;
        } else {
          result.header_ = headerBuilder_.build();
        }
        result.msgId_ = msgId_;
        result.cmd_ = cmd_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.quhong.proto.YoustarProtoBase.ClientRecvAck) {
          return mergeFrom((com.quhong.proto.YoustarProtoBase.ClientRecvAck)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.quhong.proto.YoustarProtoBase.ClientRecvAck other) {
        if (other == com.quhong.proto.YoustarProtoBase.ClientRecvAck.getDefaultInstance()) return this;
        if (other.hasHeader()) {
          mergeHeader(other.getHeader());
        }
        if (other.getMsgId() != 0L) {
          setMsgId(other.getMsgId());
        }
        if (other.getCmd() != 0) {
          setCmd(other.getCmd());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.quhong.proto.YoustarProtoBase.ClientRecvAck parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.quhong.proto.YoustarProtoBase.ClientRecvAck) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.quhong.proto.YoustarProtoBase.MsgHeader header_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoBase.MsgHeader, com.quhong.proto.YoustarProtoBase.MsgHeader.Builder, com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder> headerBuilder_;
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public boolean hasHeader() {
        return headerBuilder_ != null || header_ != null;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public com.quhong.proto.YoustarProtoBase.MsgHeader getHeader() {
        if (headerBuilder_ == null) {
          return header_ == null ? com.quhong.proto.YoustarProtoBase.MsgHeader.getDefaultInstance() : header_;
        } else {
          return headerBuilder_.getMessage();
        }
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder setHeader(com.quhong.proto.YoustarProtoBase.MsgHeader value) {
        if (headerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          header_ = value;
          onChanged();
        } else {
          headerBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder setHeader(
          com.quhong.proto.YoustarProtoBase.MsgHeader.Builder builderForValue) {
        if (headerBuilder_ == null) {
          header_ = builderForValue.build();
          onChanged();
        } else {
          headerBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder mergeHeader(com.quhong.proto.YoustarProtoBase.MsgHeader value) {
        if (headerBuilder_ == null) {
          if (header_ != null) {
            header_ =
              com.quhong.proto.YoustarProtoBase.MsgHeader.newBuilder(header_).mergeFrom(value).buildPartial();
          } else {
            header_ = value;
          }
          onChanged();
        } else {
          headerBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder clearHeader() {
        if (headerBuilder_ == null) {
          header_ = null;
          onChanged();
        } else {
          header_ = null;
          headerBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public com.quhong.proto.YoustarProtoBase.MsgHeader.Builder getHeaderBuilder() {
        
        onChanged();
        return getHeaderFieldBuilder().getBuilder();
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder getHeaderOrBuilder() {
        if (headerBuilder_ != null) {
          return headerBuilder_.getMessageOrBuilder();
        } else {
          return header_ == null ?
              com.quhong.proto.YoustarProtoBase.MsgHeader.getDefaultInstance() : header_;
        }
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoBase.MsgHeader, com.quhong.proto.YoustarProtoBase.MsgHeader.Builder, com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder> 
          getHeaderFieldBuilder() {
        if (headerBuilder_ == null) {
          headerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.quhong.proto.YoustarProtoBase.MsgHeader, com.quhong.proto.YoustarProtoBase.MsgHeader.Builder, com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder>(
                  getHeader(),
                  getParentForChildren(),
                  isClean());
          header_ = null;
        }
        return headerBuilder_;
      }

      private long msgId_ ;
      /**
       * <pre>
       *确认的msgId
       * </pre>
       *
       * <code>int64 msgId = 2;</code>
       */
      public long getMsgId() {
        return msgId_;
      }
      /**
       * <pre>
       *确认的msgId
       * </pre>
       *
       * <code>int64 msgId = 2;</code>
       */
      public Builder setMsgId(long value) {
        
        msgId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *确认的msgId
       * </pre>
       *
       * <code>int64 msgId = 2;</code>
       */
      public Builder clearMsgId() {
        
        msgId_ = 0L;
        onChanged();
        return this;
      }

      private int cmd_ ;
      /**
       * <pre>
       *cmdid
       * </pre>
       *
       * <code>int32 cmd = 3;</code>
       */
      public int getCmd() {
        return cmd_;
      }
      /**
       * <pre>
       *cmdid
       * </pre>
       *
       * <code>int32 cmd = 3;</code>
       */
      public Builder setCmd(int value) {
        
        cmd_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *cmdid
       * </pre>
       *
       * <code>int32 cmd = 3;</code>
       */
      public Builder clearCmd() {
        
        cmd_ = 0;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ClientRecvAck)
    }

    // @@protoc_insertion_point(class_scope:ClientRecvAck)
    private static final com.quhong.proto.YoustarProtoBase.ClientRecvAck DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.quhong.proto.YoustarProtoBase.ClientRecvAck();
    }

    public static com.quhong.proto.YoustarProtoBase.ClientRecvAck getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ClientRecvAck>
        PARSER = new com.google.protobuf.AbstractParser<ClientRecvAck>() {
      public ClientRecvAck parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new ClientRecvAck(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ClientRecvAck> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ClientRecvAck> getParserForType() {
      return PARSER;
    }

    public com.quhong.proto.YoustarProtoBase.ClientRecvAck getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChangeSceneOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ChangeScene)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.MsgHeader header = 1;</code>
     */
    boolean hasHeader();
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    com.quhong.proto.YoustarProtoBase.MsgHeader getHeader();
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder getHeaderOrBuilder();

    /**
     * <pre>
     *旧场景
     * </pre>
     *
     * <code>int32 oldScene = 2;</code>
     */
    int getOldScene();

    /**
     * <pre>
     *新场景
     * </pre>
     *
     * <code>int32 newScene = 3;</code>
     */
    int getNewScene();
  }
  /**
   * <pre>
   * msgType=1007 场景切换协议
   * </pre>
   *
   * Protobuf type {@code ChangeScene}
   */
  public  static final class ChangeScene extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ChangeScene)
      ChangeSceneOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ChangeScene.newBuilder() to construct.
    private ChangeScene(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ChangeScene() {
      oldScene_ = 0;
      newScene_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ChangeScene(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              com.quhong.proto.YoustarProtoBase.MsgHeader.Builder subBuilder = null;
              if (header_ != null) {
                subBuilder = header_.toBuilder();
              }
              header_ = input.readMessage(com.quhong.proto.YoustarProtoBase.MsgHeader.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(header_);
                header_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              oldScene_ = input.readInt32();
              break;
            }
            case 24: {

              newScene_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.quhong.proto.YoustarProtoBase.internal_static_ChangeScene_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.quhong.proto.YoustarProtoBase.internal_static_ChangeScene_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.quhong.proto.YoustarProtoBase.ChangeScene.class, com.quhong.proto.YoustarProtoBase.ChangeScene.Builder.class);
    }

    public static final int HEADER_FIELD_NUMBER = 1;
    private com.quhong.proto.YoustarProtoBase.MsgHeader header_;
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    public boolean hasHeader() {
      return header_ != null;
    }
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    public com.quhong.proto.YoustarProtoBase.MsgHeader getHeader() {
      return header_ == null ? com.quhong.proto.YoustarProtoBase.MsgHeader.getDefaultInstance() : header_;
    }
    /**
     * <code>.MsgHeader header = 1;</code>
     */
    public com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder getHeaderOrBuilder() {
      return getHeader();
    }

    public static final int OLDSCENE_FIELD_NUMBER = 2;
    private int oldScene_;
    /**
     * <pre>
     *旧场景
     * </pre>
     *
     * <code>int32 oldScene = 2;</code>
     */
    public int getOldScene() {
      return oldScene_;
    }

    public static final int NEWSCENE_FIELD_NUMBER = 3;
    private int newScene_;
    /**
     * <pre>
     *新场景
     * </pre>
     *
     * <code>int32 newScene = 3;</code>
     */
    public int getNewScene() {
      return newScene_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (header_ != null) {
        output.writeMessage(1, getHeader());
      }
      if (oldScene_ != 0) {
        output.writeInt32(2, oldScene_);
      }
      if (newScene_ != 0) {
        output.writeInt32(3, newScene_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (header_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getHeader());
      }
      if (oldScene_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, oldScene_);
      }
      if (newScene_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, newScene_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.quhong.proto.YoustarProtoBase.ChangeScene)) {
        return super.equals(obj);
      }
      com.quhong.proto.YoustarProtoBase.ChangeScene other = (com.quhong.proto.YoustarProtoBase.ChangeScene) obj;

      boolean result = true;
      result = result && (hasHeader() == other.hasHeader());
      if (hasHeader()) {
        result = result && getHeader()
            .equals(other.getHeader());
      }
      result = result && (getOldScene()
          == other.getOldScene());
      result = result && (getNewScene()
          == other.getNewScene());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeader()) {
        hash = (37 * hash) + HEADER_FIELD_NUMBER;
        hash = (53 * hash) + getHeader().hashCode();
      }
      hash = (37 * hash) + OLDSCENE_FIELD_NUMBER;
      hash = (53 * hash) + getOldScene();
      hash = (37 * hash) + NEWSCENE_FIELD_NUMBER;
      hash = (53 * hash) + getNewScene();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.quhong.proto.YoustarProtoBase.ChangeScene parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.ChangeScene parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.ChangeScene parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.ChangeScene parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.ChangeScene parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.ChangeScene parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.ChangeScene parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.ChangeScene parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.ChangeScene parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.ChangeScene parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.ChangeScene parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.ChangeScene parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.quhong.proto.YoustarProtoBase.ChangeScene prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * msgType=1007 场景切换协议
     * </pre>
     *
     * Protobuf type {@code ChangeScene}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ChangeScene)
        com.quhong.proto.YoustarProtoBase.ChangeSceneOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.quhong.proto.YoustarProtoBase.internal_static_ChangeScene_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.quhong.proto.YoustarProtoBase.internal_static_ChangeScene_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.quhong.proto.YoustarProtoBase.ChangeScene.class, com.quhong.proto.YoustarProtoBase.ChangeScene.Builder.class);
      }

      // Construct using com.quhong.proto.YoustarProtoBase.ChangeScene.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        if (headerBuilder_ == null) {
          header_ = null;
        } else {
          header_ = null;
          headerBuilder_ = null;
        }
        oldScene_ = 0;

        newScene_ = 0;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.quhong.proto.YoustarProtoBase.internal_static_ChangeScene_descriptor;
      }

      public com.quhong.proto.YoustarProtoBase.ChangeScene getDefaultInstanceForType() {
        return com.quhong.proto.YoustarProtoBase.ChangeScene.getDefaultInstance();
      }

      public com.quhong.proto.YoustarProtoBase.ChangeScene build() {
        com.quhong.proto.YoustarProtoBase.ChangeScene result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public com.quhong.proto.YoustarProtoBase.ChangeScene buildPartial() {
        com.quhong.proto.YoustarProtoBase.ChangeScene result = new com.quhong.proto.YoustarProtoBase.ChangeScene(this);
        if (headerBuilder_ == null) {
          result.header_ = header_;
        } else {
          result.header_ = headerBuilder_.build();
        }
        result.oldScene_ = oldScene_;
        result.newScene_ = newScene_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.quhong.proto.YoustarProtoBase.ChangeScene) {
          return mergeFrom((com.quhong.proto.YoustarProtoBase.ChangeScene)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.quhong.proto.YoustarProtoBase.ChangeScene other) {
        if (other == com.quhong.proto.YoustarProtoBase.ChangeScene.getDefaultInstance()) return this;
        if (other.hasHeader()) {
          mergeHeader(other.getHeader());
        }
        if (other.getOldScene() != 0) {
          setOldScene(other.getOldScene());
        }
        if (other.getNewScene() != 0) {
          setNewScene(other.getNewScene());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.quhong.proto.YoustarProtoBase.ChangeScene parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.quhong.proto.YoustarProtoBase.ChangeScene) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.quhong.proto.YoustarProtoBase.MsgHeader header_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoBase.MsgHeader, com.quhong.proto.YoustarProtoBase.MsgHeader.Builder, com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder> headerBuilder_;
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public boolean hasHeader() {
        return headerBuilder_ != null || header_ != null;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public com.quhong.proto.YoustarProtoBase.MsgHeader getHeader() {
        if (headerBuilder_ == null) {
          return header_ == null ? com.quhong.proto.YoustarProtoBase.MsgHeader.getDefaultInstance() : header_;
        } else {
          return headerBuilder_.getMessage();
        }
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder setHeader(com.quhong.proto.YoustarProtoBase.MsgHeader value) {
        if (headerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          header_ = value;
          onChanged();
        } else {
          headerBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder setHeader(
          com.quhong.proto.YoustarProtoBase.MsgHeader.Builder builderForValue) {
        if (headerBuilder_ == null) {
          header_ = builderForValue.build();
          onChanged();
        } else {
          headerBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder mergeHeader(com.quhong.proto.YoustarProtoBase.MsgHeader value) {
        if (headerBuilder_ == null) {
          if (header_ != null) {
            header_ =
              com.quhong.proto.YoustarProtoBase.MsgHeader.newBuilder(header_).mergeFrom(value).buildPartial();
          } else {
            header_ = value;
          }
          onChanged();
        } else {
          headerBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public Builder clearHeader() {
        if (headerBuilder_ == null) {
          header_ = null;
          onChanged();
        } else {
          header_ = null;
          headerBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public com.quhong.proto.YoustarProtoBase.MsgHeader.Builder getHeaderBuilder() {
        
        onChanged();
        return getHeaderFieldBuilder().getBuilder();
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      public com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder getHeaderOrBuilder() {
        if (headerBuilder_ != null) {
          return headerBuilder_.getMessageOrBuilder();
        } else {
          return header_ == null ?
              com.quhong.proto.YoustarProtoBase.MsgHeader.getDefaultInstance() : header_;
        }
      }
      /**
       * <code>.MsgHeader header = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.quhong.proto.YoustarProtoBase.MsgHeader, com.quhong.proto.YoustarProtoBase.MsgHeader.Builder, com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder> 
          getHeaderFieldBuilder() {
        if (headerBuilder_ == null) {
          headerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.quhong.proto.YoustarProtoBase.MsgHeader, com.quhong.proto.YoustarProtoBase.MsgHeader.Builder, com.quhong.proto.YoustarProtoBase.MsgHeaderOrBuilder>(
                  getHeader(),
                  getParentForChildren(),
                  isClean());
          header_ = null;
        }
        return headerBuilder_;
      }

      private int oldScene_ ;
      /**
       * <pre>
       *旧场景
       * </pre>
       *
       * <code>int32 oldScene = 2;</code>
       */
      public int getOldScene() {
        return oldScene_;
      }
      /**
       * <pre>
       *旧场景
       * </pre>
       *
       * <code>int32 oldScene = 2;</code>
       */
      public Builder setOldScene(int value) {
        
        oldScene_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *旧场景
       * </pre>
       *
       * <code>int32 oldScene = 2;</code>
       */
      public Builder clearOldScene() {
        
        oldScene_ = 0;
        onChanged();
        return this;
      }

      private int newScene_ ;
      /**
       * <pre>
       *新场景
       * </pre>
       *
       * <code>int32 newScene = 3;</code>
       */
      public int getNewScene() {
        return newScene_;
      }
      /**
       * <pre>
       *新场景
       * </pre>
       *
       * <code>int32 newScene = 3;</code>
       */
      public Builder setNewScene(int value) {
        
        newScene_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *新场景
       * </pre>
       *
       * <code>int32 newScene = 3;</code>
       */
      public Builder clearNewScene() {
        
        newScene_ = 0;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ChangeScene)
    }

    // @@protoc_insertion_point(class_scope:ChangeScene)
    private static final com.quhong.proto.YoustarProtoBase.ChangeScene DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.quhong.proto.YoustarProtoBase.ChangeScene();
    }

    public static com.quhong.proto.YoustarProtoBase.ChangeScene getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ChangeScene>
        PARSER = new com.google.protobuf.AbstractParser<ChangeScene>() {
      public ChangeScene parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new ChangeScene(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ChangeScene> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChangeScene> getParserForType() {
      return PARSER;
    }

    public com.quhong.proto.YoustarProtoBase.ChangeScene getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Response)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgId = 1;</code>
     */
    int getMsgId();
  }
  /**
   * <pre>
   *统一响应
   * </pre>
   *
   * Protobuf type {@code Response}
   */
  public  static final class Response extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Response)
      ResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Response.newBuilder() to construct.
    private Response(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Response() {
      msgId_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Response(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {

              msgId_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.quhong.proto.YoustarProtoBase.internal_static_Response_descriptor;
    }

    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.quhong.proto.YoustarProtoBase.internal_static_Response_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.quhong.proto.YoustarProtoBase.Response.class, com.quhong.proto.YoustarProtoBase.Response.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgId_;
    /**
     * <code>int32 msgId = 1;</code>
     */
    public int getMsgId() {
      return msgId_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgId_ != 0) {
        output.writeInt32(1, msgId_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.quhong.proto.YoustarProtoBase.Response)) {
        return super.equals(obj);
      }
      com.quhong.proto.YoustarProtoBase.Response other = (com.quhong.proto.YoustarProtoBase.Response) obj;

      boolean result = true;
      result = result && (getMsgId()
          == other.getMsgId());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.quhong.proto.YoustarProtoBase.Response parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.Response parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.Response parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.Response parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.Response parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.quhong.proto.YoustarProtoBase.Response parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.Response parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.Response parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.Response parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.Response parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.quhong.proto.YoustarProtoBase.Response parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.quhong.proto.YoustarProtoBase.Response parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.quhong.proto.YoustarProtoBase.Response prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *统一响应
     * </pre>
     *
     * Protobuf type {@code Response}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Response)
        com.quhong.proto.YoustarProtoBase.ResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.quhong.proto.YoustarProtoBase.internal_static_Response_descriptor;
      }

      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.quhong.proto.YoustarProtoBase.internal_static_Response_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.quhong.proto.YoustarProtoBase.Response.class, com.quhong.proto.YoustarProtoBase.Response.Builder.class);
      }

      // Construct using com.quhong.proto.YoustarProtoBase.Response.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        msgId_ = 0;

        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.quhong.proto.YoustarProtoBase.internal_static_Response_descriptor;
      }

      public com.quhong.proto.YoustarProtoBase.Response getDefaultInstanceForType() {
        return com.quhong.proto.YoustarProtoBase.Response.getDefaultInstance();
      }

      public com.quhong.proto.YoustarProtoBase.Response build() {
        com.quhong.proto.YoustarProtoBase.Response result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public com.quhong.proto.YoustarProtoBase.Response buildPartial() {
        com.quhong.proto.YoustarProtoBase.Response result = new com.quhong.proto.YoustarProtoBase.Response(this);
        result.msgId_ = msgId_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.quhong.proto.YoustarProtoBase.Response) {
          return mergeFrom((com.quhong.proto.YoustarProtoBase.Response)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.quhong.proto.YoustarProtoBase.Response other) {
        if (other == com.quhong.proto.YoustarProtoBase.Response.getDefaultInstance()) return this;
        if (other.getMsgId() != 0) {
          setMsgId(other.getMsgId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.quhong.proto.YoustarProtoBase.Response parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.quhong.proto.YoustarProtoBase.Response) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int msgId_ ;
      /**
       * <code>int32 msgId = 1;</code>
       */
      public int getMsgId() {
        return msgId_;
      }
      /**
       * <code>int32 msgId = 1;</code>
       */
      public Builder setMsgId(int value) {
        
        msgId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgId = 1;</code>
       */
      public Builder clearMsgId() {
        
        msgId_ = 0;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Response)
    }

    // @@protoc_insertion_point(class_scope:Response)
    private static final com.quhong.proto.YoustarProtoBase.Response DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.quhong.proto.YoustarProtoBase.Response();
    }

    public static com.quhong.proto.YoustarProtoBase.Response getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Response>
        PARSER = new com.google.protobuf.AbstractParser<Response>() {
      public Response parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
          return new Response(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Response> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Response> getParserForType() {
      return PARSER;
    }

    public com.quhong.proto.YoustarProtoBase.Response getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_MsgHeader_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_MsgHeader_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_GateMsg_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_GateMsg_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ResponseAck_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ResponseAck_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Login_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Login_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_HeartBeat_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_HeartBeat_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_HeartAck_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_HeartAck_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ClientRecvAck_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ClientRecvAck_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ChangeScene_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ChangeScene_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Response_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Response_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\nbase.proto\"\211\001\n\tMsgHeader\022\r\n\005msgId\030\001 \001(" +
      "\003\022\017\n\007fromUid\030\002 \001(\t\022\r\n\005token\030\003 \001(\t\022\016\n\006roo" +
      "mID\030\004 \001(\t\022\023\n\013responseAck\030\005 \001(\010\022\023\n\013versio" +
      "nName\030\006 \001(\t\022\023\n\013requestTime\030\007 \001(\003\"%\n\007Gate" +
      "Msg\022\032\n\006header\030\001 \001(\0132\n.MsgHeader\"D\n\013Respo" +
      "nseAck\022\032\n\006header\030\001 \001(\0132\n.MsgHeader\022\014\n\004co" +
      "de\030\002 \001(\005\022\013\n\003msg\030\003 \001(\t\"\256\001\n\005Login\022\032\n\006heade" +
      "r\030\001 \001(\0132\n.MsgHeader\022\025\n\rclientVersion\030\002 \001" +
      "(\005\022\024\n\014clientSystem\030\003 \001(\005\022\020\n\010deviceId\030\004 \001" +
      "(\t\022\021\n\troomMsgId\030\005 \001(\003\022\022\n\nmicVersion\030\006 \001(",
      "\005\022\024\n\014musicVersion\030\007 \001(\005\022\r\n\005slang\030\010 \001(\005\"\217" +
      "\001\n\tHeartBeat\022\032\n\006header\030\001 \001(\0132\n.MsgHeader" +
      "\022\r\n\005scene\030\002 \001(\005\022\021\n\troomMsgId\030\003 \001(\003\022\022\n\nmi" +
      "cVersion\030\004 \001(\005\022\024\n\014musicVersion\030\005 \001(\005\022\r\n\005" +
      "slang\030\006 \001(\005\022\013\n\003vad\030\007 \001(\005\":\n\010HeartAck\022\032\n\006" +
      "header\030\001 \001(\0132\n.MsgHeader\022\022\n\nmicVersion\030\002" +
      " \001(\005\"G\n\rClientRecvAck\022\032\n\006header\030\001 \001(\0132\n." +
      "MsgHeader\022\r\n\005msgId\030\002 \001(\003\022\013\n\003cmd\030\003 \001(\005\"M\n" +
      "\013ChangeScene\022\032\n\006header\030\001 \001(\0132\n.MsgHeader" +
      "\022\020\n\010oldScene\030\002 \001(\005\022\020\n\010newScene\030\003 \001(\005\"\031\n\010",
      "Response\022\r\n\005msgId\030\001 \001(\005B$\n\020com.quhong.pr" +
      "otoB\020YoustarProtoBaseb\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_MsgHeader_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_MsgHeader_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_MsgHeader_descriptor,
        new java.lang.String[] { "MsgId", "FromUid", "Token", "RoomID", "ResponseAck", "VersionName", "RequestTime", });
    internal_static_GateMsg_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_GateMsg_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_GateMsg_descriptor,
        new java.lang.String[] { "Header", });
    internal_static_ResponseAck_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_ResponseAck_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ResponseAck_descriptor,
        new java.lang.String[] { "Header", "Code", "Msg", });
    internal_static_Login_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Login_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Login_descriptor,
        new java.lang.String[] { "Header", "ClientVersion", "ClientSystem", "DeviceId", "RoomMsgId", "MicVersion", "MusicVersion", "Slang", });
    internal_static_HeartBeat_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_HeartBeat_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_HeartBeat_descriptor,
        new java.lang.String[] { "Header", "Scene", "RoomMsgId", "MicVersion", "MusicVersion", "Slang", "Vad", });
    internal_static_HeartAck_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_HeartAck_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_HeartAck_descriptor,
        new java.lang.String[] { "Header", "MicVersion", });
    internal_static_ClientRecvAck_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_ClientRecvAck_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ClientRecvAck_descriptor,
        new java.lang.String[] { "Header", "MsgId", "Cmd", });
    internal_static_ChangeScene_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_ChangeScene_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ChangeScene_descriptor,
        new java.lang.String[] { "Header", "OldScene", "NewScene", });
    internal_static_Response_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_Response_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Response_descriptor,
        new java.lang.String[] { "MsgId", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
