package com.quhong.api;

import com.quhong.data.dto.MsgSendDTO;
import com.quhong.data.vo.SendMsgVO;
import com.quhong.exception.CommonException;
import com.quhong.exception.CommonH5Exception;
import org.apache.dubbo.rpc.filter.ExceptionFilter;


public interface MsgService {

    /**
     * @see ExceptionFilter
     */
    SendMsgVO sendMsg(MsgSendDTO reqData) throws CommonException, CommonH5Exception;

    String test(String param) throws CommonException, CommonH5Exception;

}
