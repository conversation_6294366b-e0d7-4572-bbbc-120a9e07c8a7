package com.quhong.config;

import com.quhong.core.utils.SpringUtils;
import com.quhong.monitor.MonitorSender;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.*;

public class ThreadPoolExecutorWithMonitor extends ThreadPoolExecutor {

    private static final Logger logger = LoggerFactory.getLogger(ThreadPoolExecutorWithMonitor.class);
    private boolean warning;
    private final int blockingQueueCapacity;
    private final ThreadLocal<Long> startTimeThreadLocal = new ThreadLocal<>();


    public ThreadPoolExecutorWithMonitor(int corePoolSize, int maximumPoolSize, long keepAliveTime,
                                         TimeUnit unit, int blockingQueueCapacity,
                                         ThreadFactory threadFactory, RejectedExecutionHandler handler) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, new LinkedBlockingQueue<>(blockingQueueCapacity), threadFactory, handler);
        this.blockingQueueCapacity = blockingQueueCapacity;
    }

    @Override
    public void shutdown() {
        logger.info("thread shutdown completedTasks={} activeCount={} waiting={}",
                this.getCompletedTaskCount(), this.getActiveCount(), this.getQueue().size());
        super.shutdown();
    }

    @Override
    public List<Runnable> shutdownNow() {
        logger.info("thread shutdownNow completedTasks={} activeCount={} waiting={}",
                this.getCompletedTaskCount(), this.getActiveCount(), this.getQueue().size());
        return super.shutdownNow();
    }

    @Override
    protected void beforeExecute(Thread t, Runnable r) {
        startTimeThreadLocal.set(System.currentTimeMillis());
    }

    @Override
    protected void afterExecute(Runnable r, Throwable t) {
        try {
            long costTime = System.currentTimeMillis() - startTimeThreadLocal.get();
            startTimeThreadLocal.remove();
            if (costTime > 32000L) {
                String detail = "消耗时间：" + costTime + "(ms) \n方法名:" + r.getClass().getName();
                warn("线程执行超时", detail);
            }
            blockCheck();
        } catch (Exception e) {
            logger.error("afterExecute check error. {}", e.getMessage(), e);
        }
    }

    private void blockCheck() {
        int size = this.getQueue().size();
        int checkValue = size == 0 ? Integer.MAX_VALUE : blockingQueueCapacity / size;
        // 阻塞达到队列的33%时告警
        if (checkValue <= 2) {
            if (!this.warning) {
                warn("线程池阻塞", "总队列数：" + blockingQueueCapacity + ", 当前等待数量：" + size);
                this.warning = true;
            }
        }
        // 低于5%时如果有 线程池阻塞 的告警，则停止告警
        if (checkValue > 20) {
            if (this.warning) {
                warn("线程池阻塞恢复正常", "总队列数：" + blockingQueueCapacity + ", 当前等待数量：" + size);
                this.warning = false;
            }
        }
    }

    private void warn(String desc, String detail) {
        try {
            logger.error("catch warn error. desc={} detail={}", desc, detail);
            MonitorSender sender = SpringUtils.getBean(MonitorSender.class);
            sender.info("ustar_java_exception", desc, detail);
        } catch (Exception e) {
            logger.error("monitor error. {}", e.getMessage(), e);
        }
    }
}
