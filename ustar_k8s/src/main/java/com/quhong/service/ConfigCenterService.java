package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.api.ConfigCenterApiService;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.data.dto.ConfigCenterDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Map;

@Lazy
@Service
public class ConfigCenterService {

    private static final Logger logger = LoggerFactory.getLogger(ConfigCenterService.class);
    @Autowired(required = false)
    private ConfigCenterApiService configCenterApiService;
    @Resource
    private ConfigCenterService configCenterService;

    @Cacheable(value = "getBackstageConfigMap", key = "T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')"
            , cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public Map<String, String> getBackstageConfigMap(String labelKey) {
        ConfigCenterDTO dto = new ConfigCenterDTO();
        dto.setLabelKey(labelKey);
        return configCenterApiService.getBackstageConfigMap(dto);
    }


    public <T> T getBeanByKey(String labelKey, String configKey, Class<T> entityClass) {
        try {
            T t = null;
            Map<String, String> map = configCenterService.getBackstageConfigMap(labelKey);
            String json = map.get(configKey);
            if (!ObjectUtils.isEmpty(json)) {
                t = JSON.parseObject(json, entityClass);
            }
            return t;
        } catch (Exception e) {
            logger.error("getBeanByKey. labelKey={} configKey={}", labelKey, configKey, e);
        }
        return null;
    }


    @Cacheable(value = "getBackstageConfigByConfigKey", key = "#p0.configKey +'-'+ #p0.payUser +'-'+ #p0.newUser +'-'+ #p0.os +'-'+ #p0.versioncode",
            cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public String getBackstageConfigByConfigKey(ConfigCenterDTO dto) {
        return configCenterApiService.getBackstageConfig(dto);
    }

    public <T> T getBackstageBeanByKey(ConfigCenterDTO dto, Class<T> entityClass) {
        try {
            T t = null;
            String obj = configCenterService.getBackstageConfigByConfigKey(dto);
            if (!ObjectUtils.isEmpty(obj)) {
                t = JSON.parseObject(obj, entityClass);
            }
            return t;
        } catch (Exception e) {
            logger.error("getBeanByKey. dto={} e={}", JSONObject.toJSONString(dto), e);
        }
        return null;
    }

}
