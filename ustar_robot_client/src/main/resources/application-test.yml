baseUrl: /robot_client/
robot:
  robotDomain: http://************:9902
  remoteDomain: https://test.qmovies.tv:8081
  tcpHost: ************
  tcpPort: 9001
server:
  port: 8080
logback:
  configurationFile: ./logback.xml
dubbo:
  application:
    name: ${spring.application.name}
    qos-enable: false
    metadata-service-port: 20885
  registry:
    address: kubernetes://DEFAULT_MASTER_HOST?registry-type=service&duplicate=false&trustCerts=true&namespace=devops
  protocol:
    name: dubbo
    port: 20880
  metadata-report:
    report-metadata: false
  service:
    shutdown:
      wait: 5000
