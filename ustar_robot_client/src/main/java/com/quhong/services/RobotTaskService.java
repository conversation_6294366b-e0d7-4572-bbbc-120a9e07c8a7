package com.quhong.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.quhong.core.timers.LoopTask;
import com.quhong.core.timers.SecondTask;
import com.quhong.core.timers.TimerService;
import com.quhong.core.web.HttpResponseData;
import com.quhong.data.RobotConfigData;
import com.quhong.executors.http.SystemHandler;
import com.quhong.http.HttpResult;
import com.quhong.services.task.RobotTask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class RobotTaskService {
    private static final Logger logger = LoggerFactory.getLogger(RobotTaskService.class);

    public static final String GRAB_BOX = "grabTask"; //抢红包
    public static final String TURN_TABLE_CREATE_TASK = "turnTableCreateTask"; //创建转盘任务
    public static final String TURN_TABLE_JOIN_TASK = "turnTableJoinTask"; //加入房间任务
    public static final String STRESS_TEST_TASK = "stressTestTask"; // 压力测试任务

    private static final int TICK_INTERVAL = 30 * 1000;

    @Autowired
    private SystemHandler systemHandler;
    @Autowired
    private RobotListService robotListService;
    private Map<String, RobotTask> taskMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void postInit() {
        TimerService.getService().addDelay(new LoopTask(null, TICK_INTERVAL) {
            @Override
            protected void execute() {
                reqConfigList(false);
            }
        });
        reqConfigList(true);
        TimerService.getService().addTicker(new SecondTask(null) {
            @Override
            protected void execute() {
                onTick();
            }
        });
    }

    private void reqConfigList(boolean init) {
        systemHandler.requestRobotConfig(data -> {
            updateConfigList(data, init);
        });
    }

    private void onTick() {
        for (RobotTask robotTask : taskMap.values()) {
            if (robotTask.isRunning()) {
                robotTask.onTick();
            }
        }
    }

    private void updateConfigList(HttpResponseData<HttpResult> data, boolean init) {
        if (data.isError()) {
            logger.error("update robot config list error. status={}", data.getStatus());
            return;
        }
        Map<String, RobotConfigData> configMap = new HashMap<>();
        JSONArray jsonArray = (JSONArray) data.getBody().getJSONData();
        for (Object json : jsonArray) {
            RobotConfigData configData = ((JSONObject) json).toJavaObject(RobotConfigData.class);
            if (StringUtils.isEmpty(configData.getConfigName())) {
                logger.error("configName is empty. configId={}", configData.getId());
                continue;
            }
            configMap.put(configData.getConfigName(), configData);
        }
        updateConfigData(configMap);
        if (init) {
            robotListService.reqRobotList(configMap.get(RobotTaskService.STRESS_TEST_TASK));
        }
    }

    private void updateConfigData(Map<String, RobotConfigData> configMap) {
        for (RobotTask robotTask : taskMap.values()) {
            RobotConfigData configData = configMap.get(robotTask.getTaskName());
            if (configData == null) {
                robotTask.stop();
            } else {
                robotTask.updateConfig(configData);
            }
        }
    }

    public void addRobotTask(RobotTask robotTask) {
        logger.info("add task. taskName={}", robotTask.getTaskName());
        taskMap.put(robotTask.getTaskName(), robotTask);
    }

    public RobotTask getRobotTask(String taskName) {
        return taskMap.get(taskName);
    }

    public Map<String, RobotTask> getTaskMap() {
        return taskMap;
    }

    public List<RobotTask> getTaskList() {
        List<RobotTask> robotTaskList = new ArrayList<>();
        for (RobotTask task : taskMap.values()) {
            if (!task.isRunning()) {
                continue;
            }
            robotTaskList.add(task);
        }
        return robotTaskList;
    }

}
