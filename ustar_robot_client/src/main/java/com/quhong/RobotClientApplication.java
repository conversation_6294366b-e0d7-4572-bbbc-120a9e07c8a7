package com.quhong;

import com.quhong.config.BaseAppConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.version.VersionReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchDataAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableAsync
@ServletComponentScan
@ImportResource("classpath*:spring-context.xml")
@ImportAutoConfiguration({BaseAppConfig.class})
@SpringBootApplication(exclude = {RedisAutoConfiguration.class, MongoAutoConfiguration.class,
        ElasticsearchDataAutoConfiguration.class, DataSourceAutoConfiguration.class})
public class RobotClientApplication {
    private static final Logger logger = LoggerFactory.getLogger(RobotClientApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(RobotClientApplication.class, args);
        logger.info("============= robot client {} v={} start ===============================", ServerConfig.getServerID(), VersionReader.read());
    }
}
