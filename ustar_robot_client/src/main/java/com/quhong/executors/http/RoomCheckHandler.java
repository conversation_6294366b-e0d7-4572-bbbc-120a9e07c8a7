package com.quhong.executors.http;

import com.quhong.chains.room.TurnTableCreateStrategy;
import com.quhong.chains.room.TurnTableJoinStrategy;
import com.quhong.data.HttpReqData;
import com.quhong.data.TurnTableData;
import com.quhong.enums.TurnTableStatus;
import com.quhong.msg.obj.LuckyWheelGameObject;
import com.quhong.robot.Robot;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
public class RoomCheckHandler  extends AbstractHttpHandler {
    private static final Logger logger = LoggerFactory.getLogger(RoomCheckHandler.class);


    public void checkAll(Robot robot){
        String roomId = robot.getRoomId();
        if(StringUtils.isEmpty(roomId)){
            logger.error("roomId is empty. can not check game. uid={}", robot.getUid());
            return;
        }
        CheckReq reqData = new CheckReq();
        reqData.copyFrom(robot);
        reqData.setRoom_id(robot.getRoomId());
        request(robot, "c3/v2/check_all", reqData, data -> {
            if(data.isError()){
                logger.info("check game error. status={} roomId={} uid={}", data.getStatus(), roomId, robot.getUid());
                return;
            }
            logger.info("check game successful. roomId={} uid={}", roomId, robot.getUid());
            CheckRsp checkRsp = data.getBody().getJSONData().toJavaObject(CheckRsp.class);
            TurnTableData turnTableData = null;
            if(checkRsp.getTurntable_game() != null){
                turnTableData = new TurnTableData();
                LuckyWheelGameObject gameObject = checkRsp.getTurntable_game().getGame();
                turnTableData.copyFrom(robot.getUid(), gameObject);
            }
            TurnTableCreateStrategy strategy = (TurnTableCreateStrategy)robot.getStrategy(TurnTableCreateStrategy.class);
            if(strategy != null){
                strategy.setTurnTableData(turnTableData);
            }
            TurnTableJoinStrategy joinStrategy = (TurnTableJoinStrategy)robot.getStrategy(TurnTableJoinStrategy.class);
            if(joinStrategy != null){
                joinStrategy.setTurnTableData(turnTableData);
            }
        });
    }

    public static class CheckReq extends HttpReqData{
        private String room_id;

        public CheckReq(){

        }

        public String getRoom_id() {
            return room_id;
        }

        public void setRoom_id(String room_id) {
            this.room_id = room_id;
        }
    }

    public static class CheckRsp{
        private TurnTableGameObject turntable_game;
        private int whell_leftnums; //当日剩余次数
        private int type; // 2 转盘, 3 真心话大冒险
        private Object c3_game;

        public CheckRsp(){

        }

        public TurnTableGameObject getTurntable_game() {
            return turntable_game;
        }

        public void setTurntable_game(TurnTableGameObject turntable_game) {
            this.turntable_game = turntable_game;
        }

        public int getWhell_leftnums() {
            return whell_leftnums;
        }

        public void setWhell_leftnums(int whell_leftnums) {
            this.whell_leftnums = whell_leftnums;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public Object getC3_game() {
            return c3_game;
        }

        public void setC3_game(Object c3_game) {
            this.c3_game = c3_game;
        }
    }

    public static class TurnTableGameObject{
        private LuckyWheelGameObject game;

        public LuckyWheelGameObject getGame() {
            return game;
        }

        public void setGame(LuckyWheelGameObject game) {
            this.game = game;
        }
    }
}
