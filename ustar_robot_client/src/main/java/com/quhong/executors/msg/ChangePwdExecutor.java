package com.quhong.executors.msg;

import com.quhong.core.annotation.MsgExecutor;
import com.quhong.core.msg.Msg;
import com.quhong.core.net.connect.IConnector;
import com.quhong.enums.Cmd;
import com.quhong.executors.http.LeaveRoomHandler;
import com.quhong.msg.room.RoomChangePwdPushMsg;
import com.quhong.robot.Robot;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

@MsgExecutor
public class ChangePwdExecutor extends RobotMsgExecutor<RoomChangePwdPushMsg> {
    private static final Logger logger = LoggerFactory.getLogger(ChangePwdExecutor.class);

    @Autowired
    private LeaveRoomHandler leaveRoomHandler;

    public ChangePwdExecutor() {
        super(Cmd.ROOM_CHANGE_PWD_PUSH);
    }

    @Override
    protected void doExecute(Robot robot, RoomChangePwdPushMsg msg) {
        if(!msg.getRoomId().equals(robot.getRoomId())){
            logger.error("change pwd. robot leave room error. leaveRoomId={} roomId={} uid={}", msg.getRoomId(), robot.getRoomId(), robot.getUid());
            return;
        }
        logger.info("change pwd. robot leave room. roomId={} uid={}", robot.getRoomId(), robot.getUid());
        leaveRoomHandler.leaveRequest(robot);
    }
}
