package com.quhong.data;

public class ApiParam {
    // 进入房间持续时间（秒）
    private int enterRoomSec = -1;
    // 进入数量
    private double enterRoomCount = -1;

    // 离开房间持续时间（秒）
    private int leaveRoomSec = -1;
    // 离开数量
    private double leaveRoomCount = -1;

    // 发送消息持续时间（秒）
    private int sendMsgSec = -1;
    // 发送消息数量
    private double sendMsgCount = -1;
    // 发送消息类型 1文本 2图片
    private int sendMsgType = -1;

    // 发送礼物持续时间（秒）
    private int sendGiftSec = -1;
    // 发送礼物次数
    private double sendGiftCount = -1;
    // 发送普通礼物时单次发送礼物数量，0随机、1、7、17、77、99、666、999、9999
    private int sendGiftNum = -1;
    // 发送的礼物id
    private int sendGiftId = -1;
    // 0随机 1发送给麦位单人 2发送给麦位多人 3发送给所有麦位用户 4发送给全房间用户
    private int sendTo = -1;

    public int getEnterRoomSec() {
        return enterRoomSec;
    }

    public void setEnterRoomSec(int enterRoomSec) {
        this.enterRoomSec = enterRoomSec;
    }

    public double getEnterRoomCount() {
        return enterRoomCount;
    }

    public void setEnterRoomCount(double enterRoomCount) {
        this.enterRoomCount = enterRoomCount;
    }

    public int getLeaveRoomSec() {
        return leaveRoomSec;
    }

    public void setLeaveRoomSec(int leaveRoomSec) {
        this.leaveRoomSec = leaveRoomSec;
    }

    public double getLeaveRoomCount() {
        return leaveRoomCount;
    }

    public void setLeaveRoomCount(double leaveRoomCount) {
        this.leaveRoomCount = leaveRoomCount;
    }

    public int getSendMsgSec() {
        return sendMsgSec;
    }

    public void setSendMsgSec(int sendMsgSec) {
        this.sendMsgSec = sendMsgSec;
    }

    public double getSendMsgCount() {
        return sendMsgCount;
    }

    public void setSendMsgCount(double sendMsgCount) {
        this.sendMsgCount = sendMsgCount;
    }

    public int getSendMsgType() {
        return sendMsgType;
    }

    public void setSendMsgType(int sendMsgType) {
        this.sendMsgType = sendMsgType;
    }

    public int getSendGiftSec() {
        return sendGiftSec;
    }

    public void setSendGiftSec(int sendGiftSec) {
        this.sendGiftSec = sendGiftSec;
    }

    public double getSendGiftCount() {
        return sendGiftCount;
    }

    public void setSendGiftCount(double sendGiftCount) {
        this.sendGiftCount = sendGiftCount;
    }

    public int getSendGiftNum() {
        return sendGiftNum;
    }

    public void setSendGiftNum(int sendGiftNum) {
        this.sendGiftNum = sendGiftNum;
    }

    public int getSendGiftId() {
        return sendGiftId;
    }

    public void setSendGiftId(int sendGiftId) {
        this.sendGiftId = sendGiftId;
    }

    public int getSendTo() {
        return sendTo;
    }

    public void setSendTo(int sendTo) {
        this.sendTo = sendTo;
    }
}
