package com.quhong.feign;

import com.alibaba.fastjson.JSONObject;
import com.quhong.data.dto.TruthDareV2DTO;
import com.quhong.data.vo.TruthDareV2GameVO;
import com.quhong.datas.HttpResult;
import com.quhong.dto.InnerSudGameDTO;
import com.quhong.vo.InnerSudGameVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * 仅k8s服务可用
 */
@FeignClient(name = "ustar-java-game", url = "ustar-java-game:8080", fallback = IGameService.DefaultFallback.class)
public interface IGameService {

    /**
     * 游戏逃跑
     */
    @PostMapping("/inner/game/escape_game")
    HttpResult<Object> escapeGame(@RequestParam(value = "uid") String uid,
                                  @RequestParam(value = "roomId") String roomId,
                                  @RequestParam(value = "gameId") String gameId);


    /**
     * 在游戏房间 快速创建或者加入游戏
     */
    @PostMapping("/inner/game/createOrJoin")
    HttpResult<InnerSudGameVO> createOrJoin(InnerSudGameDTO dto);

    /**
     * 在游戏房间 退出房间
     */
    @PostMapping("/inner/game/quiteGame")
    HttpResult<InnerSudGameVO> quiteGame(InnerSudGameDTO dto);

    /**
     * 真心话大冒险退出游戏
     */
    @PostMapping("/inner/game/truthDareCloseGame")
    HttpResult<TruthDareV2GameVO> truthDareCloseGame(TruthDareV2DTO dto);

    @Component
    class DefaultFallback implements IGameService {
        private static final Logger logger = LoggerFactory.getLogger(IFriendService.class);

        @Override
        public HttpResult<Object> escapeGame(String uid, String roomId, String gameId) {
            logger.error("escapeGame error uid={} gameId={}", uid, gameId);
            return HttpResult.getError();
        }

        @Override
        public HttpResult<InnerSudGameVO> createOrJoin(InnerSudGameDTO dto) {
            logger.error("createOrJoin error dto={}", JSONObject.toJSONString(dto));
            return HttpResult.getError();
        }

        @Override
        public HttpResult<InnerSudGameVO> quiteGame(InnerSudGameDTO dto) {
            logger.error("quiteGame error dto={}", JSONObject.toJSONString(dto));
            return HttpResult.getError();
        }

        @Override
        public HttpResult<TruthDareV2GameVO> truthDareCloseGame(TruthDareV2DTO dto) {
            logger.error("truthDareCloseGame error dto={}", JSONObject.toJSONString(dto));
            return HttpResult.getError();
        }
    }
}
