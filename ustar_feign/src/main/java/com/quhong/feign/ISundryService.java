package com.quhong.feign;

import com.alibaba.fastjson.JSON;
import com.quhong.data.dto.IsLimitUserDTO;
import com.quhong.data.dto.QueryCountryByIpDTO;
import com.quhong.data.vo.IsNotLimitUseVO;
import com.quhong.data.vo.QueryCountryVO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;


/**
 * 仅k8s服务可用
 */
@FeignClient(name = "ustar-java-sundry", url = "ustar-java-sundry:8080", fallback = ISundryService.DefaultFallback.class)
public interface ISundryService {

    @PostMapping("/inner/sundry/queryCountryByIp")
    ApiResult<QueryCountryVO> queryCountryByIp(QueryCountryByIpDTO dto);

    @PostMapping("/inner/sundry/queryCityDataByIp")
    ApiResult<QueryCountryVO> queryCityDataByIp(QueryCountryByIpDTO dto);

    @PostMapping("/inner/sundry/isNotLimitUse")
    ApiResult<IsNotLimitUseVO> isNotLimitUse(IsLimitUserDTO dto);

    @Component
    class DefaultFallback implements ISundryService {
        private static final Logger logger = LoggerFactory.getLogger(ISundryService.class);

        @Override
        public ApiResult<QueryCountryVO> queryCountryByIp(QueryCountryByIpDTO dto) {
            logger.error("queryCountryByIp error dto={}", JSON.toJSONString(dto));
            return ApiResult.getError(HttpCode.SERVER_ERROR);
        }

        @Override
        public ApiResult<QueryCountryVO> queryCityDataByIp(QueryCountryByIpDTO dto) {
            logger.error("queryCityDataByIp error dto={}", JSON.toJSONString(dto));
            return ApiResult.getError(HttpCode.SERVER_ERROR);
        }

        @Override
        public ApiResult<IsNotLimitUseVO> isNotLimitUse(IsLimitUserDTO dto) {
            logger.error("isNotLimitUse error dto={}", JSON.toJSONString(dto));
            return ApiResult.getError(HttpCode.SERVER_ERROR);
        }
    }
}
