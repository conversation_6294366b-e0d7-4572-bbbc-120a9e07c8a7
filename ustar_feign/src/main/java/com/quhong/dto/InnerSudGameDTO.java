package com.quhong.dto;

import com.alibaba.fastjson.JSONObject;
import com.quhong.handler.HttpEnvData;

/**
 * <AUTHOR>
 * @date 2022/7/1
 */
public class InnerSudGameDTO extends HttpEnvData {

    /**
     * 游戏id
     */
    private String gameId;

    /**
     * 入场费id
     */
    private int currencyId;

    /**
     * 游戏币类型 1 金币 2 钻石
     */
    private int currencyType;

    private int currency;

    /**
     * 游戏类型 1碰碰我最强 2Ludo 3UMO 4Monster Crush 5Domino 6Carrom Pool
     */
    private int gameType;

    /**
     * 游戏人数上限
     */
    private int playerNumber;

    /**
     * 某些游戏（Carrom Pool）使用mode_ex字段配置，将mode_ex字段赋值到rule中
     * <a href="https://github.com/SudTechnology/sud-mgp-doc/blob/master/zh-CN/app/Server/ServerSDKAPI/PushEventData/GameSettingReqData.md">...</a>
     * <p>
     * "rule": {
     * "mode": 0, // 0快速，1经典
     * "chessNum": 2, // 2对应2颗棋子，4对应4颗棋子
     * "item": 1 // 1要，0不要
     * }
     */
    private JSONObject rule;

    private Integer index; // 不为空时，加入游戏时的队伍顺序

    private String aid;

    //进房间类型 1 创建进房（上麦+加入游戏） 2匹配进房（上麦+加入游戏） 3 邀请进房（上麦+不加入游戏）
    private int enterRoomType;

    public String getGameId() {
        return gameId;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public int getCurrencyType() {
        return currencyType;
    }

    public void setCurrencyType(int currencyType) {
        this.currencyType = currencyType;
    }

    public int getCurrency() {
        return currency;
    }

    public void setCurrency(int currency) {
        this.currency = currency;
    }

    public int getGameType() {
        return gameType;
    }

    public void setGameType(int gameType) {
        this.gameType = gameType;
    }

    public int getPlayerNumber() {
        return playerNumber;
    }

    public void setPlayerNumber(int playerNumber) {
        this.playerNumber = playerNumber;
    }

    public JSONObject getRule() {
        return rule;
    }

    public void setRule(JSONObject rule) {
        this.rule = rule;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public int getEnterRoomType() {
        return enterRoomType;
    }

    public void setEnterRoomType(int enterRoomType) {
        this.enterRoomType = enterRoomType;
    }
}
