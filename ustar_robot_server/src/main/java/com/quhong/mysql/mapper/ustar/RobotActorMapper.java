package com.quhong.mysql.mapper.ustar;

import com.quhong.data.RobotActorData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface RobotActorMapper {
    @Select("select count(1) from t_robot_actor where status > 0")
    int getCount();

    @Select("select * from t_robot_actor where status > 0 limit #{startIndex},#{pageSize}")
    List<RobotActorData> getList(@Param("startIndex") int startIndex, @Param("pageSize") int pageSize);

    @Select("select * from t_robot_actor where status > 0")
    List<RobotActorData> getAll();

    @Select("select * from t_robot_actor where id=#{id}")
    RobotActorData getById(@Param("id") int id);
}
